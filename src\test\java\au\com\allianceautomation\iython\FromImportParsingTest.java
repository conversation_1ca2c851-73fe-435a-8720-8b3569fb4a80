package au.com.allianceautomation.iython;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

import au.com.allianceautomation.iython.ast.Program;
import au.com.allianceautomation.iython.ast.statements.ImportStatement;
import au.com.allianceautomation.iython.ast.statements.Statement;

/**
 * Test class for from import statement parsing.
 */
public class FromImportParsingTest {
    
    @Test
    public void testFromImportStatementParsing() {
        // Test that from import statements are parsed correctly
        PythonExecutor executor = new PythonExecutor();
        
        try {
            // Use reflection to access the parseCode method
            java.lang.reflect.Method parseMethod = PythonExecutor.class.getDeclaredMethod("parseCode", String.class);
            parseMethod.setAccessible(true);
            
            Program program = (Program) parseMethod.invoke(executor, "from test_module import hello");
            
            assertNotNull(program, "Program should not be null");
            assertTrue(program.getStatements().size() > 0, "Program should have statements");
            
            Statement firstStatement = program.getStatements().get(0);
            System.out.println("First statement type: " + firstStatement.getClass().getName());
            
            if (firstStatement instanceof ImportStatement) {
                ImportStatement importStmt = (ImportStatement) firstStatement;
                System.out.println("Is from import: " + importStmt.isFromImport());
                System.out.println("From module: " + importStmt.getFromModule());
                System.out.println("Import names: " + importStmt.getModuleNames());
                
                assertTrue(importStmt.isFromImport(), "Should be a from import");
                assertEquals("test_module", importStmt.getFromModule());
                assertEquals("hello", importStmt.getModuleNames().get(0));
            } else {
                System.out.println("Statement is not an ImportStatement, it's: " + firstStatement.getClass().getSimpleName());
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Failed to test from import parsing", e);
        }
    }
}
