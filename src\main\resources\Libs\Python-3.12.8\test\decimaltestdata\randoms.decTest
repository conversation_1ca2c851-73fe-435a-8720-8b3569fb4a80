------------------------------------------------------------------------
-- randoms.decTest -- decimal random testcases                        --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

extended:    1
maxexponent: 999999999
minexponent: -999999999
precision:   9
rounding:    half_up

-- Randomly generated testcases [31 Dec 2000, with results defined for
-- all cases [27 Oct 2001], and no trim/finish [9 Jun 2002]
xadd001 add 905.67402 -202896611.E-780472620 -> 905.674020 Inexact Rounded
xcom001 compare 905.67402 -202896611.E-780472620 -> 1
xdiv001 divide 905.67402 -202896611.E-780472620 -> -4.46372177E+780472614 Inexact Rounded
xdvi001 divideint 905.67402 -202896611.E-780472620 -> NaN Division_impossible
xmul001 multiply 905.67402 -202896611.E-780472620 -> -1.83758189E-780472609 Inexact Rounded
xpow001 power 905.67402 -2 -> 0.00000121914730 Inexact Rounded
xrem001 remainder 905.67402 -202896611.E-780472620 -> NaN Division_impossible
xsub001 subtract 905.67402 -202896611.E-780472620 -> 905.674020 Inexact Rounded
xadd002 add 3915134.7 -597164907. -> -593249772 Inexact Rounded
xcom002 compare 3915134.7 -597164907. -> 1
xdiv002 divide 3915134.7 -597164907. -> -0.00655620358 Inexact Rounded
xdvi002 divideint 3915134.7 -597164907. -> -0
xmul002 multiply 3915134.7 -597164907. -> -2.33798105E+15 Inexact Rounded
xpow002 power 3915134.7 -597164907 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem002 remainder 3915134.7 -597164907. -> 3915134.7
xsub002 subtract 3915134.7 -597164907. -> 601080042 Inexact Rounded
xadd003 add 309759261 62663.487 -> 309821924 Inexact Rounded
xcom003 compare 309759261 62663.487 -> 1
xdiv003 divide 309759261 62663.487 -> 4943.21775 Inexact Rounded
xdvi003 divideint 309759261 62663.487 -> 4943
xmul003 multiply 309759261 62663.487 -> 1.94105954E+13 Inexact Rounded
xpow003 power 309759261 62663 -> 1.13679199E+532073 Inexact Rounded
xrem003 remainder 309759261 62663.487 -> 13644.759
xsub003 subtract 309759261 62663.487 -> 309696598 Inexact Rounded
xadd004 add 3.93591888E-236595626 7242375.00 -> 7242375.00 Inexact Rounded
xcom004 compare 3.93591888E-236595626 7242375.00 -> -1
xdiv004 divide 3.93591888E-236595626 7242375.00 -> 5.43456930E-236595633 Inexact Rounded
xdvi004 divideint 3.93591888E-236595626 7242375.00 -> 0
xmul004 multiply 3.93591888E-236595626 7242375.00 -> 2.85054005E-236595619 Inexact Rounded
xpow004 power 3.93591888E-236595626 7242375 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem004 remainder 3.93591888E-236595626 7242375.00 -> 3.93591888E-236595626
xsub004 subtract 3.93591888E-236595626 7242375.00 -> -7242375.00 Inexact Rounded
xadd005 add 323902.714 -608669.607E-657060568 -> 323902.714 Inexact Rounded
xcom005 compare 323902.714 -608669.607E-657060568 -> 1
xdiv005 divide 323902.714 -608669.607E-657060568 -> -5.32148657E+657060567 Inexact Rounded
xdvi005 divideint 323902.714 -608669.607E-657060568 -> NaN Division_impossible
xmul005 multiply 323902.714 -608669.607E-657060568 -> -1.97149738E-657060557 Inexact Rounded
xpow005 power 323902.714 -6 -> 8.65989204E-34 Inexact Rounded
xrem005 remainder 323902.714 -608669.607E-657060568 -> NaN Division_impossible
xsub005 subtract 323902.714 -608669.607E-657060568 -> 323902.714 Inexact Rounded
xadd006 add 5.11970092 -8807.22036 -> -8802.10066 Inexact Rounded
xcom006 compare 5.11970092 -8807.22036 -> 1
xdiv006 divide 5.11970092 -8807.22036 -> -0.000581307236 Inexact Rounded
xdvi006 divideint 5.11970092 -8807.22036 -> -0
xmul006 multiply 5.11970092 -8807.22036 -> -45090.3342 Inexact Rounded
xpow006 power 5.11970092 -8807 -> 4.81819262E-6247 Inexact Rounded
xrem006 remainder 5.11970092 -8807.22036 -> 5.11970092
xsub006 subtract 5.11970092 -8807.22036 -> 8812.34006 Inexact Rounded
xadd007 add -7.99874516 4561.83758 -> 4553.83883 Inexact Rounded
xcom007 compare -7.99874516 4561.83758 -> -1
xdiv007 divide -7.99874516 4561.83758 -> -0.00175340420 Inexact Rounded
xdvi007 divideint -7.99874516 4561.83758 -> -0
xmul007 multiply -7.99874516 4561.83758 -> -36488.9763 Inexact Rounded
xpow007 power -7.99874516 4562 -> 3.85236199E+4119 Inexact Rounded
xrem007 remainder -7.99874516 4561.83758 -> -7.99874516
xsub007 subtract -7.99874516 4561.83758 -> -4569.83633 Inexact Rounded
xadd008 add 297802878 -927206.324 -> 296875672 Inexact Rounded
xcom008 compare 297802878 -927206.324 -> 1
xdiv008 divide 297802878 -927206.324 -> -321.182967 Inexact Rounded
xdvi008 divideint 297802878 -927206.324 -> -321
xmul008 multiply 297802878 -927206.324 -> -2.76124712E+14 Inexact Rounded
xpow008 power 297802878 -927206 -> 1.94602810E-7857078 Inexact Rounded
xrem008 remainder 297802878 -927206.324 -> 169647.996
xsub008 subtract 297802878 -927206.324 -> 298730084 Inexact Rounded
xadd009 add -766.651824 31300.3619 -> 30533.7101 Inexact Rounded
xcom009 compare -766.651824 31300.3619 -> -1
xdiv009 divide -766.651824 31300.3619 -> -0.0244933853 Inexact Rounded
xdvi009 divideint -766.651824 31300.3619 -> -0
xmul009 multiply -766.651824 31300.3619 -> -23996479.5 Inexact Rounded
xpow009 power -766.651824 31300 -> 8.37189011E+90287 Inexact Rounded
xrem009 remainder -766.651824 31300.3619 -> -766.651824
xsub009 subtract -766.651824 31300.3619 -> -32067.0137 Inexact Rounded
xadd010 add -56746.8689E+934981942 471002521. -> -5.67468689E+934981946 Inexact Rounded
xcom010 compare -56746.8689E+934981942 471002521. -> -1
xdiv010 divide -56746.8689E+934981942 471002521. -> -1.20481030E+934981938 Inexact Rounded
xdvi010 divideint -56746.8689E+934981942 471002521. -> NaN Division_impossible
xmul010 multiply -56746.8689E+934981942 471002521. -> -2.67279183E+934981955 Inexact Rounded
xpow010 power -56746.8689E+934981942 471002521 -> -Infinity Overflow Inexact Rounded
xrem010 remainder -56746.8689E+934981942 471002521. -> NaN Division_impossible
xsub010 subtract -56746.8689E+934981942 471002521. -> -5.67468689E+934981946 Inexact Rounded
xadd011 add 456417160 -41346.1024 -> 456375814 Inexact Rounded
xcom011 compare 456417160 -41346.1024 -> 1
xdiv011 divide 456417160 -41346.1024 -> -11038.9404 Inexact Rounded
xdvi011 divideint 456417160 -41346.1024 -> -11038
xmul011 multiply 456417160 -41346.1024 -> -1.88710706E+13 Inexact Rounded
xpow011 power 456417160 -41346 -> 1.04766863E-358030 Inexact Rounded
xrem011 remainder 456417160 -41346.1024 -> 38881.7088
xsub011 subtract 456417160 -41346.1024 -> 456458506 Inexact Rounded
xadd012 add 102895.722 -2.62214826 -> 102893.100 Inexact Rounded
xcom012 compare 102895.722 -2.62214826 -> 1
xdiv012 divide 102895.722 -2.62214826 -> -39241.0008 Inexact Rounded
xdvi012 divideint 102895.722 -2.62214826 -> -39241
xmul012 multiply 102895.722 -2.62214826 -> -269807.838 Inexact Rounded
xpow012 power 102895.722 -3 -> 9.17926786E-16 Inexact Rounded
xrem012 remainder 102895.722 -2.62214826 -> 0.00212934
xsub012 subtract 102895.722 -2.62214826 -> 102898.344 Inexact Rounded
xadd013 add 61.3033331E+157644141 -567740.918E-893439456 -> 6.13033331E+157644142 Inexact Rounded
xcom013 compare 61.3033331E+157644141 -567740.918E-893439456 -> 1
xdiv013 divide 61.3033331E+157644141 -567740.918E-893439456 -> -Infinity Inexact Overflow Rounded
xdvi013 divideint 61.3033331E+157644141 -567740.918E-893439456 -> NaN Division_impossible
xmul013 multiply 61.3033331E+157644141 -567740.918E-893439456 -> -3.48044106E-735795308 Inexact Rounded
xpow013 power 61.3033331E+157644141 -6 -> 1.88406322E-945864857 Inexact Rounded
xrem013 remainder 61.3033331E+157644141 -567740.918E-893439456 -> NaN Division_impossible
xsub013 subtract 61.3033331E+157644141 -567740.918E-893439456 -> 6.13033331E+157644142 Inexact Rounded
xadd014 add 80223.3897 73921.0383E-467772675 -> 80223.3897 Inexact Rounded
xcom014 compare 80223.3897 73921.0383E-467772675 -> 1
xdiv014 divide 80223.3897 73921.0383E-467772675 -> 1.08525789E+467772675 Inexact Rounded
xdvi014 divideint 80223.3897 73921.0383E-467772675 -> NaN Division_impossible
xmul014 multiply 80223.3897 73921.0383E-467772675 -> 5.93019626E-467772666 Inexact Rounded
xpow014 power 80223.3897 7 -> 2.13848919E+34 Inexact Rounded
xrem014 remainder 80223.3897 73921.0383E-467772675 -> NaN Division_impossible
xsub014 subtract 80223.3897 73921.0383E-467772675 -> 80223.3897 Inexact Rounded
xadd015 add -654645.954 -9.12535752 -> -654655.079 Inexact Rounded
xcom015 compare -654645.954 -9.12535752 -> -1
xdiv015 divide -654645.954 -9.12535752 -> 71739.2116 Inexact Rounded
xdvi015 divideint -654645.954 -9.12535752 -> 71739
xmul015 multiply -654645.954 -9.12535752 -> 5973878.38 Inexact Rounded
xpow015 power -654645.954 -9 -> -4.52836690E-53 Inexact Rounded
xrem015 remainder -654645.954 -9.12535752 -> -1.93087272
xsub015 subtract -654645.954 -9.12535752 -> -654636.829 Inexact Rounded
xadd016 add 63.1917772E-706014634 -7.56253257E-138579234 -> -7.56253257E-138579234 Inexact Rounded
xcom016 compare 63.1917772E-706014634 -7.56253257E-138579234 -> 1
xdiv016 divide 63.1917772E-706014634 -7.56253257E-138579234 -> -8.35590149E-567435400 Inexact Rounded
xdvi016 divideint 63.1917772E-706014634 -7.56253257E-138579234 -> -0
xmul016 multiply 63.1917772E-706014634 -7.56253257E-138579234 -> -4.77889873E-844593866 Inexact Rounded
xpow016 power 63.1917772E-706014634 -8 -> Infinity Overflow Inexact Rounded
xrem016 remainder 63.1917772E-706014634 -7.56253257E-138579234 -> 6.31917772E-706014633
xsub016 subtract 63.1917772E-706014634 -7.56253257E-138579234 -> 7.56253257E-138579234 Inexact Rounded
xadd017 add -39674.7190 2490607.78 -> 2450933.06 Inexact Rounded
xcom017 compare -39674.7190 2490607.78 -> -1
xdiv017 divide -39674.7190 2490607.78 -> -0.0159297338 Inexact Rounded
xdvi017 divideint -39674.7190 2490607.78 -> -0
xmul017 multiply -39674.7190 2490607.78 -> -9.88141638E+10 Inexact Rounded
xpow017 power -39674.7190 2490608 -> 2.55032329E+11453095 Inexact Rounded
xrem017 remainder -39674.7190 2490607.78 -> -39674.7190
xsub017 subtract -39674.7190 2490607.78 -> -2530282.50 Inexact Rounded
xadd018 add -3364.59737E-600363681 896487.451 -> 896487.451 Inexact Rounded
xcom018 compare -3364.59737E-600363681 896487.451 -> -1
xdiv018 divide -3364.59737E-600363681 896487.451 -> -3.75308920E-600363684 Inexact Rounded
xdvi018 divideint -3364.59737E-600363681 896487.451 -> -0
xmul018 multiply -3364.59737E-600363681 896487.451 -> -3.01631932E-600363672 Inexact Rounded
xpow018 power -3364.59737E-600363681 896487 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem018 remainder -3364.59737E-600363681 896487.451 -> -3.36459737E-600363678
xsub018 subtract -3364.59737E-600363681 896487.451 -> -896487.451 Inexact Rounded
xadd019 add -64138.0578 31759011.3E+697488342 -> 3.17590113E+697488349 Inexact Rounded
xcom019 compare -64138.0578 31759011.3E+697488342 -> -1
xdiv019 divide -64138.0578 31759011.3E+697488342 -> -2.01952313E-697488345 Inexact Rounded
xdvi019 divideint -64138.0578 31759011.3E+697488342 -> -0
xmul019 multiply -64138.0578 31759011.3E+697488342 -> -2.03696130E+697488354 Inexact Rounded
xpow019 power -64138.0578 3 -> -2.63844116E+14 Inexact Rounded
xrem019 remainder -64138.0578 31759011.3E+697488342 -> -64138.0578
xsub019 subtract -64138.0578 31759011.3E+697488342 -> -3.17590113E+697488349 Inexact Rounded
xadd020 add 61399.8527 -64344484.5 -> -64283084.6 Inexact Rounded
xcom020 compare 61399.8527 -64344484.5 -> 1
xdiv020 divide 61399.8527 -64344484.5 -> -0.000954236454 Inexact Rounded
xdvi020 divideint 61399.8527 -64344484.5 -> -0
xmul020 multiply 61399.8527 -64344484.5 -> -3.95074187E+12 Inexact Rounded
xpow020 power 61399.8527 -64344485 -> 1.27378842E-308092161 Inexact Rounded
xrem020 remainder 61399.8527 -64344484.5 -> 61399.8527
xsub020 subtract 61399.8527 -64344484.5 -> 64405884.4 Inexact Rounded
xadd021 add -722960.204 -26154599.8 -> -26877560.0 Inexact Rounded
xcom021 compare -722960.204 -26154599.8 -> 1
xdiv021 divide -722960.204 -26154599.8 -> 0.0276417995 Inexact Rounded
xdvi021 divideint -722960.204 -26154599.8 -> 0
xmul021 multiply -722960.204 -26154599.8 -> 1.89087348E+13 Inexact Rounded
xpow021 power -722960.204 -26154600 -> 5.34236139E-153242794 Inexact Rounded
xrem021 remainder -722960.204 -26154599.8 -> -722960.204
xsub021 subtract -722960.204 -26154599.8 -> 25431639.6 Inexact Rounded
xadd022 add 9.47109959E+230565093 73354723.2 -> 9.47109959E+230565093 Inexact Rounded
xcom022 compare 9.47109959E+230565093 73354723.2 -> 1
xdiv022 divide 9.47109959E+230565093 73354723.2 -> 1.29113698E+230565086 Inexact Rounded
xdvi022 divideint 9.47109959E+230565093 73354723.2 -> NaN Division_impossible
xmul022 multiply 9.47109959E+230565093 73354723.2 -> 6.94749889E+230565101 Inexact Rounded
xpow022 power 9.47109959E+230565093 73354723 -> Infinity Overflow Inexact Rounded
xrem022 remainder 9.47109959E+230565093 73354723.2 -> NaN Division_impossible
xsub022 subtract 9.47109959E+230565093 73354723.2 -> 9.47109959E+230565093 Inexact Rounded
xadd023 add 43.7456245 547441956. -> 547442000 Inexact Rounded
xcom023 compare 43.7456245 547441956. -> -1
xdiv023 divide 43.7456245 547441956. -> 7.99091557E-8 Inexact Rounded
xdvi023 divideint 43.7456245 547441956. -> 0
xmul023 multiply 43.7456245 547441956. -> 2.39481902E+10 Inexact Rounded
xpow023 power 43.7456245 547441956 -> 2.91742391E+898316458 Inexact Rounded
xrem023 remainder 43.7456245 547441956. -> 43.7456245
xsub023 subtract 43.7456245 547441956. -> -547441912 Inexact Rounded
xadd024 add -73150542E-242017390 -8.15869954 -> -8.15869954 Inexact Rounded
xcom024 compare -73150542E-242017390 -8.15869954 -> 1
xdiv024 divide -73150542E-242017390 -8.15869954 -> 8.96595611E-242017384 Inexact Rounded
xdvi024 divideint -73150542E-242017390 -8.15869954 -> 0
xmul024 multiply -73150542E-242017390 -8.15869954 -> 5.96813293E-242017382 Inexact Rounded
xpow024 power -73150542E-242017390 -8 -> Infinity Overflow Inexact Rounded
xrem024 remainder -73150542E-242017390 -8.15869954 -> -7.3150542E-242017383
xsub024 subtract -73150542E-242017390 -8.15869954 -> 8.15869954 Inexact Rounded
xadd025 add 2015.62109E+299897596 -11788916.1 -> 2.01562109E+299897599 Inexact Rounded
xcom025 compare 2015.62109E+299897596 -11788916.1 -> 1
xdiv025 divide 2015.62109E+299897596 -11788916.1 -> -1.70975947E+299897592 Inexact Rounded
xdvi025 divideint 2015.62109E+299897596 -11788916.1 -> NaN Division_impossible
xmul025 multiply 2015.62109E+299897596 -11788916.1 -> -2.37619879E+299897606 Inexact Rounded
xpow025 power 2015.62109E+299897596 -11788916 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem025 remainder 2015.62109E+299897596 -11788916.1 -> NaN Division_impossible
xsub025 subtract 2015.62109E+299897596 -11788916.1 -> 2.01562109E+299897599 Inexact Rounded
xadd026 add 29.498114 -26486451 -> -26486421.5 Inexact Rounded
xcom026 compare 29.498114 -26486451 -> 1
xdiv026 divide 29.498114 -26486451 -> -0.00000111370580 Inexact Rounded
xdvi026 divideint 29.498114 -26486451 -> -0
xmul026 multiply 29.498114 -26486451 -> -781300351 Inexact Rounded
xpow026 power 29.498114 -26486451 -> 4.22252513E-38929634 Inexact Rounded
xrem026 remainder 29.498114 -26486451 -> 29.498114
xsub026 subtract 29.498114 -26486451 -> 26486480.5 Inexact Rounded
xadd027 add 244375043.E+130840878 -9.44522029 -> 2.44375043E+130840886 Inexact Rounded
xcom027 compare 244375043.E+130840878 -9.44522029 -> 1
xdiv027 divide 244375043.E+130840878 -9.44522029 -> -2.58728791E+130840885 Inexact Rounded
xdvi027 divideint 244375043.E+130840878 -9.44522029 -> NaN Division_impossible
xmul027 multiply 244375043.E+130840878 -9.44522029 -> -2.30817611E+130840887 Inexact Rounded
xpow027 power 244375043.E+130840878 -9 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem027 remainder 244375043.E+130840878 -9.44522029 -> NaN Division_impossible
xsub027 subtract 244375043.E+130840878 -9.44522029 -> 2.44375043E+130840886 Inexact Rounded
xadd028 add -349388.759 -196215.776 -> -545604.535
xcom028 compare -349388.759 -196215.776 -> -1
xdiv028 divide -349388.759 -196215.776 -> 1.78063541 Inexact Rounded
xdvi028 divideint -349388.759 -196215.776 -> 1
xmul028 multiply -349388.759 -196215.776 -> 6.85555865E+10 Inexact Rounded
xpow028 power -349388.759 -196216 -> 1.24551752E-1087686 Inexact Rounded
xrem028 remainder -349388.759 -196215.776 -> -153172.983
xsub028 subtract -349388.759 -196215.776 -> -153172.983
xadd029 add -70905112.4 -91353968.8 -> -162259081 Inexact Rounded
xcom029 compare -70905112.4 -91353968.8 -> 1
xdiv029 divide -70905112.4 -91353968.8 -> 0.776157986 Inexact Rounded
xdvi029 divideint -70905112.4 -91353968.8 -> 0
xmul029 multiply -70905112.4 -91353968.8 -> 6.47746343E+15 Inexact Rounded
xpow029 power -70905112.4 -91353969 -> -3.05944741E-717190554 Inexact Rounded
xrem029 remainder -70905112.4 -91353968.8 -> -70905112.4
xsub029 subtract -70905112.4 -91353968.8 -> 20448856.4
xadd030 add -225094.28 -88.7723542 -> -225183.052 Inexact Rounded
xcom030 compare -225094.28 -88.7723542 -> -1
xdiv030 divide -225094.28 -88.7723542 -> 2535.63491 Inexact Rounded
xdvi030 divideint -225094.28 -88.7723542 -> 2535
xmul030 multiply -225094.28 -88.7723542 -> 19982149.2 Inexact Rounded
xpow030 power -225094.28 -89 -> -4.36076965E-477 Inexact Rounded
xrem030 remainder -225094.28 -88.7723542 -> -56.3621030
xsub030 subtract -225094.28 -88.7723542 -> -225005.508 Inexact Rounded
xadd031 add 50.4442340 82.7952169E+880120759 -> 8.27952169E+880120760 Inexact Rounded
xcom031 compare 50.4442340 82.7952169E+880120759 -> -1
xdiv031 divide 50.4442340 82.7952169E+880120759 -> 6.09265075E-880120760 Inexact Rounded
xdvi031 divideint 50.4442340 82.7952169E+880120759 -> 0
xmul031 multiply 50.4442340 82.7952169E+880120759 -> 4.17654130E+880120762 Inexact Rounded
xpow031 power 50.4442340 8 -> 4.19268518E+13 Inexact Rounded
xrem031 remainder 50.4442340 82.7952169E+880120759 -> 50.4442340
xsub031 subtract 50.4442340 82.7952169E+880120759 -> -8.27952169E+880120760 Inexact Rounded
xadd032 add -32311.9037 8.36379449 -> -32303.5399 Inexact Rounded
xcom032 compare -32311.9037 8.36379449 -> -1
xdiv032 divide -32311.9037 8.36379449 -> -3863.30675 Inexact Rounded
xdvi032 divideint -32311.9037 8.36379449 -> -3863
xmul032 multiply -32311.9037 8.36379449 -> -270250.122 Inexact Rounded
xpow032 power -32311.9037 8 -> 1.18822960E+36 Inexact Rounded
xrem032 remainder -32311.9037 8.36379449 -> -2.56558513
xsub032 subtract -32311.9037 8.36379449 -> -32320.2675 Inexact Rounded
xadd033 add 615396156.E+549895291 -29530247.4 -> 6.15396156E+549895299 Inexact Rounded
xcom033 compare 615396156.E+549895291 -29530247.4 -> 1
xdiv033 divide 615396156.E+549895291 -29530247.4 -> -2.08395191E+549895292 Inexact Rounded
xdvi033 divideint 615396156.E+549895291 -29530247.4 -> NaN Division_impossible
xmul033 multiply 615396156.E+549895291 -29530247.4 -> -1.81728007E+549895307 Inexact Rounded
xpow033 power 615396156.E+549895291 -29530247 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem033 remainder 615396156.E+549895291 -29530247.4 -> NaN Division_impossible
xsub033 subtract 615396156.E+549895291 -29530247.4 -> 6.15396156E+549895299 Inexact Rounded
xadd034 add 592.142173E-419941416 -3.46079109E-844011845 -> 5.92142173E-419941414 Inexact Rounded
xcom034 compare 592.142173E-419941416 -3.46079109E-844011845 -> 1
xdiv034 divide 592.142173E-419941416 -3.46079109E-844011845 -> -1.71100236E+424070431 Inexact Rounded
xdvi034 divideint 592.142173E-419941416 -3.46079109E-844011845 -> NaN Division_impossible
xmul034 multiply 592.142173E-419941416 -3.46079109E-844011845 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xpow034 power 592.142173E-419941416 -3 -> Infinity Overflow Inexact Rounded
xrem034 remainder 592.142173E-419941416 -3.46079109E-844011845 -> NaN Division_impossible
xsub034 subtract 592.142173E-419941416 -3.46079109E-844011845 -> 5.92142173E-419941414 Inexact Rounded
xadd035 add 849.515993E-878446473 -1039.08778 -> -1039.08778 Inexact Rounded
xcom035 compare 849.515993E-878446473 -1039.08778 -> 1
xdiv035 divide 849.515993E-878446473 -1039.08778 -> -8.17559411E-878446474 Inexact Rounded
xdvi035 divideint 849.515993E-878446473 -1039.08778 -> -0
xmul035 multiply 849.515993E-878446473 -1039.08778 -> -8.82721687E-878446468 Inexact Rounded
xpow035 power 849.515993E-878446473 -1039 -> Infinity Overflow Inexact Rounded
xrem035 remainder 849.515993E-878446473 -1039.08778 -> 8.49515993E-878446471
xsub035 subtract 849.515993E-878446473 -1039.08778 -> 1039.08778 Inexact Rounded
xadd036 add 213361789 -599.644851 -> 213361189 Inexact Rounded
xcom036 compare 213361789 -599.644851 -> 1
xdiv036 divide 213361789 -599.644851 -> -355813.593 Inexact Rounded
xdvi036 divideint 213361789 -599.644851 -> -355813
xmul036 multiply 213361789 -599.644851 -> -1.27941298E+11 Inexact Rounded
xpow036 power 213361789 -600 -> 3.38854684E-4998 Inexact Rounded
xrem036 remainder 213361789 -599.644851 -> 355.631137
xsub036 subtract 213361789 -599.644851 -> 213362389 Inexact Rounded
xadd037 add -795522555. -298.037702 -> -795522853 Inexact Rounded
xcom037 compare -795522555. -298.037702 -> -1
xdiv037 divide -795522555. -298.037702 -> 2669201.08 Inexact Rounded
xdvi037 divideint -795522555. -298.037702 -> 2669201
xmul037 multiply -795522555. -298.037702 -> 2.37095714E+11 Inexact Rounded
xpow037 power -795522555. -298 -> 4.03232712E-2653 Inexact Rounded
xrem037 remainder -795522555. -298.037702 -> -22.783898
xsub037 subtract -795522555. -298.037702 -> -795522257 Inexact Rounded
xadd038 add -501260651. -8761893.0E-689281479 -> -501260651 Inexact Rounded
xcom038 compare -501260651. -8761893.0E-689281479 -> -1
xdiv038 divide -501260651. -8761893.0E-689281479 -> 5.72091728E+689281480 Inexact Rounded
xdvi038 divideint -501260651. -8761893.0E-689281479 -> NaN Division_impossible
xmul038 multiply -501260651. -8761893.0E-689281479 -> 4.39199219E-689281464 Inexact Rounded
xpow038 power -501260651. -9 -> -5.00526961E-79 Inexact Rounded
xrem038 remainder -501260651. -8761893.0E-689281479 -> NaN Division_impossible
xsub038 subtract -501260651. -8761893.0E-689281479 -> -501260651 Inexact Rounded
xadd039 add -1.70781105E-848889023 36504769.4 -> 36504769.4 Inexact Rounded
xcom039 compare -1.70781105E-848889023 36504769.4 -> -1
xdiv039 divide -1.70781105E-848889023 36504769.4 -> -4.67832307E-848889031 Inexact Rounded
xdvi039 divideint -1.70781105E-848889023 36504769.4 -> -0
xmul039 multiply -1.70781105E-848889023 36504769.4 -> -6.23432486E-848889016 Inexact Rounded
xpow039 power -1.70781105E-848889023 36504769 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem039 remainder -1.70781105E-848889023 36504769.4 -> -1.70781105E-848889023
xsub039 subtract -1.70781105E-848889023 36504769.4 -> -36504769.4 Inexact Rounded
xadd040 add -5290.54984E-490626676 842535254 -> 842535254 Inexact Rounded
xcom040 compare -5290.54984E-490626676 842535254 -> -1
xdiv040 divide -5290.54984E-490626676 842535254 -> -6.27932162E-490626682 Inexact Rounded
xdvi040 divideint -5290.54984E-490626676 842535254 -> -0
xmul040 multiply -5290.54984E-490626676 842535254 -> -4.45747475E-490626664 Inexact Rounded
xpow040 power -5290.54984E-490626676 842535254 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem040 remainder -5290.54984E-490626676 842535254 -> -5.29054984E-490626673
xsub040 subtract -5290.54984E-490626676 842535254 -> -842535254 Inexact Rounded
xadd041 add 608.31825E+535268120 -59609.0993 -> 6.08318250E+535268122 Inexact Rounded
xcom041 compare 608.31825E+535268120 -59609.0993 -> 1
xdiv041 divide 608.31825E+535268120 -59609.0993 -> -1.02051240E+535268118 Inexact Rounded
xdvi041 divideint 608.31825E+535268120 -59609.0993 -> NaN Division_impossible
xmul041 multiply 608.31825E+535268120 -59609.0993 -> -3.62613030E+535268127 Inexact Rounded
xpow041 power 608.31825E+535268120 -59609 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem041 remainder 608.31825E+535268120 -59609.0993 -> NaN Division_impossible
xsub041 subtract 608.31825E+535268120 -59609.0993 -> 6.08318250E+535268122 Inexact Rounded
xadd042 add -4629035.31 -167.884398 -> -4629203.19 Inexact Rounded
xcom042 compare -4629035.31 -167.884398 -> -1
xdiv042 divide -4629035.31 -167.884398 -> 27572.7546 Inexact Rounded
xdvi042 divideint -4629035.31 -167.884398 -> 27572
xmul042 multiply -4629035.31 -167.884398 -> 777142806 Inexact Rounded
xpow042 power -4629035.31 -168 -> 1.57614831E-1120 Inexact Rounded
xrem042 remainder -4629035.31 -167.884398 -> -126.688344
xsub042 subtract -4629035.31 -167.884398 -> -4628867.43 Inexact Rounded
xadd043 add -66527378. -706400268. -> -772927646
xcom043 compare -66527378. -706400268. -> 1
xdiv043 divide -66527378. -706400268. -> 0.0941780192 Inexact Rounded
xdvi043 divideint -66527378. -706400268. -> 0
xmul043 multiply -66527378. -706400268. -> 4.69949576E+16 Inexact Rounded
xpow043 power -66527378. -706400268 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem043 remainder -66527378. -706400268. -> -66527378
xsub043 subtract -66527378. -706400268. -> 639872890
xadd044 add -2510497.53 372882462. -> 370371964 Inexact Rounded
xcom044 compare -2510497.53 372882462. -> -1
xdiv044 divide -2510497.53 372882462. -> -0.00673267795 Inexact Rounded
xdvi044 divideint -2510497.53 372882462. -> -0
xmul044 multiply -2510497.53 372882462. -> -9.36120500E+14 Inexact Rounded
xpow044 power -2510497.53 372882462 -> Infinity Overflow Inexact Rounded
xrem044 remainder -2510497.53 372882462. -> -2510497.53
xsub044 subtract -2510497.53 372882462. -> -375392960 Inexact Rounded
xadd045 add 136.255393E+53329961 -53494.7201E+720058060 -> -5.34947201E+720058064 Inexact Rounded
xcom045 compare 136.255393E+53329961 -53494.7201E+720058060 -> 1
xdiv045 divide 136.255393E+53329961 -53494.7201E+720058060 -> -2.54708115E-666728102 Inexact Rounded
xdvi045 divideint 136.255393E+53329961 -53494.7201E+720058060 -> -0
xmul045 multiply 136.255393E+53329961 -53494.7201E+720058060 -> -7.28894411E+773388027 Inexact Rounded
xpow045 power 136.255393E+53329961 -5 -> 2.12927373E-266649816 Inexact Rounded
xrem045 remainder 136.255393E+53329961 -53494.7201E+720058060 -> 1.36255393E+53329963
xsub045 subtract 136.255393E+53329961 -53494.7201E+720058060 -> 5.34947201E+720058064 Inexact Rounded
xadd046 add -876673.100 -6150.92266 -> -882824.023 Inexact Rounded
xcom046 compare -876673.100 -6150.92266 -> -1
xdiv046 divide -876673.100 -6150.92266 -> 142.527089 Inexact Rounded
xdvi046 divideint -876673.100 -6150.92266 -> 142
xmul046 multiply -876673.100 -6150.92266 -> 5.39234844E+9 Inexact Rounded
xpow046 power -876673.100 -6151 -> -4.03111774E-36555 Inexact Rounded
xrem046 remainder -876673.100 -6150.92266 -> -3242.08228
xsub046 subtract -876673.100 -6150.92266 -> -870522.177 Inexact Rounded
xadd047 add -2.45151797E+911306117 27235771 -> -2.45151797E+911306117 Inexact Rounded
xcom047 compare -2.45151797E+911306117 27235771 -> -1
xdiv047 divide -2.45151797E+911306117 27235771 -> -9.00109628E+911306109 Inexact Rounded
xdvi047 divideint -2.45151797E+911306117 27235771 -> NaN Division_impossible
xmul047 multiply -2.45151797E+911306117 27235771 -> -6.67689820E+911306124 Inexact Rounded
xpow047 power -2.45151797E+911306117 27235771 -> -Infinity Overflow Inexact Rounded
xrem047 remainder -2.45151797E+911306117 27235771 -> NaN Division_impossible
xsub047 subtract -2.45151797E+911306117 27235771 -> -2.45151797E+911306117 Inexact Rounded
xadd048 add -9.15117551 -4.95100733E-314511326 -> -9.15117551 Inexact Rounded
xcom048 compare -9.15117551 -4.95100733E-314511326 -> -1
xdiv048 divide -9.15117551 -4.95100733E-314511326 -> 1.84834618E+314511326 Inexact Rounded
xdvi048 divideint -9.15117551 -4.95100733E-314511326 -> NaN Division_impossible
xmul048 multiply -9.15117551 -4.95100733E-314511326 -> 4.53075370E-314511325 Inexact Rounded
xpow048 power -9.15117551 -5 -> -0.0000155817265 Inexact Rounded
xrem048 remainder -9.15117551 -4.95100733E-314511326 -> NaN Division_impossible
xsub048 subtract -9.15117551 -4.95100733E-314511326 -> -9.15117551 Inexact Rounded
xadd049 add 3.61890453E-985606128 30664416. -> 30664416.0 Inexact Rounded
xcom049 compare 3.61890453E-985606128 30664416. -> -1
xdiv049 divide 3.61890453E-985606128 30664416. -> 1.18016418E-985606135 Inexact Rounded
xdvi049 divideint 3.61890453E-985606128 30664416. -> 0
xmul049 multiply 3.61890453E-985606128 30664416. -> 1.10971594E-985606120 Inexact Rounded
xpow049 power 3.61890453E-985606128 30664416 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem049 remainder 3.61890453E-985606128 30664416. -> 3.61890453E-985606128
xsub049 subtract 3.61890453E-985606128 30664416. -> -30664416.0 Inexact Rounded
xadd050 add -257674602E+216723382 -70820959.4 -> -2.57674602E+216723390 Inexact Rounded
xcom050 compare -257674602E+216723382 -70820959.4 -> -1
xdiv050 divide -257674602E+216723382 -70820959.4 -> 3.63839468E+216723382 Inexact Rounded
xdvi050 divideint -257674602E+216723382 -70820959.4 -> NaN Division_impossible
xmul050 multiply -257674602E+216723382 -70820959.4 -> 1.82487625E+216723398 Inexact Rounded
xpow050 power -257674602E+216723382 -70820959 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem050 remainder -257674602E+216723382 -70820959.4 -> NaN Division_impossible
xsub050 subtract -257674602E+216723382 -70820959.4 -> -2.57674602E+216723390 Inexact Rounded
xadd051 add 218699.206 556944241. -> 557162940 Inexact Rounded
xcom051 compare 218699.206 556944241. -> -1
xdiv051 divide 218699.206 556944241. -> 0.000392677022 Inexact Rounded
xdvi051 divideint 218699.206 556944241. -> 0
xmul051 multiply 218699.206 556944241. -> 1.21803263E+14 Inexact Rounded
xpow051 power 218699.206 556944241 -> Infinity Overflow Inexact Rounded
xrem051 remainder 218699.206 556944241. -> 218699.206
xsub051 subtract 218699.206 556944241. -> -556725542 Inexact Rounded
xadd052 add 106211716. -3456793.74 -> 102754922 Inexact Rounded
xcom052 compare 106211716. -3456793.74 -> 1
xdiv052 divide 106211716. -3456793.74 -> -30.7255000 Inexact Rounded
xdvi052 divideint 106211716. -3456793.74 -> -30
xmul052 multiply 106211716. -3456793.74 -> -3.67151995E+14 Inexact Rounded
xpow052 power 106211716. -3456794 -> 2.07225581E-27744825 Inexact Rounded
xrem052 remainder 106211716. -3456793.74 -> 2507903.80
xsub052 subtract 106211716. -3456793.74 -> 109668510 Inexact Rounded
xadd053 add 1.25018078 399856.763E-726816740 -> 1.25018078 Inexact Rounded
xcom053 compare 1.25018078 399856.763E-726816740 -> 1
xdiv053 divide 1.25018078 399856.763E-726816740 -> 3.12657155E+726816734 Inexact Rounded
xdvi053 divideint 1.25018078 399856.763E-726816740 -> NaN Division_impossible
xmul053 multiply 1.25018078 399856.763E-726816740 -> 4.99893240E-726816735 Inexact Rounded
xpow053 power 1.25018078 4 -> 2.44281890 Inexact Rounded
xrem053 remainder 1.25018078 399856.763E-726816740 -> NaN Division_impossible
xsub053 subtract 1.25018078 399856.763E-726816740 -> 1.25018078 Inexact Rounded
xadd054 add 364.99811 -46222.0505 -> -45857.0524 Inexact Rounded
xcom054 compare 364.99811 -46222.0505 -> 1
xdiv054 divide 364.99811 -46222.0505 -> -0.00789662306 Inexact Rounded
xdvi054 divideint 364.99811 -46222.0505 -> -0
xmul054 multiply 364.99811 -46222.0505 -> -16870961.1 Inexact Rounded
xpow054 power 364.99811 -46222 -> 6.35570856E-118435 Inexact Rounded
xrem054 remainder 364.99811 -46222.0505 -> 364.99811
xsub054 subtract 364.99811 -46222.0505 -> 46587.0486 Inexact Rounded
xadd055 add -392217576. -958364096 -> -1.35058167E+9 Inexact Rounded
xcom055 compare -392217576. -958364096 -> 1
xdiv055 divide -392217576. -958364096 -> 0.409257377 Inexact Rounded
xdvi055 divideint -392217576. -958364096 -> 0
xmul055 multiply -392217576. -958364096 -> 3.75887243E+17 Inexact Rounded
xpow055 power -392217576. -958364096 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem055 remainder -392217576. -958364096 -> -392217576
xsub055 subtract -392217576. -958364096 -> 566146520
xadd056 add 169601285 714526.639 -> 170315812 Inexact Rounded
xcom056 compare 169601285 714526.639 -> 1
xdiv056 divide 169601285 714526.639 -> 237.361738 Inexact Rounded
xdvi056 divideint 169601285 714526.639 -> 237
xmul056 multiply 169601285 714526.639 -> 1.21184636E+14 Inexact Rounded
xpow056 power 169601285 714527 -> 2.06052444E+5880149 Inexact Rounded
xrem056 remainder 169601285 714526.639 -> 258471.557
xsub056 subtract 169601285 714526.639 -> 168886758 Inexact Rounded
xadd057 add -674.094552E+586944319 6354.2668E+589657266 -> 6.35426680E+589657269 Inexact Rounded
xcom057 compare -674.094552E+586944319 6354.2668E+589657266 -> -1
xdiv057 divide -674.094552E+586944319 6354.2668E+589657266 -> -1.06085340E-2712948 Inexact Rounded
xdvi057 divideint -674.094552E+586944319 6354.2668E+589657266 -> -0
xmul057 multiply -674.094552E+586944319 6354.2668E+589657266 -> -Infinity Inexact Overflow Rounded
xpow057 power -674.094552E+586944319 6 -> Infinity Overflow Inexact Rounded
xrem057 remainder -674.094552E+586944319 6354.2668E+589657266 -> -6.74094552E+586944321
xsub057 subtract -674.094552E+586944319 6354.2668E+589657266 -> -6.35426680E+589657269 Inexact Rounded
xadd058 add 151795163E-371727182 -488.09788E-738852245 -> 1.51795163E-371727174 Inexact Rounded
xcom058 compare 151795163E-371727182 -488.09788E-738852245 -> 1
xdiv058 divide 151795163E-371727182 -488.09788E-738852245 -> -3.10993285E+367125068 Inexact Rounded
xdvi058 divideint 151795163E-371727182 -488.09788E-738852245 -> NaN Division_impossible
xmul058 multiply 151795163E-371727182 -488.09788E-738852245 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xpow058 power 151795163E-371727182 -5 -> Infinity Overflow Inexact Rounded
xrem058 remainder 151795163E-371727182 -488.09788E-738852245 -> NaN Division_impossible
xsub058 subtract 151795163E-371727182 -488.09788E-738852245 -> 1.51795163E-371727174 Inexact Rounded
xadd059 add -746.293386 927749.647 -> 927003.354 Inexact Rounded
xcom059 compare -746.293386 927749.647 -> -1
xdiv059 divide -746.293386 927749.647 -> -0.000804412471 Inexact Rounded
xdvi059 divideint -746.293386 927749.647 -> -0
xmul059 multiply -746.293386 927749.647 -> -692373425 Inexact Rounded
xpow059 power -746.293386 927750 -> 7.49278741E+2665341 Inexact Rounded
xrem059 remainder -746.293386 927749.647 -> -746.293386
xsub059 subtract -746.293386 927749.647 -> -928495.940 Inexact Rounded
xadd060 add 888946471E+241331592 -235739.595 -> 8.88946471E+241331600 Inexact Rounded
xcom060 compare 888946471E+241331592 -235739.595 -> 1
xdiv060 divide 888946471E+241331592 -235739.595 -> -3.77088317E+241331595 Inexact Rounded
xdvi060 divideint 888946471E+241331592 -235739.595 -> NaN Division_impossible
xmul060 multiply 888946471E+241331592 -235739.595 -> -2.09559881E+241331606 Inexact Rounded
xpow060 power 888946471E+241331592 -235740 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem060 remainder 888946471E+241331592 -235739.595 -> NaN Division_impossible
xsub060 subtract 888946471E+241331592 -235739.595 -> 8.88946471E+241331600 Inexact Rounded
xadd061 add 6.64377249 79161.1070E+619453776 -> 7.91611070E+619453780 Inexact Rounded
xcom061 compare 6.64377249 79161.1070E+619453776 -> -1
xdiv061 divide 6.64377249 79161.1070E+619453776 -> 8.39272307E-619453781 Inexact Rounded
xdvi061 divideint 6.64377249 79161.1070E+619453776 -> 0
xmul061 multiply 6.64377249 79161.1070E+619453776 -> 5.25928385E+619453781 Inexact Rounded
xpow061 power 6.64377249 8 -> 3795928.44 Inexact Rounded
xrem061 remainder 6.64377249 79161.1070E+619453776 -> 6.64377249
xsub061 subtract 6.64377249 79161.1070E+619453776 -> -7.91611070E+619453780 Inexact Rounded
xadd062 add 3146.66571E-313373366 88.5282010 -> 88.5282010 Inexact Rounded
xcom062 compare 3146.66571E-313373366 88.5282010 -> -1
xdiv062 divide 3146.66571E-313373366 88.5282010 -> 3.55442184E-313373365 Inexact Rounded
xdvi062 divideint 3146.66571E-313373366 88.5282010 -> 0
xmul062 multiply 3146.66571E-313373366 88.5282010 -> 2.78568654E-313373361 Inexact Rounded
xpow062 power 3146.66571E-313373366 89 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem062 remainder 3146.66571E-313373366 88.5282010 -> 3.14666571E-313373363
xsub062 subtract 3146.66571E-313373366 88.5282010 -> -88.5282010 Inexact Rounded
xadd063 add 6.44693097 -87195.8711 -> -87189.4242 Inexact Rounded
xcom063 compare 6.44693097 -87195.8711 -> 1
xdiv063 divide 6.44693097 -87195.8711 -> -0.0000739361955 Inexact Rounded
xdvi063 divideint 6.44693097 -87195.8711 -> -0
xmul063 multiply 6.44693097 -87195.8711 -> -562145.762 Inexact Rounded
xpow063 power 6.44693097 -87196 -> 4.50881730E-70573 Inexact Rounded
xrem063 remainder 6.44693097 -87195.8711 -> 6.44693097
xsub063 subtract 6.44693097 -87195.8711 -> 87202.3180 Inexact Rounded
xadd064 add -2113132.56E+577957840 981125821 -> -2.11313256E+577957846 Inexact Rounded
xcom064 compare -2113132.56E+577957840 981125821 -> -1
xdiv064 divide -2113132.56E+577957840 981125821 -> -2.15378345E+577957837 Inexact Rounded
xdvi064 divideint -2113132.56E+577957840 981125821 -> NaN Division_impossible
xmul064 multiply -2113132.56E+577957840 981125821 -> -2.07324892E+577957855 Inexact Rounded
xpow064 power -2113132.56E+577957840 981125821 -> -Infinity Overflow Inexact Rounded
xrem064 remainder -2113132.56E+577957840 981125821 -> NaN Division_impossible
xsub064 subtract -2113132.56E+577957840 981125821 -> -2.11313256E+577957846 Inexact Rounded
xadd065 add -7701.42814 72667.5181 -> 64966.0900 Inexact Rounded
xcom065 compare -7701.42814 72667.5181 -> -1
xdiv065 divide -7701.42814 72667.5181 -> -0.105981714 Inexact Rounded
xdvi065 divideint -7701.42814 72667.5181 -> -0
xmul065 multiply -7701.42814 72667.5181 -> -559643669 Inexact Rounded
xpow065 power -7701.42814 72668 -> 2.29543837E+282429 Inexact Rounded
xrem065 remainder -7701.42814 72667.5181 -> -7701.42814
xsub065 subtract -7701.42814 72667.5181 -> -80368.9462 Inexact Rounded
xadd066 add -851.754789 -582659.149 -> -583510.904 Inexact Rounded
xcom066 compare -851.754789 -582659.149 -> 1
xdiv066 divide -851.754789 -582659.149 -> 0.00146184058 Inexact Rounded
xdvi066 divideint -851.754789 -582659.149 -> 0
xmul066 multiply -851.754789 -582659.149 -> 496282721 Inexact Rounded
xpow066 power -851.754789 -582659 -> -6.83532593E-1707375 Inexact Rounded
xrem066 remainder -851.754789 -582659.149 -> -851.754789
xsub066 subtract -851.754789 -582659.149 -> 581807.394 Inexact Rounded
xadd067 add -5.01992943 7852.16531 -> 7847.14538 Inexact Rounded
xcom067 compare -5.01992943 7852.16531 -> -1
xdiv067 divide -5.01992943 7852.16531 -> -0.000639305113 Inexact Rounded
xdvi067 divideint -5.01992943 7852.16531 -> -0
xmul067 multiply -5.01992943 7852.16531 -> -39417.3157 Inexact Rounded
xpow067 power -5.01992943 7852 -> 7.54481448E+5501 Inexact Rounded
xrem067 remainder -5.01992943 7852.16531 -> -5.01992943
xsub067 subtract -5.01992943 7852.16531 -> -7857.18524 Inexact Rounded
xadd068 add -12393257.2 76803689E+949125770 -> 7.68036890E+949125777 Inexact Rounded
xcom068 compare -12393257.2 76803689E+949125770 -> -1
xdiv068 divide -12393257.2 76803689E+949125770 -> -1.61362786E-949125771 Inexact Rounded
xdvi068 divideint -12393257.2 76803689E+949125770 -> -0
xmul068 multiply -12393257.2 76803689E+949125770 -> -9.51847872E+949125784 Inexact Rounded
xpow068 power -12393257.2 8 -> 5.56523749E+56 Inexact Rounded
xrem068 remainder -12393257.2 76803689E+949125770 -> -12393257.2
xsub068 subtract -12393257.2 76803689E+949125770 -> -7.68036890E+949125777 Inexact Rounded
xadd069 add -754771634.E+716555026 -292336.311 -> -7.54771634E+716555034 Inexact Rounded
xcom069 compare -754771634.E+716555026 -292336.311 -> -1
xdiv069 divide -754771634.E+716555026 -292336.311 -> 2.58186070E+716555029 Inexact Rounded
xdvi069 divideint -754771634.E+716555026 -292336.311 -> NaN Division_impossible
xmul069 multiply -754771634.E+716555026 -292336.311 -> 2.20647155E+716555040 Inexact Rounded
xpow069 power -754771634.E+716555026 -292336 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem069 remainder -754771634.E+716555026 -292336.311 -> NaN Division_impossible
xsub069 subtract -754771634.E+716555026 -292336.311 -> -7.54771634E+716555034 Inexact Rounded
xadd070 add -915006.171E+614548652 -314086965. -> -9.15006171E+614548657 Inexact Rounded
xcom070 compare -915006.171E+614548652 -314086965. -> -1
xdiv070 divide -915006.171E+614548652 -314086965. -> 2.91322555E+614548649 Inexact Rounded
xdvi070 divideint -915006.171E+614548652 -314086965. -> NaN Division_impossible
xmul070 multiply -915006.171E+614548652 -314086965. -> 2.87391511E+614548666 Inexact Rounded
xpow070 power -915006.171E+614548652 -314086965 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem070 remainder -915006.171E+614548652 -314086965. -> NaN Division_impossible
xsub070 subtract -915006.171E+614548652 -314086965. -> -9.15006171E+614548657 Inexact Rounded
xadd071 add -296590035 -481734529 -> -778324564
xcom071 compare -296590035 -481734529 -> 1
xdiv071 divide -296590035 -481734529 -> 0.615671116 Inexact Rounded
xdvi071 divideint -296590035 -481734529 -> 0
xmul071 multiply -296590035 -481734529 -> 1.42877661E+17 Inexact Rounded
xpow071 power -296590035 -481734529 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem071 remainder -296590035 -481734529 -> -296590035
xsub071 subtract -296590035 -481734529 -> 185144494
xadd072 add 8.27822605 9241557.19 -> 9241565.47 Inexact Rounded
xcom072 compare 8.27822605 9241557.19 -> -1
xdiv072 divide 8.27822605 9241557.19 -> 8.95760950E-7 Inexact Rounded
xdvi072 divideint 8.27822605 9241557.19 -> 0
xmul072 multiply 8.27822605 9241557.19 -> 76503699.5 Inexact Rounded
xpow072 power 8.27822605 9241557 -> 5.10219969E+8483169 Inexact Rounded
xrem072 remainder 8.27822605 9241557.19 -> 8.27822605
xsub072 subtract 8.27822605 9241557.19 -> -9241548.91 Inexact Rounded
xadd073 add -1.43581098 7286313.54 -> 7286312.10 Inexact Rounded
xcom073 compare -1.43581098 7286313.54 -> -1
xdiv073 divide -1.43581098 7286313.54 -> -1.97055887E-7 Inexact Rounded
xdvi073 divideint -1.43581098 7286313.54 -> -0
xmul073 multiply -1.43581098 7286313.54 -> -10461769.0 Inexact Rounded
xpow073 power -1.43581098 7286314 -> 1.09389741E+1144660 Inexact Rounded
xrem073 remainder -1.43581098 7286313.54 -> -1.43581098
xsub073 subtract -1.43581098 7286313.54 -> -7286314.98 Inexact Rounded
xadd074 add -699036193. 759263.509E+533543625 -> 7.59263509E+533543630 Inexact Rounded
xcom074 compare -699036193. 759263.509E+533543625 -> -1
xdiv074 divide -699036193. 759263.509E+533543625 -> -9.20676662E-533543623 Inexact Rounded
xdvi074 divideint -699036193. 759263.509E+533543625 -> -0
xmul074 multiply -699036193. 759263.509E+533543625 -> -5.30752673E+533543639 Inexact Rounded
xpow074 power -699036193. 8 -> 5.70160724E+70 Inexact Rounded
xrem074 remainder -699036193. 759263.509E+533543625 -> -699036193
xsub074 subtract -699036193. 759263.509E+533543625 -> -7.59263509E+533543630 Inexact Rounded
xadd075 add -83.7273615E-305281957 -287779593.E+458777774 -> -2.87779593E+458777782 Inexact Rounded
xcom075 compare -83.7273615E-305281957 -287779593.E+458777774 -> 1
xdiv075 divide -83.7273615E-305281957 -287779593.E+458777774 -> 2.90942664E-764059738 Inexact Rounded
xdvi075 divideint -83.7273615E-305281957 -287779593.E+458777774 -> 0
xmul075 multiply -83.7273615E-305281957 -287779593.E+458777774 -> 2.40950260E+153495827 Inexact Rounded
xpow075 power -83.7273615E-305281957 -3 -> -1.70371828E+915845865 Inexact Rounded
xrem075 remainder -83.7273615E-305281957 -287779593.E+458777774 -> -8.37273615E-305281956
xsub075 subtract -83.7273615E-305281957 -287779593.E+458777774 -> 2.87779593E+458777782 Inexact Rounded
xadd076 add 8.48503224 6522.03316 -> 6530.51819 Inexact Rounded
xcom076 compare 8.48503224 6522.03316 -> -1
xdiv076 divide 8.48503224 6522.03316 -> 0.00130097962 Inexact Rounded
xdvi076 divideint 8.48503224 6522.03316 -> 0
xmul076 multiply 8.48503224 6522.03316 -> 55339.6616 Inexact Rounded
xpow076 power 8.48503224 6522 -> 4.76547542E+6056 Inexact Rounded
xrem076 remainder 8.48503224 6522.03316 -> 8.48503224
xsub076 subtract 8.48503224 6522.03316 -> -6513.54813 Inexact Rounded
xadd077 add 527916091 -809.054070 -> 527915282 Inexact Rounded
xcom077 compare 527916091 -809.054070 -> 1
xdiv077 divide 527916091 -809.054070 -> -652510.272 Inexact Rounded
xdvi077 divideint 527916091 -809.054070 -> -652510
xmul077 multiply 527916091 -809.054070 -> -4.27112662E+11 Inexact Rounded
xpow077 power 527916091 -809 -> 2.78609697E-7057 Inexact Rounded
xrem077 remainder 527916091 -809.054070 -> 219.784300
xsub077 subtract 527916091 -809.054070 -> 527916900 Inexact Rounded
xadd078 add 3857058.60 5792997.58E+881077409 -> 5.79299758E+881077415 Inexact Rounded
xcom078 compare 3857058.60 5792997.58E+881077409 -> -1
xdiv078 divide 3857058.60 5792997.58E+881077409 -> 6.65813950E-881077410 Inexact Rounded
xdvi078 divideint 3857058.60 5792997.58E+881077409 -> 0
xmul078 multiply 3857058.60 5792997.58E+881077409 -> 2.23439311E+881077422 Inexact Rounded
xpow078 power 3857058.60 6 -> 3.29258824E+39 Inexact Rounded
xrem078 remainder 3857058.60 5792997.58E+881077409 -> 3857058.60
xsub078 subtract 3857058.60 5792997.58E+881077409 -> -5.79299758E+881077415 Inexact Rounded
xadd079 add -66587363.E+556538173 -551902402E+357309146 -> -6.65873630E+556538180 Inexact Rounded
xcom079 compare -66587363.E+556538173 -551902402E+357309146 -> -1
xdiv079 divide -66587363.E+556538173 -551902402E+357309146 -> 1.20650613E+199229026 Inexact Rounded
xdvi079 divideint -66587363.E+556538173 -551902402E+357309146 -> NaN Division_impossible
xmul079 multiply -66587363.E+556538173 -551902402E+357309146 -> 3.67497256E+913847335 Inexact Rounded
xpow079 power -66587363.E+556538173 -6 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem079 remainder -66587363.E+556538173 -551902402E+357309146 -> NaN Division_impossible
xsub079 subtract -66587363.E+556538173 -551902402E+357309146 -> -6.65873630E+556538180 Inexact Rounded
xadd080 add -580.502955 38125521.7 -> 38124941.2 Inexact Rounded
xcom080 compare -580.502955 38125521.7 -> -1
xdiv080 divide -580.502955 38125521.7 -> -0.0000152260987 Inexact Rounded
xdvi080 divideint -580.502955 38125521.7 -> -0
xmul080 multiply -580.502955 38125521.7 -> -2.21319780E+10 Inexact Rounded
xpow080 power -580.502955 38125522 -> 6.07262078E+105371486 Inexact Rounded
xrem080 remainder -580.502955 38125521.7 -> -580.502955
xsub080 subtract -580.502955 38125521.7 -> -38126102.2 Inexact Rounded
xadd081 add -9627363.00 -80616885E-749891394 -> -9627363.00 Inexact Rounded
xcom081 compare -9627363.00 -80616885E-749891394 -> -1
xdiv081 divide -9627363.00 -80616885E-749891394 -> 1.19421173E+749891393 Inexact Rounded
xdvi081 divideint -9627363.00 -80616885E-749891394 -> NaN Division_impossible
xmul081 multiply -9627363.00 -80616885E-749891394 -> 7.76128016E-749891380 Inexact Rounded
xpow081 power -9627363.00 -8 -> 1.35500601E-56 Inexact Rounded
xrem081 remainder -9627363.00 -80616885E-749891394 -> NaN Division_impossible
xsub081 subtract -9627363.00 -80616885E-749891394 -> -9627363.00 Inexact Rounded
xadd082 add -526.594855E+803110107 -64.5451639 -> -5.26594855E+803110109 Inexact Rounded
xcom082 compare -526.594855E+803110107 -64.5451639 -> -1
xdiv082 divide -526.594855E+803110107 -64.5451639 -> 8.15854858E+803110107 Inexact Rounded
xdvi082 divideint -526.594855E+803110107 -64.5451639 -> NaN Division_impossible
xmul082 multiply -526.594855E+803110107 -64.5451639 -> 3.39891512E+803110111 Inexact Rounded
xpow082 power -526.594855E+803110107 -65 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem082 remainder -526.594855E+803110107 -64.5451639 -> NaN Division_impossible
xsub082 subtract -526.594855E+803110107 -64.5451639 -> -5.26594855E+803110109 Inexact Rounded
xadd083 add -8378.55499 760.131257 -> -7618.42373 Inexact Rounded
xcom083 compare -8378.55499 760.131257 -> -1
xdiv083 divide -8378.55499 760.131257 -> -11.0225108 Inexact Rounded
xdvi083 divideint -8378.55499 760.131257 -> -11
xmul083 multiply -8378.55499 760.131257 -> -6368801.54 Inexact Rounded
xpow083 power -8378.55499 760 -> 4.06007928E+2981 Inexact Rounded
xrem083 remainder -8378.55499 760.131257 -> -17.111163
xsub083 subtract -8378.55499 760.131257 -> -9138.68625 Inexact Rounded
xadd084 add -717.697718 984304413 -> 984303695 Inexact Rounded
xcom084 compare -717.697718 984304413 -> -1
xdiv084 divide -717.697718 984304413 -> -7.29142030E-7 Inexact Rounded
xdvi084 divideint -717.697718 984304413 -> -0
xmul084 multiply -717.697718 984304413 -> -7.06433031E+11 Inexact Rounded
xpow084 power -717.697718 984304413 -> -Infinity Overflow Inexact Rounded
xrem084 remainder -717.697718 984304413 -> -717.697718
xsub084 subtract -717.697718 984304413 -> -984305131 Inexact Rounded
xadd085 add -76762243.4E-741100094 -273.706674 -> -273.706674 Inexact Rounded
xcom085 compare -76762243.4E-741100094 -273.706674 -> 1
xdiv085 divide -76762243.4E-741100094 -273.706674 -> 2.80454409E-741100089 Inexact Rounded
xdvi085 divideint -76762243.4E-741100094 -273.706674 -> 0
xmul085 multiply -76762243.4E-741100094 -273.706674 -> 2.10103383E-741100084 Inexact Rounded
xpow085 power -76762243.4E-741100094 -274 -> Infinity Overflow Inexact Rounded
xrem085 remainder -76762243.4E-741100094 -273.706674 -> -7.67622434E-741100087
xsub085 subtract -76762243.4E-741100094 -273.706674 -> 273.706674 Inexact Rounded
xadd086 add -701.518354E+786274918 8822750.68E+243052107 -> -7.01518354E+786274920 Inexact Rounded
xcom086 compare -701.518354E+786274918 8822750.68E+243052107 -> -1
xdiv086 divide -701.518354E+786274918 8822750.68E+243052107 -> -7.95124309E+543222806 Inexact Rounded
xdvi086 divideint -701.518354E+786274918 8822750.68E+243052107 -> NaN Division_impossible
xmul086 multiply -701.518354E+786274918 8822750.68E+243052107 -> -Infinity Inexact Overflow Rounded
xpow086 power -701.518354E+786274918 9 -> -Infinity Overflow Inexact Rounded
xrem086 remainder -701.518354E+786274918 8822750.68E+243052107 -> NaN Division_impossible
xsub086 subtract -701.518354E+786274918 8822750.68E+243052107 -> -7.01518354E+786274920 Inexact Rounded
xadd087 add -359866845. -4.57434117 -> -359866850 Inexact Rounded
xcom087 compare -359866845. -4.57434117 -> -1
xdiv087 divide -359866845. -4.57434117 -> 78670748.8 Inexact Rounded
xdvi087 divideint -359866845. -4.57434117 -> 78670748
xmul087 multiply -359866845. -4.57434117 -> 1.64615372E+9 Inexact Rounded
xpow087 power -359866845. -5 -> -1.65687909E-43 Inexact Rounded
xrem087 remainder -359866845. -4.57434117 -> -3.54890484
xsub087 subtract -359866845. -4.57434117 -> -359866840 Inexact Rounded
xadd088 add 779934536. -76562645.7 -> 703371890 Inexact Rounded
xcom088 compare 779934536. -76562645.7 -> 1
xdiv088 divide 779934536. -76562645.7 -> -10.1868807 Inexact Rounded
xdvi088 divideint 779934536. -76562645.7 -> -10
xmul088 multiply 779934536. -76562645.7 -> -5.97138515E+16 Inexact Rounded
xpow088 power 779934536. -76562646 -> 3.36739063E-680799501 Inexact Rounded
xrem088 remainder 779934536. -76562645.7 -> 14308079.0
xsub088 subtract 779934536. -76562645.7 -> 856497182 Inexact Rounded
xadd089 add -4820.95451 3516234.99E+303303176 -> 3.51623499E+303303182 Inexact Rounded
xcom089 compare -4820.95451 3516234.99E+303303176 -> -1
xdiv089 divide -4820.95451 3516234.99E+303303176 -> -1.37105584E-303303179 Inexact Rounded
xdvi089 divideint -4820.95451 3516234.99E+303303176 -> -0
xmul089 multiply -4820.95451 3516234.99E+303303176 -> -1.69516089E+303303186 Inexact Rounded
xpow089 power -4820.95451 4 -> 5.40172082E+14 Inexact Rounded
xrem089 remainder -4820.95451 3516234.99E+303303176 -> -4820.95451
xsub089 subtract -4820.95451 3516234.99E+303303176 -> -3.51623499E+303303182 Inexact Rounded
xadd090 add 69355976.9 -9.57838562E+758804984 -> -9.57838562E+758804984 Inexact Rounded
xcom090 compare 69355976.9 -9.57838562E+758804984 -> 1
xdiv090 divide 69355976.9 -9.57838562E+758804984 -> -7.24088376E-758804978 Inexact Rounded
xdvi090 divideint 69355976.9 -9.57838562E+758804984 -> -0
xmul090 multiply 69355976.9 -9.57838562E+758804984 -> -6.64318292E+758804992 Inexact Rounded
xpow090 power 69355976.9 -10 -> 3.88294346E-79 Inexact Rounded
xrem090 remainder 69355976.9 -9.57838562E+758804984 -> 69355976.9
xsub090 subtract 69355976.9 -9.57838562E+758804984 -> 9.57838562E+758804984 Inexact Rounded
xadd091 add -12672093.1 8569.78255E-382866025 -> -12672093.1 Inexact Rounded
xcom091 compare -12672093.1 8569.78255E-382866025 -> -1
xdiv091 divide -12672093.1 8569.78255E-382866025 -> -1.47869482E+382866028 Inexact Rounded
xdvi091 divideint -12672093.1 8569.78255E-382866025 -> NaN Division_impossible
xmul091 multiply -12672093.1 8569.78255E-382866025 -> -1.08597082E-382866014 Inexact Rounded
xpow091 power -12672093.1 9 -> -8.42626658E+63 Inexact Rounded
xrem091 remainder -12672093.1 8569.78255E-382866025 -> NaN Division_impossible
xsub091 subtract -12672093.1 8569.78255E-382866025 -> -12672093.1 Inexact Rounded
xadd092 add -5910750.2 66150383E-662459241 -> -5910750.20 Inexact Rounded
xcom092 compare -5910750.2 66150383E-662459241 -> -1
xdiv092 divide -5910750.2 66150383E-662459241 -> -8.93532272E+662459239 Inexact Rounded
xdvi092 divideint -5910750.2 66150383E-662459241 -> NaN Division_impossible
xmul092 multiply -5910750.2 66150383E-662459241 -> -3.90998390E-662459227 Inexact Rounded
xpow092 power -5910750.2 7 -> -2.52056696E+47 Inexact Rounded
xrem092 remainder -5910750.2 66150383E-662459241 -> NaN Division_impossible
xsub092 subtract -5910750.2 66150383E-662459241 -> -5910750.20 Inexact Rounded
xadd093 add -532577268.E-163806629 -240650398E-650110558 -> -5.32577268E-163806621 Inexact Rounded
xcom093 compare -532577268.E-163806629 -240650398E-650110558 -> -1
xdiv093 divide -532577268.E-163806629 -240650398E-650110558 -> 2.21307454E+486303929 Inexact Rounded
xdvi093 divideint -532577268.E-163806629 -240650398E-650110558 -> NaN Division_impossible
xmul093 multiply -532577268.E-163806629 -240650398E-650110558 -> 1.28164932E-813917170 Inexact Rounded
xpow093 power -532577268.E-163806629 -2 -> 3.52561389E+327613240 Inexact Rounded
xrem093 remainder -532577268.E-163806629 -240650398E-650110558 -> NaN Division_impossible
xsub093 subtract -532577268.E-163806629 -240650398E-650110558 -> -5.32577268E-163806621 Inexact Rounded
xadd094 add -671.507198E-908587890 3057429.32E-555230623 -> 3.05742932E-555230617 Inexact Rounded
xcom094 compare -671.507198E-908587890 3057429.32E-555230623 -> -1
xdiv094 divide -671.507198E-908587890 3057429.32E-555230623 -> -2.19631307E-353357271 Inexact Rounded
xdvi094 divideint -671.507198E-908587890 3057429.32E-555230623 -> -0
xmul094 multiply -671.507198E-908587890 3057429.32E-555230623 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xpow094 power -671.507198E-908587890 3 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem094 remainder -671.507198E-908587890 3057429.32E-555230623 -> -6.71507198E-908587888
xsub094 subtract -671.507198E-908587890 3057429.32E-555230623 -> -3.05742932E-555230617 Inexact Rounded
xadd095 add -294.994352E+346452027 -6061853.0 -> -2.94994352E+346452029 Inexact Rounded
xcom095 compare -294.994352E+346452027 -6061853.0 -> -1
xdiv095 divide -294.994352E+346452027 -6061853.0 -> 4.86640557E+346452022 Inexact Rounded
xdvi095 divideint -294.994352E+346452027 -6061853.0 -> NaN Division_impossible
xmul095 multiply -294.994352E+346452027 -6061853.0 -> 1.78821240E+346452036 Inexact Rounded
xpow095 power -294.994352E+346452027 -6061853 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem095 remainder -294.994352E+346452027 -6061853.0 -> NaN Division_impossible
xsub095 subtract -294.994352E+346452027 -6061853.0 -> -2.94994352E+346452029 Inexact Rounded
xadd096 add 329579114 146780548. -> 476359662
xcom096 compare 329579114 146780548. -> 1
xdiv096 divide 329579114 146780548. -> 2.24538686 Inexact Rounded
xdvi096 divideint 329579114 146780548. -> 2
xmul096 multiply 329579114 146780548. -> 4.83758030E+16 Inexact Rounded
xpow096 power 329579114 146780548 -> Infinity Overflow Inexact Rounded
xrem096 remainder 329579114 146780548. -> 36018018
xsub096 subtract 329579114 146780548. -> 182798566
xadd097 add -789904.686E-217225000 -1991.07181E-84080059 -> -1.99107181E-84080056 Inexact Rounded
xcom097 compare -789904.686E-217225000 -1991.07181E-84080059 -> 1
xdiv097 divide -789904.686E-217225000 -1991.07181E-84080059 -> 3.96723354E-133144939 Inexact Rounded
xdvi097 divideint -789904.686E-217225000 -1991.07181E-84080059 -> 0
xmul097 multiply -789904.686E-217225000 -1991.07181E-84080059 -> 1.57275695E-301305050 Inexact Rounded
xpow097 power -789904.686E-217225000 -2 -> 1.60269403E+434449988 Inexact Rounded
xrem097 remainder -789904.686E-217225000 -1991.07181E-84080059 -> -7.89904686E-217224995
xsub097 subtract -789904.686E-217225000 -1991.07181E-84080059 -> 1.99107181E-84080056 Inexact Rounded
xadd098 add 59893.3544 -408595868 -> -408535975 Inexact Rounded
xcom098 compare 59893.3544 -408595868 -> 1
xdiv098 divide 59893.3544 -408595868 -> -0.000146583358 Inexact Rounded
xdvi098 divideint 59893.3544 -408595868 -> -0
xmul098 multiply 59893.3544 -408595868 -> -2.44721771E+13 Inexact Rounded
xpow098 power 59893.3544 -408595868 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem098 remainder 59893.3544 -408595868 -> 59893.3544
xsub098 subtract 59893.3544 -408595868 -> 408655761 Inexact Rounded
xadd099 add 129.878613 -54652.7288E-963564940 -> 129.878613 Inexact Rounded
xcom099 compare 129.878613 -54652.7288E-963564940 -> 1
xdiv099 divide 129.878613 -54652.7288E-963564940 -> -2.37643418E+963564937 Inexact Rounded
xdvi099 divideint 129.878613 -54652.7288E-963564940 -> NaN Division_impossible
xmul099 multiply 129.878613 -54652.7288E-963564940 -> -7.09822061E-963564934 Inexact Rounded
xpow099 power 129.878613 -5 -> 2.70590029E-11 Inexact Rounded
xrem099 remainder 129.878613 -54652.7288E-963564940 -> NaN Division_impossible
xsub099 subtract 129.878613 -54652.7288E-963564940 -> 129.878613 Inexact Rounded
xadd100 add 9866.99208 708756501. -> 708766368 Inexact Rounded
xcom100 compare 9866.99208 708756501. -> -1
xdiv100 divide 9866.99208 708756501. -> 0.0000139215543 Inexact Rounded
xdvi100 divideint 9866.99208 708756501. -> 0
xmul100 multiply 9866.99208 708756501. -> 6.99329478E+12 Inexact Rounded
xpow100 power 9866.99208 708756501 -> Infinity Overflow Inexact Rounded
xrem100 remainder 9866.99208 708756501. -> 9866.99208
xsub100 subtract 9866.99208 708756501. -> -708746634 Inexact Rounded
xadd101 add -78810.6297 -399884.68 -> -478695.310 Inexact Rounded
xcom101 compare -78810.6297 -399884.68 -> 1
xdiv101 divide -78810.6297 -399884.68 -> 0.197083393 Inexact Rounded
xdvi101 divideint -78810.6297 -399884.68 -> 0
xmul101 multiply -78810.6297 -399884.68 -> 3.15151634E+10 Inexact Rounded
xpow101 power -78810.6297 -399885 -> -1.54252408E-1958071 Inexact Rounded
xrem101 remainder -78810.6297 -399884.68 -> -78810.6297
xsub101 subtract -78810.6297 -399884.68 -> 321074.050 Inexact Rounded
xadd102 add 409189761 -771.471460 -> 409188990 Inexact Rounded
xcom102 compare 409189761 -771.471460 -> 1
xdiv102 divide 409189761 -771.471460 -> -530401.683 Inexact Rounded
xdvi102 divideint 409189761 -771.471460 -> -530401
xmul102 multiply 409189761 -771.471460 -> -3.15678222E+11 Inexact Rounded
xpow102 power 409189761 -771 -> 1.60698414E-6640 Inexact Rounded
xrem102 remainder 409189761 -771.471460 -> 527.144540
xsub102 subtract 409189761 -771.471460 -> 409190532 Inexact Rounded
xadd103 add -1.68748838 460.46924 -> 458.781752 Inexact Rounded
xcom103 compare -1.68748838 460.46924 -> -1
xdiv103 divide -1.68748838 460.46924 -> -0.00366471467 Inexact Rounded
xdvi103 divideint -1.68748838 460.46924 -> -0
xmul103 multiply -1.68748838 460.46924 -> -777.036492 Inexact Rounded
xpow103 power -1.68748838 460 -> 3.39440648E+104 Inexact Rounded
xrem103 remainder -1.68748838 460.46924 -> -1.68748838
xsub103 subtract -1.68748838 460.46924 -> -462.156728 Inexact Rounded
xadd104 add 553527296. -7924.40185 -> 553519372 Inexact Rounded
xcom104 compare 553527296. -7924.40185 -> 1
xdiv104 divide 553527296. -7924.40185 -> -69850.9877 Inexact Rounded
xdvi104 divideint 553527296. -7924.40185 -> -69850
xmul104 multiply 553527296. -7924.40185 -> -4.38637273E+12 Inexact Rounded
xpow104 power 553527296. -7924 -> 2.32397214E-69281 Inexact Rounded
xrem104 remainder 553527296. -7924.40185 -> 7826.77750
xsub104 subtract 553527296. -7924.40185 -> 553535220 Inexact Rounded
xadd105 add -38.7465207 64936.2942 -> 64897.5477 Inexact Rounded
xcom105 compare -38.7465207 64936.2942 -> -1
xdiv105 divide -38.7465207 64936.2942 -> -0.000596685123 Inexact Rounded
xdvi105 divideint -38.7465207 64936.2942 -> -0
xmul105 multiply -38.7465207 64936.2942 -> -2516055.47 Inexact Rounded
xpow105 power -38.7465207 64936 -> 3.01500762E+103133 Inexact Rounded
xrem105 remainder -38.7465207 64936.2942 -> -38.7465207
xsub105 subtract -38.7465207 64936.2942 -> -64975.0407 Inexact Rounded
xadd106 add -201075.248 845.663928 -> -200229.584 Inexact Rounded
xcom106 compare -201075.248 845.663928 -> -1
xdiv106 divide -201075.248 845.663928 -> -237.772053 Inexact Rounded
xdvi106 divideint -201075.248 845.663928 -> -237
xmul106 multiply -201075.248 845.663928 -> -170042084 Inexact Rounded
xpow106 power -201075.248 846 -> 4.37911767E+4486 Inexact Rounded
xrem106 remainder -201075.248 845.663928 -> -652.897064
xsub106 subtract -201075.248 845.663928 -> -201920.912 Inexact Rounded
xadd107 add 91048.4559 75953609.3 -> 76044657.8 Inexact Rounded
xcom107 compare 91048.4559 75953609.3 -> -1
xdiv107 divide 91048.4559 75953609.3 -> 0.00119873771 Inexact Rounded
xdvi107 divideint 91048.4559 75953609.3 -> 0
xmul107 multiply 91048.4559 75953609.3 -> 6.91545885E+12 Inexact Rounded
xpow107 power 91048.4559 75953609 -> 6.94467746E+376674650 Inexact Rounded
xrem107 remainder 91048.4559 75953609.3 -> 91048.4559
xsub107 subtract 91048.4559 75953609.3 -> -75862560.8 Inexact Rounded
xadd108 add 6898273.86E-252097460 15.3456196 -> 15.3456196 Inexact Rounded
xcom108 compare 6898273.86E-252097460 15.3456196 -> -1
xdiv108 divide 6898273.86E-252097460 15.3456196 -> 4.49527229E-252097455 Inexact Rounded
xdvi108 divideint 6898273.86E-252097460 15.3456196 -> 0
xmul108 multiply 6898273.86E-252097460 15.3456196 -> 1.05858287E-252097452 Inexact Rounded
xpow108 power 6898273.86E-252097460 15 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem108 remainder 6898273.86E-252097460 15.3456196 -> 6.89827386E-252097454
xsub108 subtract 6898273.86E-252097460 15.3456196 -> -15.3456196 Inexact Rounded
xadd109 add 88.4370343 -980709105E-869899289 -> 88.4370343 Inexact Rounded
xcom109 compare 88.4370343 -980709105E-869899289 -> 1
xdiv109 divide 88.4370343 -980709105E-869899289 -> -9.01766220E+869899281 Inexact Rounded
xdvi109 divideint 88.4370343 -980709105E-869899289 -> NaN Division_impossible
xmul109 multiply 88.4370343 -980709105E-869899289 -> -8.67310048E-869899279 Inexact Rounded
xpow109 power 88.4370343 -10 -> 3.41710479E-20 Inexact Rounded
xrem109 remainder 88.4370343 -980709105E-869899289 -> NaN Division_impossible
xsub109 subtract 88.4370343 -980709105E-869899289 -> 88.4370343 Inexact Rounded
xadd110 add -17643.39 2.0352568E+304871331 -> 2.03525680E+304871331 Inexact Rounded
xcom110 compare -17643.39 2.0352568E+304871331 -> -1
xdiv110 divide -17643.39 2.0352568E+304871331 -> -8.66887658E-304871328 Inexact Rounded
xdvi110 divideint -17643.39 2.0352568E+304871331 -> -0
xmul110 multiply -17643.39 2.0352568E+304871331 -> -3.59088295E+304871335 Inexact Rounded
xpow110 power -17643.39 2 -> 311289211 Inexact Rounded
xrem110 remainder -17643.39 2.0352568E+304871331 -> -17643.39
xsub110 subtract -17643.39 2.0352568E+304871331 -> -2.03525680E+304871331 Inexact Rounded
xadd111 add 4589785.16 7459.04237 -> 4597244.20 Inexact Rounded
xcom111 compare 4589785.16 7459.04237 -> 1
xdiv111 divide 4589785.16 7459.04237 -> 615.331692 Inexact Rounded
xdvi111 divideint 4589785.16 7459.04237 -> 615
xmul111 multiply 4589785.16 7459.04237 -> 3.42354020E+10 Inexact Rounded
xpow111 power 4589785.16 7459 -> 2.03795258E+49690 Inexact Rounded
xrem111 remainder 4589785.16 7459.04237 -> 2474.10245
xsub111 subtract 4589785.16 7459.04237 -> 4582326.12 Inexact Rounded
xadd112 add -51.1632090E-753968082 8.96207471E-585797887 -> 8.96207471E-585797887 Inexact Rounded
xcom112 compare -51.1632090E-753968082 8.96207471E-585797887 -> -1
xdiv112 divide -51.1632090E-753968082 8.96207471E-585797887 -> -5.70885768E-168170195 Inexact Rounded
xdvi112 divideint -51.1632090E-753968082 8.96207471E-585797887 -> -0
xmul112 multiply -51.1632090E-753968082 8.96207471E-585797887 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xpow112 power -51.1632090E-753968082 9 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem112 remainder -51.1632090E-753968082 8.96207471E-585797887 -> -5.11632090E-753968081
xsub112 subtract -51.1632090E-753968082 8.96207471E-585797887 -> -8.96207471E-585797887 Inexact Rounded
xadd113 add 982.217817 7224909.4E-45243816 -> 982.217817 Inexact Rounded
xcom113 compare 982.217817 7224909.4E-45243816 -> 1
xdiv113 divide 982.217817 7224909.4E-45243816 -> 1.35948807E+45243812 Inexact Rounded
xdvi113 divideint 982.217817 7224909.4E-45243816 -> NaN Division_impossible
xmul113 multiply 982.217817 7224909.4E-45243816 -> 7.09643474E-45243807 Inexact Rounded
xpow113 power 982.217817 7 -> 8.81971709E+20 Inexact Rounded
xrem113 remainder 982.217817 7224909.4E-45243816 -> NaN Division_impossible
xsub113 subtract 982.217817 7224909.4E-45243816 -> 982.217817 Inexact Rounded
xadd114 add 503712056. -57490703.5E+924890183 -> -5.74907035E+924890190 Inexact Rounded
xcom114 compare 503712056. -57490703.5E+924890183 -> 1
xdiv114 divide 503712056. -57490703.5E+924890183 -> -8.76162623E-924890183 Inexact Rounded
xdvi114 divideint 503712056. -57490703.5E+924890183 -> -0
xmul114 multiply 503712056. -57490703.5E+924890183 -> -2.89587605E+924890199 Inexact Rounded
xpow114 power 503712056. -6 -> 6.12217764E-53 Inexact Rounded
xrem114 remainder 503712056. -57490703.5E+924890183 -> 503712056
xsub114 subtract 503712056. -57490703.5E+924890183 -> 5.74907035E+924890190 Inexact Rounded
xadd115 add 883.849223 249259171 -> 249260055 Inexact Rounded
xcom115 compare 883.849223 249259171 -> -1
xdiv115 divide 883.849223 249259171 -> 0.00000354590453 Inexact Rounded
xdvi115 divideint 883.849223 249259171 -> 0
xmul115 multiply 883.849223 249259171 -> 2.20307525E+11 Inexact Rounded
xpow115 power 883.849223 249259171 -> 5.15642844E+734411783 Inexact Rounded
xrem115 remainder 883.849223 249259171 -> 883.849223
xsub115 subtract 883.849223 249259171 -> -249258287 Inexact Rounded
xadd116 add 245.092853E+872642874 828195.152E+419771532 -> 2.45092853E+872642876 Inexact Rounded
xcom116 compare 245.092853E+872642874 828195.152E+419771532 -> 1
xdiv116 divide 245.092853E+872642874 828195.152E+419771532 -> 2.95936112E+452871338 Inexact Rounded
xdvi116 divideint 245.092853E+872642874 828195.152E+419771532 -> NaN Division_impossible
xmul116 multiply 245.092853E+872642874 828195.152E+419771532 -> Infinity Inexact Overflow Rounded
xpow116 power 245.092853E+872642874 8 -> Infinity Overflow Inexact Rounded
xrem116 remainder 245.092853E+872642874 828195.152E+419771532 -> NaN Division_impossible
xsub116 subtract 245.092853E+872642874 828195.152E+419771532 -> 2.45092853E+872642876 Inexact Rounded
xadd117 add -83658638.6E+728551928 2952478.42 -> -8.36586386E+728551935 Inexact Rounded
xcom117 compare -83658638.6E+728551928 2952478.42 -> -1
xdiv117 divide -83658638.6E+728551928 2952478.42 -> -2.83350551E+728551929 Inexact Rounded
xdvi117 divideint -83658638.6E+728551928 2952478.42 -> NaN Division_impossible
xmul117 multiply -83658638.6E+728551928 2952478.42 -> -2.47000325E+728551942 Inexact Rounded
xpow117 power -83658638.6E+728551928 2952478 -> Infinity Overflow Inexact Rounded
xrem117 remainder -83658638.6E+728551928 2952478.42 -> NaN Division_impossible
xsub117 subtract -83658638.6E+728551928 2952478.42 -> -8.36586386E+728551935 Inexact Rounded
xadd118 add -6291780.97 269967.394E-22000817 -> -6291780.97 Inexact Rounded
xcom118 compare -6291780.97 269967.394E-22000817 -> -1
xdiv118 divide -6291780.97 269967.394E-22000817 -> -2.33057069E+22000818 Inexact Rounded
xdvi118 divideint -6291780.97 269967.394E-22000817 -> NaN Division_impossible
xmul118 multiply -6291780.97 269967.394E-22000817 -> -1.69857571E-22000805 Inexact Rounded
xpow118 power -6291780.97 3 -> -2.49069636E+20 Inexact Rounded
xrem118 remainder -6291780.97 269967.394E-22000817 -> NaN Division_impossible
xsub118 subtract -6291780.97 269967.394E-22000817 -> -6291780.97 Inexact Rounded
xadd119 add 978571348.E+222382547 6006.19370 -> 9.78571348E+222382555 Inexact Rounded
xcom119 compare 978571348.E+222382547 6006.19370 -> 1
xdiv119 divide 978571348.E+222382547 6006.19370 -> 1.62927038E+222382552 Inexact Rounded
xdvi119 divideint 978571348.E+222382547 6006.19370 -> NaN Division_impossible
xmul119 multiply 978571348.E+222382547 6006.19370 -> 5.87748907E+222382559 Inexact Rounded
xpow119 power 978571348.E+222382547 6006 -> Infinity Overflow Inexact Rounded
xrem119 remainder 978571348.E+222382547 6006.19370 -> NaN Division_impossible
xsub119 subtract 978571348.E+222382547 6006.19370 -> 9.78571348E+222382555 Inexact Rounded
xadd120 add 14239029. -36527.2221 -> 14202501.8 Inexact Rounded
xcom120 compare 14239029. -36527.2221 -> 1
xdiv120 divide 14239029. -36527.2221 -> -389.819652 Inexact Rounded
xdvi120 divideint 14239029. -36527.2221 -> -389
xmul120 multiply 14239029. -36527.2221 -> -5.20112175E+11 Inexact Rounded
xpow120 power 14239029. -36527 -> 6.64292731E-261296 Inexact Rounded
xrem120 remainder 14239029. -36527.2221 -> 29939.6031
xsub120 subtract 14239029. -36527.2221 -> 14275556.2 Inexact Rounded
xadd121 add 72333.2654E-544425548 -690.664836E+662155120 -> -6.90664836E+662155122 Inexact Rounded
xcom121 compare 72333.2654E-544425548 -690.664836E+662155120 -> 1
xdiv121 divide 72333.2654E-544425548 -690.664836E+662155120 -> -0E-1000000007 Inexact Rounded Underflow Subnormal Clamped
xdvi121 divideint 72333.2654E-544425548 -690.664836E+662155120 -> -0
xmul121 multiply 72333.2654E-544425548 -690.664836E+662155120 -> -4.99580429E+117729579 Inexact Rounded
xpow121 power 72333.2654E-544425548 -7 -> Infinity Overflow Inexact Rounded
xrem121 remainder 72333.2654E-544425548 -690.664836E+662155120 -> 7.23332654E-544425544
xsub121 subtract 72333.2654E-544425548 -690.664836E+662155120 -> 6.90664836E+662155122 Inexact Rounded
xadd122 add -37721.1567E-115787341 -828949864E-76251747 -> -8.28949864E-76251739 Inexact Rounded
xcom122 compare -37721.1567E-115787341 -828949864E-76251747 -> 1
xdiv122 divide -37721.1567E-115787341 -828949864E-76251747 -> 4.55047505E-39535599 Inexact Rounded
xdvi122 divideint -37721.1567E-115787341 -828949864E-76251747 -> 0
xmul122 multiply -37721.1567E-115787341 -828949864E-76251747 -> 3.12689477E-192039075 Inexact Rounded
xpow122 power -37721.1567E-115787341 -8 -> 2.43960765E+926298691 Inexact Rounded
xrem122 remainder -37721.1567E-115787341 -828949864E-76251747 -> -3.77211567E-115787337
xsub122 subtract -37721.1567E-115787341 -828949864E-76251747 -> 8.28949864E-76251739 Inexact Rounded
xadd123 add -2078852.83E-647080089 -119779858.E+734665461 -> -1.19779858E+734665469 Inexact Rounded
xcom123 compare -2078852.83E-647080089 -119779858.E+734665461 -> 1
xdiv123 divide -2078852.83E-647080089 -119779858.E+734665461 -> 0E-1000000007 Inexact Rounded Underflow Subnormal Clamped
xdvi123 divideint -2078852.83E-647080089 -119779858.E+734665461 -> 0
xmul123 multiply -2078852.83E-647080089 -119779858.E+734665461 -> 2.49004697E+87585386 Inexact Rounded
xpow123 power -2078852.83E-647080089 -1 -> -4.81034533E+647080082 Inexact Rounded
xrem123 remainder -2078852.83E-647080089 -119779858.E+734665461 -> -2.07885283E-647080083
xsub123 subtract -2078852.83E-647080089 -119779858.E+734665461 -> 1.19779858E+734665469 Inexact Rounded
xadd124 add -79145.3625 -7718.57307 -> -86863.9356 Inexact Rounded
xcom124 compare -79145.3625 -7718.57307 -> -1
xdiv124 divide -79145.3625 -7718.57307 -> 10.2538852 Inexact Rounded
xdvi124 divideint -79145.3625 -7718.57307 -> 10
xmul124 multiply -79145.3625 -7718.57307 -> 610889264 Inexact Rounded
xpow124 power -79145.3625 -7719 -> -1.13181941E-37811 Inexact Rounded
xrem124 remainder -79145.3625 -7718.57307 -> -1959.63180
xsub124 subtract -79145.3625 -7718.57307 -> -71426.7894 Inexact Rounded
xadd125 add 2103890.49E+959247237 20024.3017 -> 2.10389049E+959247243 Inexact Rounded
xcom125 compare 2103890.49E+959247237 20024.3017 -> 1
xdiv125 divide 2103890.49E+959247237 20024.3017 -> 1.05066859E+959247239 Inexact Rounded
xdvi125 divideint 2103890.49E+959247237 20024.3017 -> NaN Division_impossible
xmul125 multiply 2103890.49E+959247237 20024.3017 -> 4.21289379E+959247247 Inexact Rounded
xpow125 power 2103890.49E+959247237 20024 -> Infinity Overflow Inexact Rounded
xrem125 remainder 2103890.49E+959247237 20024.3017 -> NaN Division_impossible
xsub125 subtract 2103890.49E+959247237 20024.3017 -> 2.10389049E+959247243 Inexact Rounded
xadd126 add 911249557 79810804.9 -> 991060362 Inexact Rounded
xcom126 compare 911249557 79810804.9 -> 1
xdiv126 divide 911249557 79810804.9 -> 11.4176214 Inexact Rounded
xdvi126 divideint 911249557 79810804.9 -> 11
xmul126 multiply 911249557 79810804.9 -> 7.27275606E+16 Inexact Rounded
xpow126 power 911249557 79810805 -> 6.77595741E+715075867 Inexact Rounded
xrem126 remainder 911249557 79810804.9 -> 33330703.1
xsub126 subtract 911249557 79810804.9 -> 831438752 Inexact Rounded
xadd127 add 341134.994 3.37486292 -> 341138.369 Inexact Rounded
xcom127 compare 341134.994 3.37486292 -> 1
xdiv127 divide 341134.994 3.37486292 -> 101081.141 Inexact Rounded
xdvi127 divideint 341134.994 3.37486292 -> 101081
xmul127 multiply 341134.994 3.37486292 -> 1151283.84 Inexact Rounded
xpow127 power 341134.994 3 -> 3.96989314E+16 Inexact Rounded
xrem127 remainder 341134.994 3.37486292 -> 0.47518348
xsub127 subtract 341134.994 3.37486292 -> 341131.619 Inexact Rounded
xadd128 add 244.23634 512706190E-341459836 -> 244.236340 Inexact Rounded
xcom128 compare 244.23634 512706190E-341459836 -> 1
xdiv128 divide 244.23634 512706190E-341459836 -> 4.76367059E+341459829 Inexact Rounded
xdvi128 divideint 244.23634 512706190E-341459836 -> NaN Division_impossible
xmul128 multiply 244.23634 512706190E-341459836 -> 1.25221483E-341459825 Inexact Rounded
xpow128 power 244.23634 5 -> 8.69063312E+11 Inexact Rounded
xrem128 remainder 244.23634 512706190E-341459836 -> NaN Division_impossible
xsub128 subtract 244.23634 512706190E-341459836 -> 244.236340 Inexact Rounded
xadd129 add -9.22783849E+171585954 -99.0946800 -> -9.22783849E+171585954 Inexact Rounded
xcom129 compare -9.22783849E+171585954 -99.0946800 -> -1
xdiv129 divide -9.22783849E+171585954 -99.0946800 -> 9.31214318E+171585952 Inexact Rounded
xdvi129 divideint -9.22783849E+171585954 -99.0946800 -> NaN Division_impossible
xmul129 multiply -9.22783849E+171585954 -99.0946800 -> 9.14429702E+171585956 Inexact Rounded
xpow129 power -9.22783849E+171585954 -99 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem129 remainder -9.22783849E+171585954 -99.0946800 -> NaN Division_impossible
xsub129 subtract -9.22783849E+171585954 -99.0946800 -> -9.22783849E+171585954 Inexact Rounded
xadd130 add 699631.893 -226.423958 -> 699405.469 Inexact Rounded
xcom130 compare 699631.893 -226.423958 -> 1
xdiv130 divide 699631.893 -226.423958 -> -3089.91990 Inexact Rounded
xdvi130 divideint 699631.893 -226.423958 -> -3089
xmul130 multiply 699631.893 -226.423958 -> -158413422 Inexact Rounded
xpow130 power 699631.893 -226 -> 1.14675511E-1321 Inexact Rounded
xrem130 remainder 699631.893 -226.423958 -> 208.286738
xsub130 subtract 699631.893 -226.423958 -> 699858.317 Inexact Rounded
xadd131 add -249350139.E-571793673 775732428. -> 775732428 Inexact Rounded
xcom131 compare -249350139.E-571793673 775732428. -> -1
xdiv131 divide -249350139.E-571793673 775732428. -> -3.21438334E-571793674 Inexact Rounded
xdvi131 divideint -249350139.E-571793673 775732428. -> -0
xmul131 multiply -249350139.E-571793673 775732428. -> -1.93428989E-571793656 Inexact Rounded
xpow131 power -249350139.E-571793673 775732428 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem131 remainder -249350139.E-571793673 775732428. -> -2.49350139E-571793665
xsub131 subtract -249350139.E-571793673 775732428. -> -775732428 Inexact Rounded
xadd132 add 5.11629020 -480.53194 -> -475.415650 Inexact Rounded
xcom132 compare 5.11629020 -480.53194 -> 1
xdiv132 divide 5.11629020 -480.53194 -> -0.0106471387 Inexact Rounded
xdvi132 divideint 5.11629020 -480.53194 -> -0
xmul132 multiply 5.11629020 -480.53194 -> -2458.54086 Inexact Rounded
xpow132 power 5.11629020 -481 -> 9.83021951E-342 Inexact Rounded
xrem132 remainder 5.11629020 -480.53194 -> 5.11629020
xsub132 subtract 5.11629020 -480.53194 -> 485.648230 Inexact Rounded
xadd133 add -8.23352673E-446723147 -530710.866 -> -530710.866 Inexact Rounded
xcom133 compare -8.23352673E-446723147 -530710.866 -> 1
xdiv133 divide -8.23352673E-446723147 -530710.866 -> 1.55141476E-446723152 Inexact Rounded
xdvi133 divideint -8.23352673E-446723147 -530710.866 -> 0
xmul133 multiply -8.23352673E-446723147 -530710.866 -> 4.36962210E-446723141 Inexact Rounded
xpow133 power -8.23352673E-446723147 -530711 -> -Infinity Overflow Inexact Rounded
xrem133 remainder -8.23352673E-446723147 -530710.866 -> -8.23352673E-446723147
xsub133 subtract -8.23352673E-446723147 -530710.866 -> 530710.866 Inexact Rounded
xadd134 add 7.0598608 -95908.35 -> -95901.2901 Inexact Rounded
xcom134 compare 7.0598608 -95908.35 -> 1
xdiv134 divide 7.0598608 -95908.35 -> -0.0000736104917 Inexact Rounded
xdvi134 divideint 7.0598608 -95908.35 -> -0
xmul134 multiply 7.0598608 -95908.35 -> -677099.601 Inexact Rounded
xpow134 power 7.0598608 -95908 -> 4.57073877E-81407 Inexact Rounded
xrem134 remainder 7.0598608 -95908.35 -> 7.0598608
xsub134 subtract 7.0598608 -95908.35 -> 95915.4099 Inexact Rounded
xadd135 add -7.91189845E+207202706 1532.71847E+509944335 -> 1.53271847E+509944338 Inexact Rounded
xcom135 compare -7.91189845E+207202706 1532.71847E+509944335 -> -1
xdiv135 divide -7.91189845E+207202706 1532.71847E+509944335 -> -5.16200372E-302741632 Inexact Rounded
xdvi135 divideint -7.91189845E+207202706 1532.71847E+509944335 -> -0
xmul135 multiply -7.91189845E+207202706 1532.71847E+509944335 -> -1.21267129E+717147045 Inexact Rounded
xpow135 power -7.91189845E+207202706 2 -> 6.25981371E+414405413 Inexact Rounded
xrem135 remainder -7.91189845E+207202706 1532.71847E+509944335 -> -7.91189845E+207202706
xsub135 subtract -7.91189845E+207202706 1532.71847E+509944335 -> -1.53271847E+509944338 Inexact Rounded
xadd136 add 208839370.E-215147432 -75.9420559 -> -75.9420559 Inexact Rounded
xcom136 compare 208839370.E-215147432 -75.9420559 -> 1
xdiv136 divide 208839370.E-215147432 -75.9420559 -> -2.74998310E-215147426 Inexact Rounded
xdvi136 divideint 208839370.E-215147432 -75.9420559 -> -0
xmul136 multiply 208839370.E-215147432 -75.9420559 -> -1.58596911E-215147422 Inexact Rounded
xpow136 power 208839370.E-215147432 -76 -> Infinity Overflow Inexact Rounded
xrem136 remainder 208839370.E-215147432 -75.9420559 -> 2.08839370E-215147424
xsub136 subtract 208839370.E-215147432 -75.9420559 -> 75.9420559 Inexact Rounded
xadd137 add 427.754244E-353328369 4705.0796 -> 4705.07960 Inexact Rounded
xcom137 compare 427.754244E-353328369 4705.0796 -> -1
xdiv137 divide 427.754244E-353328369 4705.0796 -> 9.09132853E-353328371 Inexact Rounded
xdvi137 divideint 427.754244E-353328369 4705.0796 -> 0
xmul137 multiply 427.754244E-353328369 4705.0796 -> 2.01261777E-353328363 Inexact Rounded
xpow137 power 427.754244E-353328369 4705 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem137 remainder 427.754244E-353328369 4705.0796 -> 4.27754244E-353328367
xsub137 subtract 427.754244E-353328369 4705.0796 -> -4705.07960 Inexact Rounded
xadd138 add 44911.089 -95.1733605E-313081848 -> 44911.0890 Inexact Rounded
xcom138 compare 44911.089 -95.1733605E-313081848 -> 1
xdiv138 divide 44911.089 -95.1733605E-313081848 -> -4.71887183E+313081850 Inexact Rounded
xdvi138 divideint 44911.089 -95.1733605E-313081848 -> NaN Division_impossible
xmul138 multiply 44911.089 -95.1733605E-313081848 -> -4.27433926E-313081842 Inexact Rounded
xpow138 power 44911.089 -10 -> 2.99546425E-47 Inexact Rounded
xrem138 remainder 44911.089 -95.1733605E-313081848 -> NaN Division_impossible
xsub138 subtract 44911.089 -95.1733605E-313081848 -> 44911.0890 Inexact Rounded
xadd139 add 452371821. -4109709.19 -> 448262112 Inexact Rounded
xcom139 compare 452371821. -4109709.19 -> 1
xdiv139 divide 452371821. -4109709.19 -> -110.073925 Inexact Rounded
xdvi139 divideint 452371821. -4109709.19 -> -110
xmul139 multiply 452371821. -4109709.19 -> -1.85911663E+15 Inexact Rounded
xpow139 power 452371821. -4109709 -> 1.15528807E-35571568 Inexact Rounded
xrem139 remainder 452371821. -4109709.19 -> 303810.10
xsub139 subtract 452371821. -4109709.19 -> 456481530 Inexact Rounded
xadd140 add 94007.4392 -9467725.5E+681898234 -> -9.46772550E+681898240 Inexact Rounded
xcom140 compare 94007.4392 -9467725.5E+681898234 -> 1
xdiv140 divide 94007.4392 -9467725.5E+681898234 -> -9.92925272E-681898237 Inexact Rounded
xdvi140 divideint 94007.4392 -9467725.5E+681898234 -> -0
xmul140 multiply 94007.4392 -9467725.5E+681898234 -> -8.90036629E+681898245 Inexact Rounded
xpow140 power 94007.4392 -9 -> 1.74397397E-45 Inexact Rounded
xrem140 remainder 94007.4392 -9467725.5E+681898234 -> 94007.4392
xsub140 subtract 94007.4392 -9467725.5E+681898234 -> 9.46772550E+681898240 Inexact Rounded
xadd141 add 99147554.0E-751410586 38313.6423 -> 38313.6423 Inexact Rounded
xcom141 compare 99147554.0E-751410586 38313.6423 -> -1
xdiv141 divide 99147554.0E-751410586 38313.6423 -> 2.58778722E-751410583 Inexact Rounded
xdvi141 divideint 99147554.0E-751410586 38313.6423 -> 0
xmul141 multiply 99147554.0E-751410586 38313.6423 -> 3.79870392E-751410574 Inexact Rounded
xpow141 power 99147554.0E-751410586 38314 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem141 remainder 99147554.0E-751410586 38313.6423 -> 9.91475540E-751410579
xsub141 subtract 99147554.0E-751410586 38313.6423 -> -38313.6423 Inexact Rounded
xadd142 add -7919.30254 -669.607854 -> -8588.91039 Inexact Rounded
xcom142 compare -7919.30254 -669.607854 -> -1
xdiv142 divide -7919.30254 -669.607854 -> 11.8267767 Inexact Rounded
xdvi142 divideint -7919.30254 -669.607854 -> 11
xmul142 multiply -7919.30254 -669.607854 -> 5302827.18 Inexact Rounded
xpow142 power -7919.30254 -670 -> 7.58147724E-2613 Inexact Rounded
xrem142 remainder -7919.30254 -669.607854 -> -553.616146
xsub142 subtract -7919.30254 -669.607854 -> -7249.69469 Inexact Rounded
xadd143 add 461.58280E+136110821 710666052.E-383754231 -> 4.61582800E+136110823 Inexact Rounded
xcom143 compare 461.58280E+136110821 710666052.E-383754231 -> 1
xdiv143 divide 461.58280E+136110821 710666052.E-383754231 -> 6.49507316E+519865045 Inexact Rounded
xdvi143 divideint 461.58280E+136110821 710666052.E-383754231 -> NaN Division_impossible
xmul143 multiply 461.58280E+136110821 710666052.E-383754231 -> 3.28031226E-247643399 Inexact Rounded
xpow143 power 461.58280E+136110821 7 -> 4.46423781E+952775765 Inexact Rounded
xrem143 remainder 461.58280E+136110821 710666052.E-383754231 -> NaN Division_impossible
xsub143 subtract 461.58280E+136110821 710666052.E-383754231 -> 4.61582800E+136110823 Inexact Rounded
xadd144 add 3455755.47E-112465506 771.674306 -> 771.674306 Inexact Rounded
xcom144 compare 3455755.47E-112465506 771.674306 -> -1
xdiv144 divide 3455755.47E-112465506 771.674306 -> 4.47825649E-112465503 Inexact Rounded
xdvi144 divideint 3455755.47E-112465506 771.674306 -> 0
xmul144 multiply 3455755.47E-112465506 771.674306 -> 2.66671770E-112465497 Inexact Rounded
xpow144 power 3455755.47E-112465506 772 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem144 remainder 3455755.47E-112465506 771.674306 -> 3.45575547E-112465500
xsub144 subtract 3455755.47E-112465506 771.674306 -> -771.674306 Inexact Rounded
xadd145 add -477067757.E-961684940 7.70122608E-741072245 -> 7.70122608E-741072245 Inexact Rounded
xcom145 compare -477067757.E-961684940 7.70122608E-741072245 -> -1
xdiv145 divide -477067757.E-961684940 7.70122608E-741072245 -> -6.19469877E-220612688 Inexact Rounded
xdvi145 divideint -477067757.E-961684940 7.70122608E-741072245 -> -0
xmul145 multiply -477067757.E-961684940 7.70122608E-741072245 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xpow145 power -477067757.E-961684940 8 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem145 remainder -477067757.E-961684940 7.70122608E-741072245 -> -4.77067757E-961684932
xsub145 subtract -477067757.E-961684940 7.70122608E-741072245 -> -7.70122608E-741072245 Inexact Rounded
xadd146 add 76482.352 8237806.8 -> 8314289.15 Inexact Rounded
xcom146 compare 76482.352 8237806.8 -> -1
xdiv146 divide 76482.352 8237806.8 -> 0.00928430999 Inexact Rounded
xdvi146 divideint 76482.352 8237806.8 -> 0
xmul146 multiply 76482.352 8237806.8 -> 6.30046839E+11 Inexact Rounded
xpow146 power 76482.352 8237807 -> 8.44216559E+40229834 Inexact Rounded
xrem146 remainder 76482.352 8237806.8 -> 76482.352
xsub146 subtract 76482.352 8237806.8 -> -8161324.45 Inexact Rounded
xadd147 add 1.21505164E-565556504 9.26146573 -> 9.26146573 Inexact Rounded
xcom147 compare 1.21505164E-565556504 9.26146573 -> -1
xdiv147 divide 1.21505164E-565556504 9.26146573 -> 1.31194314E-565556505 Inexact Rounded
xdvi147 divideint 1.21505164E-565556504 9.26146573 -> 0
xmul147 multiply 1.21505164E-565556504 9.26146573 -> 1.12531591E-565556503 Inexact Rounded
xpow147 power 1.21505164E-565556504 9 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem147 remainder 1.21505164E-565556504 9.26146573 -> 1.21505164E-565556504
xsub147 subtract 1.21505164E-565556504 9.26146573 -> -9.26146573 Inexact Rounded
xadd148 add -8303060.25E-169894883 901561.985 -> 901561.985 Inexact Rounded
xcom148 compare -8303060.25E-169894883 901561.985 -> -1
xdiv148 divide -8303060.25E-169894883 901561.985 -> -9.20963881E-169894883 Inexact Rounded
xdvi148 divideint -8303060.25E-169894883 901561.985 -> -0
xmul148 multiply -8303060.25E-169894883 901561.985 -> -7.48572348E-169894871 Inexact Rounded
xpow148 power -8303060.25E-169894883 901562 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem148 remainder -8303060.25E-169894883 901561.985 -> -8.30306025E-169894877
xsub148 subtract -8303060.25E-169894883 901561.985 -> -901561.985 Inexact Rounded
xadd149 add -592464.92 71445510.7 -> 70853045.8 Inexact Rounded
xcom149 compare -592464.92 71445510.7 -> -1
xdiv149 divide -592464.92 71445510.7 -> -0.00829254231 Inexact Rounded
xdvi149 divideint -592464.92 71445510.7 -> -0
xmul149 multiply -592464.92 71445510.7 -> -4.23289588E+13 Inexact Rounded
xpow149 power -592464.92 71445511 -> -1.58269108E+412430832 Inexact Rounded
xrem149 remainder -592464.92 71445510.7 -> -592464.92
xsub149 subtract -592464.92 71445510.7 -> -72037975.6 Inexact Rounded
xadd150 add -73774.4165 -39.8243027 -> -73814.2408 Inexact Rounded
xcom150 compare -73774.4165 -39.8243027 -> -1
xdiv150 divide -73774.4165 -39.8243027 -> 1852.49738 Inexact Rounded
xdvi150 divideint -73774.4165 -39.8243027 -> 1852
xmul150 multiply -73774.4165 -39.8243027 -> 2938014.69 Inexact Rounded
xpow150 power -73774.4165 -40 -> 1.92206765E-195 Inexact Rounded
xrem150 remainder -73774.4165 -39.8243027 -> -19.8078996
xsub150 subtract -73774.4165 -39.8243027 -> -73734.5922 Inexact Rounded
xadd151 add -524724715. -55763.7937 -> -524780479 Inexact Rounded
xcom151 compare -524724715. -55763.7937 -> -1
xdiv151 divide -524724715. -55763.7937 -> 9409.77434 Inexact Rounded
xdvi151 divideint -524724715. -55763.7937 -> 9409
xmul151 multiply -524724715. -55763.7937 -> 2.92606408E+13 Inexact Rounded
xpow151 power -524724715. -55764 -> 5.47898351E-486259 Inexact Rounded
xrem151 remainder -524724715. -55763.7937 -> -43180.0767
xsub151 subtract -524724715. -55763.7937 -> -524668951 Inexact Rounded
xadd152 add 7.53800427 784873768E-9981146 -> 7.53800427 Inexact Rounded
xcom152 compare 7.53800427 784873768E-9981146 -> 1
xdiv152 divide 7.53800427 784873768E-9981146 -> 9.60409760E+9981137 Inexact Rounded
xdvi152 divideint 7.53800427 784873768E-9981146 -> NaN Division_impossible
xmul152 multiply 7.53800427 784873768E-9981146 -> 5.91638181E-9981137 Inexact Rounded
xpow152 power 7.53800427 8 -> 10424399.2 Inexact Rounded
xrem152 remainder 7.53800427 784873768E-9981146 -> NaN Division_impossible
xsub152 subtract 7.53800427 784873768E-9981146 -> 7.53800427 Inexact Rounded
xadd153 add 37.6027452 7.22454233 -> 44.8272875 Inexact Rounded
xcom153 compare 37.6027452 7.22454233 -> 1
xdiv153 divide 37.6027452 7.22454233 -> 5.20486191 Inexact Rounded
xdvi153 divideint 37.6027452 7.22454233 -> 5
xmul153 multiply 37.6027452 7.22454233 -> 271.662624 Inexact Rounded
xpow153 power 37.6027452 7 -> 1.06300881E+11 Inexact Rounded
xrem153 remainder 37.6027452 7.22454233 -> 1.48003355
xsub153 subtract 37.6027452 7.22454233 -> 30.3782029 Inexact Rounded
xadd154 add 2447660.39 -36981.4253 -> 2410678.96 Inexact Rounded
xcom154 compare 2447660.39 -36981.4253 -> 1
xdiv154 divide 2447660.39 -36981.4253 -> -66.1862102 Inexact Rounded
xdvi154 divideint 2447660.39 -36981.4253 -> -66
xmul154 multiply 2447660.39 -36981.4253 -> -9.05179699E+10 Inexact Rounded
xpow154 power 2447660.39 -36981 -> 3.92066064E-236263 Inexact Rounded
xrem154 remainder 2447660.39 -36981.4253 -> 6886.3202
xsub154 subtract 2447660.39 -36981.4253 -> 2484641.82 Inexact Rounded
xadd155 add 2160.36419 1418.33574E+656265382 -> 1.41833574E+656265385 Inexact Rounded
xcom155 compare 2160.36419 1418.33574E+656265382 -> -1
xdiv155 divide 2160.36419 1418.33574E+656265382 -> 1.52316841E-656265382 Inexact Rounded
xdvi155 divideint 2160.36419 1418.33574E+656265382 -> 0
xmul155 multiply 2160.36419 1418.33574E+656265382 -> 3.06412174E+656265388 Inexact Rounded
xpow155 power 2160.36419 1 -> 2160.36419
xrem155 remainder 2160.36419 1418.33574E+656265382 -> 2160.36419
xsub155 subtract 2160.36419 1418.33574E+656265382 -> -1.41833574E+656265385 Inexact Rounded
xadd156 add 8926.44939 54.9430027 -> 8981.39239 Inexact Rounded
xcom156 compare 8926.44939 54.9430027 -> 1
xdiv156 divide 8926.44939 54.9430027 -> 162.467447 Inexact Rounded
xdvi156 divideint 8926.44939 54.9430027 -> 162
xmul156 multiply 8926.44939 54.9430027 -> 490445.933 Inexact Rounded
xpow156 power 8926.44939 55 -> 1.93789877E+217 Inexact Rounded
xrem156 remainder 8926.44939 54.9430027 -> 25.6829526
xsub156 subtract 8926.44939 54.9430027 -> 8871.50639 Inexact Rounded
xadd157 add 861588029 -41657398E+77955925 -> -4.16573980E+77955932 Inexact Rounded
xcom157 compare 861588029 -41657398E+77955925 -> 1
xdiv157 divide 861588029 -41657398E+77955925 -> -2.06827135E-77955924 Inexact Rounded
xdvi157 divideint 861588029 -41657398E+77955925 -> -0
xmul157 multiply 861588029 -41657398E+77955925 -> -3.58915154E+77955941 Inexact Rounded
xpow157 power 861588029 -4 -> 1.81468553E-36 Inexact Rounded
xrem157 remainder 861588029 -41657398E+77955925 -> 861588029
xsub157 subtract 861588029 -41657398E+77955925 -> 4.16573980E+77955932 Inexact Rounded
xadd158 add -34.5253062 52.6722019 -> 18.1468957
xcom158 compare -34.5253062 52.6722019 -> -1
xdiv158 divide -34.5253062 52.6722019 -> -0.655474899 Inexact Rounded
xdvi158 divideint -34.5253062 52.6722019 -> -0
xmul158 multiply -34.5253062 52.6722019 -> -1818.52390 Inexact Rounded
xpow158 power -34.5253062 53 -> -3.32115821E+81 Inexact Rounded
xrem158 remainder -34.5253062 52.6722019 -> -34.5253062
xsub158 subtract -34.5253062 52.6722019 -> -87.1975081
xadd159 add -18861647. 99794586.7 -> 80932939.7
xcom159 compare -18861647. 99794586.7 -> -1
xdiv159 divide -18861647. 99794586.7 -> -0.189004711 Inexact Rounded
xdvi159 divideint -18861647. 99794586.7 -> -0
xmul159 multiply -18861647. 99794586.7 -> -1.88229027E+15 Inexact Rounded
xpow159 power -18861647. 99794587 -> -4.28957459E+726063462 Inexact Rounded
xrem159 remainder -18861647. 99794586.7 -> -18861647.0
xsub159 subtract -18861647. 99794586.7 -> -118656234 Inexact Rounded
xadd160 add 322192.407 461.67044 -> 322654.077 Inexact Rounded
xcom160 compare 322192.407 461.67044 -> 1
xdiv160 divide 322192.407 461.67044 -> 697.883986 Inexact Rounded
xdvi160 divideint 322192.407 461.67044 -> 697
xmul160 multiply 322192.407 461.67044 -> 148746710 Inexact Rounded
xpow160 power 322192.407 462 -> 5.61395873E+2544 Inexact Rounded
xrem160 remainder 322192.407 461.67044 -> 408.11032
xsub160 subtract 322192.407 461.67044 -> 321730.737 Inexact Rounded
xadd161 add -896298518E+61412314 78873.8049 -> -8.96298518E+61412322 Inexact Rounded
xcom161 compare -896298518E+61412314 78873.8049 -> -1
xdiv161 divide -896298518E+61412314 78873.8049 -> -1.13637033E+61412318 Inexact Rounded
xdvi161 divideint -896298518E+61412314 78873.8049 -> NaN Division_impossible
xmul161 multiply -896298518E+61412314 78873.8049 -> -7.06944744E+61412327 Inexact Rounded
xpow161 power -896298518E+61412314 78874 -> Infinity Overflow Inexact Rounded
xrem161 remainder -896298518E+61412314 78873.8049 -> NaN Division_impossible
xsub161 subtract -896298518E+61412314 78873.8049 -> -8.96298518E+61412322 Inexact Rounded
xadd162 add 293.773732 479899052E+789950177 -> 4.79899052E+789950185 Inexact Rounded
xcom162 compare 293.773732 479899052E+789950177 -> -1
xdiv162 divide 293.773732 479899052E+789950177 -> 6.12157350E-789950184 Inexact Rounded
xdvi162 divideint 293.773732 479899052E+789950177 -> 0
xmul162 multiply 293.773732 479899052E+789950177 -> 1.40981735E+789950188 Inexact Rounded
xpow162 power 293.773732 5 -> 2.18808809E+12 Inexact Rounded
xrem162 remainder 293.773732 479899052E+789950177 -> 293.773732
xsub162 subtract 293.773732 479899052E+789950177 -> -4.79899052E+789950185 Inexact Rounded
xadd163 add -103519362 51897955.3 -> -51621406.7
xcom163 compare -103519362 51897955.3 -> -1
xdiv163 divide -103519362 51897955.3 -> -1.99467130 Inexact Rounded
xdvi163 divideint -103519362 51897955.3 -> -1
xmul163 multiply -103519362 51897955.3 -> -5.37244322E+15 Inexact Rounded
xpow163 power -103519362 51897955 -> -4.28858229E+415963229 Inexact Rounded
xrem163 remainder -103519362 51897955.3 -> -51621406.7
xsub163 subtract -103519362 51897955.3 -> -155417317 Inexact Rounded
xadd164 add 37380.7802 -277719788. -> -277682407 Inexact Rounded
xcom164 compare 37380.7802 -277719788. -> 1
xdiv164 divide 37380.7802 -277719788. -> -0.000134598908 Inexact Rounded
xdvi164 divideint 37380.7802 -277719788. -> -0
xmul164 multiply 37380.7802 -277719788. -> -1.03813824E+13 Inexact Rounded
xpow164 power 37380.7802 -277719788 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem164 remainder 37380.7802 -277719788. -> 37380.7802
xsub164 subtract 37380.7802 -277719788. -> 277757169 Inexact Rounded
xadd165 add 320133844. -977517477 -> -657383633
xcom165 compare 320133844. -977517477 -> 1
xdiv165 divide 320133844. -977517477 -> -0.327496798 Inexact Rounded
xdvi165 divideint 320133844. -977517477 -> -0
xmul165 multiply 320133844. -977517477 -> -3.12936427E+17 Inexact Rounded
xpow165 power 320133844. -977517477 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem165 remainder 320133844. -977517477 -> 320133844
xsub165 subtract 320133844. -977517477 -> 1.29765132E+9 Inexact Rounded
xadd166 add 721776701E+933646161 -5689279.64E+669903645 -> 7.21776701E+933646169 Inexact Rounded
xcom166 compare 721776701E+933646161 -5689279.64E+669903645 -> 1
xdiv166 divide 721776701E+933646161 -5689279.64E+669903645 -> -1.26866097E+263742518 Inexact Rounded
xdvi166 divideint 721776701E+933646161 -5689279.64E+669903645 -> NaN Division_impossible
xmul166 multiply 721776701E+933646161 -5689279.64E+669903645 -> -Infinity Inexact Overflow Rounded
xpow166 power 721776701E+933646161 -6 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem166 remainder 721776701E+933646161 -5689279.64E+669903645 -> NaN Division_impossible
xsub166 subtract 721776701E+933646161 -5689279.64E+669903645 -> 7.21776701E+933646169 Inexact Rounded
xadd167 add -5409.00482 -2.16149386 -> -5411.16631 Inexact Rounded
xcom167 compare -5409.00482 -2.16149386 -> -1
xdiv167 divide -5409.00482 -2.16149386 -> 2502.43821 Inexact Rounded
xdvi167 divideint -5409.00482 -2.16149386 -> 2502
xmul167 multiply -5409.00482 -2.16149386 -> 11691.5307 Inexact Rounded
xpow167 power -5409.00482 -2 -> 3.41794652E-8 Inexact Rounded
xrem167 remainder -5409.00482 -2.16149386 -> -0.94718228
xsub167 subtract -5409.00482 -2.16149386 -> -5406.84333 Inexact Rounded
xadd168 add -957960.367 322.858170 -> -957637.509 Inexact Rounded
xcom168 compare -957960.367 322.858170 -> -1
xdiv168 divide -957960.367 322.858170 -> -2967.12444 Inexact Rounded
xdvi168 divideint -957960.367 322.858170 -> -2967
xmul168 multiply -957960.367 322.858170 -> -309285331 Inexact Rounded
xpow168 power -957960.367 323 -> -9.44617460E+1931 Inexact Rounded
xrem168 remainder -957960.367 322.858170 -> -40.176610
xsub168 subtract -957960.367 322.858170 -> -958283.225 Inexact Rounded
xadd169 add -11817.8754E+613893442 -3.84735082E+888333249 -> -3.84735082E+888333249 Inexact Rounded
xcom169 compare -11817.8754E+613893442 -3.84735082E+888333249 -> 1
xdiv169 divide -11817.8754E+613893442 -3.84735082E+888333249 -> 3.07169165E-274439804 Inexact Rounded
xdvi169 divideint -11817.8754E+613893442 -3.84735082E+888333249 -> 0
xmul169 multiply -11817.8754E+613893442 -3.84735082E+888333249 -> Infinity Inexact Overflow Rounded
xpow169 power -11817.8754E+613893442 -4 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem169 remainder -11817.8754E+613893442 -3.84735082E+888333249 -> -1.18178754E+613893446
xsub169 subtract -11817.8754E+613893442 -3.84735082E+888333249 -> 3.84735082E+888333249 Inexact Rounded
xadd170 add 840258203 58363.980E-906584723 -> 840258203 Inexact Rounded
xcom170 compare 840258203 58363.980E-906584723 -> 1
xdiv170 divide 840258203 58363.980E-906584723 -> 1.43968626E+906584727 Inexact Rounded
xdvi170 divideint 840258203 58363.980E-906584723 -> NaN Division_impossible
xmul170 multiply 840258203 58363.980E-906584723 -> 4.90408130E-906584710 Inexact Rounded
xpow170 power 840258203 6 -> 3.51946431E+53 Inexact Rounded
xrem170 remainder 840258203 58363.980E-906584723 -> NaN Division_impossible
xsub170 subtract 840258203 58363.980E-906584723 -> 840258203 Inexact Rounded
xadd171 add -205842096. -191342.721 -> -206033439 Inexact Rounded
xcom171 compare -205842096. -191342.721 -> -1
xdiv171 divide -205842096. -191342.721 -> 1075.77699 Inexact Rounded
xdvi171 divideint -205842096. -191342.721 -> 1075
xmul171 multiply -205842096. -191342.721 -> 3.93863867E+13 Inexact Rounded
xpow171 power -205842096. -191343 -> -2.66955553E-1590737 Inexact Rounded
xrem171 remainder -205842096. -191342.721 -> -148670.925
xsub171 subtract -205842096. -191342.721 -> -205650753 Inexact Rounded
xadd172 add 42501124. 884.938498E+123341480 -> 8.84938498E+123341482 Inexact Rounded
xcom172 compare 42501124. 884.938498E+123341480 -> -1
xdiv172 divide 42501124. 884.938498E+123341480 -> 4.80272065E-123341476 Inexact Rounded
xdvi172 divideint 42501124. 884.938498E+123341480 -> 0
xmul172 multiply 42501124. 884.938498E+123341480 -> 3.76108808E+123341490 Inexact Rounded
xpow172 power 42501124. 9 -> 4.52484536E+68 Inexact Rounded
xrem172 remainder 42501124. 884.938498E+123341480 -> 42501124
xsub172 subtract 42501124. 884.938498E+123341480 -> -8.84938498E+123341482 Inexact Rounded
xadd173 add -57809452. -620380746 -> -678190198
xcom173 compare -57809452. -620380746 -> 1
xdiv173 divide -57809452. -620380746 -> 0.0931838268 Inexact Rounded
xdvi173 divideint -57809452. -620380746 -> 0
xmul173 multiply -57809452. -620380746 -> 3.58638710E+16 Inexact Rounded
xpow173 power -57809452. -620380746 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem173 remainder -57809452. -620380746 -> -57809452
xsub173 subtract -57809452. -620380746 -> 562571294
xadd174 add -8022370.31 9858581.6 -> 1836211.29
xcom174 compare -8022370.31 9858581.6 -> -1
xdiv174 divide -8022370.31 9858581.6 -> -0.813744881 Inexact Rounded
xdvi174 divideint -8022370.31 9858581.6 -> -0
xmul174 multiply -8022370.31 9858581.6 -> -7.90891923E+13 Inexact Rounded
xpow174 power -8022370.31 9858582 -> 2.34458249E+68066634 Inexact Rounded
xrem174 remainder -8022370.31 9858581.6 -> -8022370.31
xsub174 subtract -8022370.31 9858581.6 -> -17880951.9 Inexact Rounded
xadd175 add 2.49065060E+592139141 -5432.72014E-419965357 -> 2.49065060E+592139141 Inexact Rounded
xcom175 compare 2.49065060E+592139141 -5432.72014E-419965357 -> 1
xdiv175 divide 2.49065060E+592139141 -5432.72014E-419965357 -> -Infinity Inexact Overflow Rounded
xdvi175 divideint 2.49065060E+592139141 -5432.72014E-419965357 -> NaN Division_impossible
xmul175 multiply 2.49065060E+592139141 -5432.72014E-419965357 -> -1.35310077E+172173788 Inexact Rounded
xpow175 power 2.49065060E+592139141 -5 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem175 remainder 2.49065060E+592139141 -5432.72014E-419965357 -> NaN Division_impossible
xsub175 subtract 2.49065060E+592139141 -5432.72014E-419965357 -> 2.49065060E+592139141 Inexact Rounded
xadd176 add -697273715E-242824870 -3.81757506 -> -3.81757506 Inexact Rounded
xcom176 compare -697273715E-242824870 -3.81757506 -> 1
xdiv176 divide -697273715E-242824870 -3.81757506 -> 1.82648331E-242824862 Inexact Rounded
xdvi176 divideint -697273715E-242824870 -3.81757506 -> 0
xmul176 multiply -697273715E-242824870 -3.81757506 -> 2.66189474E-242824861 Inexact Rounded
xpow176 power -697273715E-242824870 -4 -> 4.23045251E+971299444 Inexact Rounded
xrem176 remainder -697273715E-242824870 -3.81757506 -> -6.97273715E-242824862
xsub176 subtract -697273715E-242824870 -3.81757506 -> 3.81757506 Inexact Rounded
xadd177 add -7.42204403E-315716280 -8156111.67E+283261636 -> -8.15611167E+283261642 Inexact Rounded
xcom177 compare -7.42204403E-315716280 -8156111.67E+283261636 -> 1
xdiv177 divide -7.42204403E-315716280 -8156111.67E+283261636 -> 9.09997843E-598977923 Inexact Rounded
xdvi177 divideint -7.42204403E-315716280 -8156111.67E+283261636 -> 0
xmul177 multiply -7.42204403E-315716280 -8156111.67E+283261636 -> 6.05350199E-32454637 Inexact Rounded
xpow177 power -7.42204403E-315716280 -8 -> Infinity Overflow Inexact Rounded
xrem177 remainder -7.42204403E-315716280 -8156111.67E+283261636 -> -7.42204403E-315716280
xsub177 subtract -7.42204403E-315716280 -8156111.67E+283261636 -> 8.15611167E+283261642 Inexact Rounded
xadd178 add 738063892 89900467.8 -> 827964360 Inexact Rounded
xcom178 compare 738063892 89900467.8 -> 1
xdiv178 divide 738063892 89900467.8 -> 8.20978923 Inexact Rounded
xdvi178 divideint 738063892 89900467.8 -> 8
xmul178 multiply 738063892 89900467.8 -> 6.63522892E+16 Inexact Rounded
xpow178 power 738063892 89900468 -> 1.53166723E+797245797 Inexact Rounded
xrem178 remainder 738063892 89900467.8 -> 18860149.6
xsub178 subtract 738063892 89900467.8 -> 648163424 Inexact Rounded
xadd179 add -630309366 -884783.338E-21595410 -> -630309366 Inexact Rounded
xcom179 compare -630309366 -884783.338E-21595410 -> -1
xdiv179 divide -630309366 -884783.338E-21595410 -> 7.12388377E+21595412 Inexact Rounded
xdvi179 divideint -630309366 -884783.338E-21595410 -> NaN Division_impossible
xmul179 multiply -630309366 -884783.338E-21595410 -> 5.57687225E-21595396 Inexact Rounded
xpow179 power -630309366 -9 -> -6.36819210E-80 Inexact Rounded
xrem179 remainder -630309366 -884783.338E-21595410 -> NaN Division_impossible
xsub179 subtract -630309366 -884783.338E-21595410 -> -630309366 Inexact Rounded
xadd180 add 613.207774 -3007.78608 -> -2394.57831 Inexact Rounded
xcom180 compare 613.207774 -3007.78608 -> 1
xdiv180 divide 613.207774 -3007.78608 -> -0.203873466 Inexact Rounded
xdvi180 divideint 613.207774 -3007.78608 -> -0
xmul180 multiply 613.207774 -3007.78608 -> -1844397.81 Inexact Rounded
xpow180 power 613.207774 -3008 -> 7.51939160E-8386 Inexact Rounded
xrem180 remainder 613.207774 -3007.78608 -> 613.207774
xsub180 subtract 613.207774 -3007.78608 -> 3620.99385 Inexact Rounded
xadd181 add -93006222.3 -3.08964619 -> -93006225.4 Inexact Rounded
xcom181 compare -93006222.3 -3.08964619 -> -1
xdiv181 divide -93006222.3 -3.08964619 -> 30102547.9 Inexact Rounded
xdvi181 divideint -93006222.3 -3.08964619 -> 30102547
xmul181 multiply -93006222.3 -3.08964619 -> 287356320 Inexact Rounded
xpow181 power -93006222.3 -3 -> -1.24297956E-24 Inexact Rounded
xrem181 remainder -93006222.3 -3.08964619 -> -2.65215407
xsub181 subtract -93006222.3 -3.08964619 -> -93006219.2 Inexact Rounded
xadd182 add -18116.0621 34096.306E-270347092 -> -18116.0621 Inexact Rounded
xcom182 compare -18116.0621 34096.306E-270347092 -> -1
xdiv182 divide -18116.0621 34096.306E-270347092 -> -5.31320375E+270347091 Inexact Rounded
xdvi182 divideint -18116.0621 34096.306E-270347092 -> NaN Division_impossible
xmul182 multiply -18116.0621 34096.306E-270347092 -> -6.17690797E-270347084 Inexact Rounded
xpow182 power -18116.0621 3 -> -5.94554133E+12 Inexact Rounded
xrem182 remainder -18116.0621 34096.306E-270347092 -> NaN Division_impossible
xsub182 subtract -18116.0621 34096.306E-270347092 -> -18116.0621 Inexact Rounded
xadd183 add 19272386.9 -410442379. -> -391169992 Inexact Rounded
xcom183 compare 19272386.9 -410442379. -> 1
xdiv183 divide 19272386.9 -410442379. -> -0.0469551584 Inexact Rounded
xdvi183 divideint 19272386.9 -410442379. -> -0
xmul183 multiply 19272386.9 -410442379. -> -7.91020433E+15 Inexact Rounded
xpow183 power 19272386.9 -410442379 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem183 remainder 19272386.9 -410442379. -> 19272386.9
xsub183 subtract 19272386.9 -410442379. -> 429714766 Inexact Rounded
xadd184 add 4180.30821 -1.6439543E-624759104 -> 4180.30821 Inexact Rounded
xcom184 compare 4180.30821 -1.6439543E-624759104 -> 1
xdiv184 divide 4180.30821 -1.6439543E-624759104 -> -2.54283724E+624759107 Inexact Rounded
xdvi184 divideint 4180.30821 -1.6439543E-624759104 -> NaN Division_impossible
xmul184 multiply 4180.30821 -1.6439543E-624759104 -> -6.87223566E-624759101 Inexact Rounded
xpow184 power 4180.30821 -2 -> 5.72246828E-8 Inexact Rounded
xrem184 remainder 4180.30821 -1.6439543E-624759104 -> NaN Division_impossible
xsub184 subtract 4180.30821 -1.6439543E-624759104 -> 4180.30821 Inexact Rounded
xadd185 add 571.536725 389.899220 -> 961.435945
xcom185 compare 571.536725 389.899220 -> 1
xdiv185 divide 571.536725 389.899220 -> 1.46585757 Inexact Rounded
xdvi185 divideint 571.536725 389.899220 -> 1
xmul185 multiply 571.536725 389.899220 -> 222841.723 Inexact Rounded
xpow185 power 571.536725 390 -> 1.76691373E+1075 Inexact Rounded
xrem185 remainder 571.536725 389.899220 -> 181.637505
xsub185 subtract 571.536725 389.899220 -> 181.637505
xadd186 add -622007306.E+159924886 -126.971745 -> -6.22007306E+159924894 Inexact Rounded
xcom186 compare -622007306.E+159924886 -126.971745 -> -1
xdiv186 divide -622007306.E+159924886 -126.971745 -> 4.89878521E+159924892 Inexact Rounded
xdvi186 divideint -622007306.E+159924886 -126.971745 -> NaN Division_impossible
xmul186 multiply -622007306.E+159924886 -126.971745 -> 7.89773530E+159924896 Inexact Rounded
xpow186 power -622007306.E+159924886 -127 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem186 remainder -622007306.E+159924886 -126.971745 -> NaN Division_impossible
xsub186 subtract -622007306.E+159924886 -126.971745 -> -6.22007306E+159924894 Inexact Rounded
xadd187 add -29.356551E-282816139 37141748E-903397821 -> -2.93565510E-282816138 Inexact Rounded
xcom187 compare -29.356551E-282816139 37141748E-903397821 -> -1
xdiv187 divide -29.356551E-282816139 37141748E-903397821 -> -7.90392283E+620581675 Inexact Rounded
xdvi187 divideint -29.356551E-282816139 37141748E-903397821 -> NaN Division_impossible
xmul187 multiply -29.356551E-282816139 37141748E-903397821 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xpow187 power -29.356551E-282816139 4 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem187 remainder -29.356551E-282816139 37141748E-903397821 -> NaN Division_impossible
xsub187 subtract -29.356551E-282816139 37141748E-903397821 -> -2.93565510E-282816138 Inexact Rounded
xadd188 add 92427442.4 674334898. -> 766762340 Inexact Rounded
xcom188 compare 92427442.4 674334898. -> -1
xdiv188 divide 92427442.4 674334898. -> 0.137064599 Inexact Rounded
xdvi188 divideint 92427442.4 674334898. -> 0
xmul188 multiply 92427442.4 674334898. -> 6.23270499E+16 Inexact Rounded
xpow188 power 92427442.4 674334898 -> Infinity Overflow Inexact Rounded
xrem188 remainder 92427442.4 674334898. -> 92427442.4
xsub188 subtract 92427442.4 674334898. -> -581907456 Inexact Rounded
xadd189 add 44651895.7 -910508.438 -> 43741387.3 Inexact Rounded
xcom189 compare 44651895.7 -910508.438 -> 1
xdiv189 divide 44651895.7 -910508.438 -> -49.0406171 Inexact Rounded
xdvi189 divideint 44651895.7 -910508.438 -> -49
xmul189 multiply 44651895.7 -910508.438 -> -4.06559278E+13 Inexact Rounded
xpow189 power 44651895.7 -910508 -> 3.72264277E-6965241 Inexact Rounded
xrem189 remainder 44651895.7 -910508.438 -> 36982.238
xsub189 subtract 44651895.7 -910508.438 -> 45562404.1 Inexact Rounded
xadd190 add 647897872.E+374021790 -467.423029 -> 6.47897872E+374021798 Inexact Rounded
xcom190 compare 647897872.E+374021790 -467.423029 -> 1
xdiv190 divide 647897872.E+374021790 -467.423029 -> -1.38610601E+374021796 Inexact Rounded
xdvi190 divideint 647897872.E+374021790 -467.423029 -> NaN Division_impossible
xmul190 multiply 647897872.E+374021790 -467.423029 -> -3.02842386E+374021801 Inexact Rounded
xpow190 power 647897872.E+374021790 -467 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem190 remainder 647897872.E+374021790 -467.423029 -> NaN Division_impossible
xsub190 subtract 647897872.E+374021790 -467.423029 -> 6.47897872E+374021798 Inexact Rounded
xadd191 add 25.2592149 59.0436981 -> 84.3029130
xcom191 compare 25.2592149 59.0436981 -> -1
xdiv191 divide 25.2592149 59.0436981 -> 0.427805434 Inexact Rounded
xdvi191 divideint 25.2592149 59.0436981 -> 0
xmul191 multiply 25.2592149 59.0436981 -> 1491.39746 Inexact Rounded
xpow191 power 25.2592149 59 -> 5.53058435E+82 Inexact Rounded
xrem191 remainder 25.2592149 59.0436981 -> 25.2592149
xsub191 subtract 25.2592149 59.0436981 -> -33.7844832
xadd192 add -6.850835 -1273.48240 -> -1280.33324 Inexact Rounded
xcom192 compare -6.850835 -1273.48240 -> 1
xdiv192 divide -6.850835 -1273.48240 -> 0.00537960713 Inexact Rounded
xdvi192 divideint -6.850835 -1273.48240 -> 0
xmul192 multiply -6.850835 -1273.48240 -> 8724.41780 Inexact Rounded
xpow192 power -6.850835 -1273 -> -1.25462678E-1064 Inexact Rounded
xrem192 remainder -6.850835 -1273.48240 -> -6.850835
xsub192 subtract -6.850835 -1273.48240 -> 1266.63157 Inexact Rounded
xadd193 add 174.272325 5638.16229 -> 5812.43462 Inexact Rounded
xcom193 compare 174.272325 5638.16229 -> -1
xdiv193 divide 174.272325 5638.16229 -> 0.0309094198 Inexact Rounded
xdvi193 divideint 174.272325 5638.16229 -> 0
xmul193 multiply 174.272325 5638.16229 -> 982575.651 Inexact Rounded
xpow193 power 174.272325 5638 -> 1.11137724E+12636 Inexact Rounded
xrem193 remainder 174.272325 5638.16229 -> 174.272325
xsub193 subtract 174.272325 5638.16229 -> -5463.88997 Inexact Rounded
xadd194 add 3455629.76 -8.27332322 -> 3455621.49 Inexact Rounded
xcom194 compare 3455629.76 -8.27332322 -> 1
xdiv194 divide 3455629.76 -8.27332322 -> -417683.399 Inexact Rounded
xdvi194 divideint 3455629.76 -8.27332322 -> -417683
xmul194 multiply 3455629.76 -8.27332322 -> -28589541.9 Inexact Rounded
xpow194 power 3455629.76 -8 -> 4.91793015E-53 Inexact Rounded
xrem194 remainder 3455629.76 -8.27332322 -> 3.29750074
xsub194 subtract 3455629.76 -8.27332322 -> 3455638.03 Inexact Rounded
xadd195 add -924337723E-640771235 86639377.1 -> 86639377.1 Inexact Rounded
xcom195 compare -924337723E-640771235 86639377.1 -> -1
xdiv195 divide -924337723E-640771235 86639377.1 -> -1.06687947E-640771234 Inexact Rounded
xdvi195 divideint -924337723E-640771235 86639377.1 -> -0
xmul195 multiply -924337723E-640771235 86639377.1 -> -8.00840446E-640771219 Inexact Rounded
xpow195 power -924337723E-640771235 86639377 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem195 remainder -924337723E-640771235 86639377.1 -> -9.24337723E-640771227
xsub195 subtract -924337723E-640771235 86639377.1 -> -86639377.1 Inexact Rounded
xadd196 add -620236932.E+656823969 3364722.73 -> -6.20236932E+656823977 Inexact Rounded
xcom196 compare -620236932.E+656823969 3364722.73 -> -1
xdiv196 divide -620236932.E+656823969 3364722.73 -> -1.84335228E+656823971 Inexact Rounded
xdvi196 divideint -620236932.E+656823969 3364722.73 -> NaN Division_impossible
xmul196 multiply -620236932.E+656823969 3364722.73 -> -2.08692530E+656823984 Inexact Rounded
xpow196 power -620236932.E+656823969 3364723 -> -Infinity Overflow Inexact Rounded
xrem196 remainder -620236932.E+656823969 3364722.73 -> NaN Division_impossible
xsub196 subtract -620236932.E+656823969 3364722.73 -> -6.20236932E+656823977 Inexact Rounded
xadd197 add 9.10025079 702777882E-8192234 -> 9.10025079 Inexact Rounded
xcom197 compare 9.10025079 702777882E-8192234 -> 1
xdiv197 divide 9.10025079 702777882E-8192234 -> 1.29489715E+8192226 Inexact Rounded
xdvi197 divideint 9.10025079 702777882E-8192234 -> NaN Division_impossible
xmul197 multiply 9.10025079 702777882E-8192234 -> 6.39545498E-8192225 Inexact Rounded
xpow197 power 9.10025079 7 -> 5168607.19 Inexact Rounded
xrem197 remainder 9.10025079 702777882E-8192234 -> NaN Division_impossible
xsub197 subtract 9.10025079 702777882E-8192234 -> 9.10025079 Inexact Rounded
xadd198 add -18857539.9 813013129. -> 794155589 Inexact Rounded
xcom198 compare -18857539.9 813013129. -> -1
xdiv198 divide -18857539.9 813013129. -> -0.0231946315 Inexact Rounded
xdvi198 divideint -18857539.9 813013129. -> -0
xmul198 multiply -18857539.9 813013129. -> -1.53314275E+16 Inexact Rounded
xpow198 power -18857539.9 813013129 -> -Infinity Overflow Inexact Rounded
xrem198 remainder -18857539.9 813013129. -> -18857539.9
xsub198 subtract -18857539.9 813013129. -> -831870669 Inexact Rounded
xadd199 add -8.29530327 3243419.57E+35688332 -> 3.24341957E+35688338 Inexact Rounded
xcom199 compare -8.29530327 3243419.57E+35688332 -> -1
xdiv199 divide -8.29530327 3243419.57E+35688332 -> -2.55757946E-35688338 Inexact Rounded
xdvi199 divideint -8.29530327 3243419.57E+35688332 -> -0
xmul199 multiply -8.29530327 3243419.57E+35688332 -> -2.69051490E+35688339 Inexact Rounded
xpow199 power -8.29530327 3 -> -570.816876 Inexact Rounded
xrem199 remainder -8.29530327 3243419.57E+35688332 -> -8.29530327
xsub199 subtract -8.29530327 3243419.57E+35688332 -> -3.24341957E+35688338 Inexact Rounded
xadd200 add -57101683.5 763551341E+991491712 -> 7.63551341E+991491720 Inexact Rounded
xcom200 compare -57101683.5 763551341E+991491712 -> -1
xdiv200 divide -57101683.5 763551341E+991491712 -> -7.47843405E-991491714 Inexact Rounded
xdvi200 divideint -57101683.5 763551341E+991491712 -> -0
xmul200 multiply -57101683.5 763551341E+991491712 -> -4.36000670E+991491728 Inexact Rounded
xpow200 power -57101683.5 8 -> 1.13029368E+62 Inexact Rounded
xrem200 remainder -57101683.5 763551341E+991491712 -> -57101683.5
xsub200 subtract -57101683.5 763551341E+991491712 -> -7.63551341E+991491720 Inexact Rounded
xadd201 add -603326.740 1710.95183 -> -601615.788 Inexact Rounded
xcom201 compare -603326.740 1710.95183 -> -1
xdiv201 divide -603326.740 1710.95183 -> -352.626374 Inexact Rounded
xdvi201 divideint -603326.740 1710.95183 -> -352
xmul201 multiply -603326.740 1710.95183 -> -1.03226299E+9 Inexact Rounded
xpow201 power -603326.740 1711 -> -3.35315976E+9890 Inexact Rounded
xrem201 remainder -603326.740 1710.95183 -> -1071.69584
xsub201 subtract -603326.740 1710.95183 -> -605037.692 Inexact Rounded
xadd202 add -48142763.3 -943434114 -> -991576877 Inexact Rounded
xcom202 compare -48142763.3 -943434114 -> 1
xdiv202 divide -48142763.3 -943434114 -> 0.0510292797 Inexact Rounded
xdvi202 divideint -48142763.3 -943434114 -> 0
xmul202 multiply -48142763.3 -943434114 -> 4.54195252E+16 Inexact Rounded
xpow202 power -48142763.3 -943434114 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem202 remainder -48142763.3 -943434114 -> -48142763.3
xsub202 subtract -48142763.3 -943434114 -> 895291351 Inexact Rounded
xadd203 add -204.586767 -235.531847 -> -440.118614
xcom203 compare -204.586767 -235.531847 -> 1
xdiv203 divide -204.586767 -235.531847 -> 0.868616154 Inexact Rounded
xdvi203 divideint -204.586767 -235.531847 -> 0
xmul203 multiply -204.586767 -235.531847 -> 48186.6991 Inexact Rounded
xpow203 power -204.586767 -236 -> 4.29438222E-546 Inexact Rounded
xrem203 remainder -204.586767 -235.531847 -> -204.586767
xsub203 subtract -204.586767 -235.531847 -> 30.945080
xadd204 add -70.3805581 830137.913 -> 830067.532 Inexact Rounded
xcom204 compare -70.3805581 830137.913 -> -1
xdiv204 divide -70.3805581 830137.913 -> -0.0000847817658 Inexact Rounded
xdvi204 divideint -70.3805581 830137.913 -> -0
xmul204 multiply -70.3805581 830137.913 -> -58425569.6 Inexact Rounded
xpow204 power -70.3805581 830138 -> 4.95165841E+1533640 Inexact Rounded
xrem204 remainder -70.3805581 830137.913 -> -70.3805581
xsub204 subtract -70.3805581 830137.913 -> -830208.294 Inexact Rounded
xadd205 add -8818.47606 -60766.4571 -> -69584.9332 Inexact Rounded
xcom205 compare -8818.47606 -60766.4571 -> 1
xdiv205 divide -8818.47606 -60766.4571 -> 0.145120787 Inexact Rounded
xdvi205 divideint -8818.47606 -60766.4571 -> 0
xmul205 multiply -8818.47606 -60766.4571 -> 535867547 Inexact Rounded
xpow205 power -8818.47606 -60766 -> 1.64487755E-239746 Inexact Rounded
xrem205 remainder -8818.47606 -60766.4571 -> -8818.47606
xsub205 subtract -8818.47606 -60766.4571 -> 51947.9810 Inexact Rounded
xadd206 add 37060929.3E-168439509 -79576717.1 -> -79576717.1 Inexact Rounded
xcom206 compare 37060929.3E-168439509 -79576717.1 -> 1
xdiv206 divide 37060929.3E-168439509 -79576717.1 -> -4.65725788E-168439510 Inexact Rounded
xdvi206 divideint 37060929.3E-168439509 -79576717.1 -> -0
xmul206 multiply 37060929.3E-168439509 -79576717.1 -> -2.94918709E-168439494 Inexact Rounded
xpow206 power 37060929.3E-168439509 -79576717 -> Infinity Overflow Inexact Rounded
xrem206 remainder 37060929.3E-168439509 -79576717.1 -> 3.70609293E-168439502
xsub206 subtract 37060929.3E-168439509 -79576717.1 -> 79576717.1 Inexact Rounded
xadd207 add -656285310. -107221462. -> -763506772
xcom207 compare -656285310. -107221462. -> -1
xdiv207 divide -656285310. -107221462. -> 6.12083904 Inexact Rounded
xdvi207 divideint -656285310. -107221462. -> 6
xmul207 multiply -656285310. -107221462. -> 7.03678704E+16 Inexact Rounded
xpow207 power -656285310. -107221462 -> 8.05338080E-945381569 Inexact Rounded
xrem207 remainder -656285310. -107221462. -> -12956538
xsub207 subtract -656285310. -107221462. -> -549063848
xadd208 add 653397.125 7195.30990 -> 660592.435 Inexact Rounded
xcom208 compare 653397.125 7195.30990 -> 1
xdiv208 divide 653397.125 7195.30990 -> 90.8087538 Inexact Rounded
xdvi208 divideint 653397.125 7195.30990 -> 90
xmul208 multiply 653397.125 7195.30990 -> 4.70139480E+9 Inexact Rounded
xpow208 power 653397.125 7195 -> 1.58522983E+41840 Inexact Rounded
xrem208 remainder 653397.125 7195.30990 -> 5819.23400
xsub208 subtract 653397.125 7195.30990 -> 646201.815 Inexact Rounded
xadd209 add 56221910.0E+857909374 -58.7247929 -> 5.62219100E+857909381 Inexact Rounded
xcom209 compare 56221910.0E+857909374 -58.7247929 -> 1
xdiv209 divide 56221910.0E+857909374 -58.7247929 -> -9.57379451E+857909379 Inexact Rounded
xdvi209 divideint 56221910.0E+857909374 -58.7247929 -> NaN Division_impossible
xmul209 multiply 56221910.0E+857909374 -58.7247929 -> -3.30162002E+857909383 Inexact Rounded
xpow209 power 56221910.0E+857909374 -59 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem209 remainder 56221910.0E+857909374 -58.7247929 -> NaN Division_impossible
xsub209 subtract 56221910.0E+857909374 -58.7247929 -> 5.62219100E+857909381 Inexact Rounded
xadd210 add 809862859E+643769974 -5.06784016 -> 8.09862859E+643769982 Inexact Rounded
xcom210 compare 809862859E+643769974 -5.06784016 -> 1
xdiv210 divide 809862859E+643769974 -5.06784016 -> -1.59804341E+643769982 Inexact Rounded
xdvi210 divideint 809862859E+643769974 -5.06784016 -> NaN Division_impossible
xmul210 multiply 809862859E+643769974 -5.06784016 -> -4.10425552E+643769983 Inexact Rounded
xpow210 power 809862859E+643769974 -5 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem210 remainder 809862859E+643769974 -5.06784016 -> NaN Division_impossible
xsub210 subtract 809862859E+643769974 -5.06784016 -> 8.09862859E+643769982 Inexact Rounded
xadd211 add -62011.4563E-117563240 -57.1731586E+115657204 -> -5.71731586E+115657205 Inexact Rounded
xcom211 compare -62011.4563E-117563240 -57.1731586E+115657204 -> 1
xdiv211 divide -62011.4563E-117563240 -57.1731586E+115657204 -> 1.08462534E-233220441 Inexact Rounded
xdvi211 divideint -62011.4563E-117563240 -57.1731586E+115657204 -> 0
xmul211 multiply -62011.4563E-117563240 -57.1731586E+115657204 -> 3.54539083E-1906030 Inexact Rounded
xpow211 power -62011.4563E-117563240 -6 -> 1.75860546E+705379411 Inexact Rounded
xrem211 remainder -62011.4563E-117563240 -57.1731586E+115657204 -> -6.20114563E-117563236
xsub211 subtract -62011.4563E-117563240 -57.1731586E+115657204 -> 5.71731586E+115657205 Inexact Rounded
xadd212 add 315.33351 91588.837E-536020149 -> 315.333510 Inexact Rounded
xcom212 compare 315.33351 91588.837E-536020149 -> 1
xdiv212 divide 315.33351 91588.837E-536020149 -> 3.44292515E+536020146 Inexact Rounded
xdvi212 divideint 315.33351 91588.837E-536020149 -> NaN Division_impossible
xmul212 multiply 315.33351 91588.837E-536020149 -> 2.88810294E-536020142 Inexact Rounded
xpow212 power 315.33351 9 -> 3.08269902E+22 Inexact Rounded
xrem212 remainder 315.33351 91588.837E-536020149 -> NaN Division_impossible
xsub212 subtract 315.33351 91588.837E-536020149 -> 315.333510 Inexact Rounded
xadd213 add 739.944710 202949.175 -> 203689.120 Inexact Rounded
xcom213 compare 739.944710 202949.175 -> -1
xdiv213 divide 739.944710 202949.175 -> 0.00364596067 Inexact Rounded
xdvi213 divideint 739.944710 202949.175 -> 0
xmul213 multiply 739.944710 202949.175 -> 150171168 Inexact Rounded
xpow213 power 739.944710 202949 -> 1.32611729E+582301 Inexact Rounded
xrem213 remainder 739.944710 202949.175 -> 739.944710
xsub213 subtract 739.944710 202949.175 -> -202209.230 Inexact Rounded
xadd214 add 87686.8016 4204890.40 -> 4292577.20 Inexact Rounded
xcom214 compare 87686.8016 4204890.40 -> -1
xdiv214 divide 87686.8016 4204890.40 -> 0.0208535285 Inexact Rounded
xdvi214 divideint 87686.8016 4204890.40 -> 0
xmul214 multiply 87686.8016 4204890.40 -> 3.68713390E+11 Inexact Rounded
xpow214 power 87686.8016 4204890 -> 5.14846981E+20784494 Inexact Rounded
xrem214 remainder 87686.8016 4204890.40 -> 87686.8016
xsub214 subtract 87686.8016 4204890.40 -> -4117203.60 Inexact Rounded
xadd215 add 987126721.E-725794834 4874166.23 -> 4874166.23 Inexact Rounded
xcom215 compare 987126721.E-725794834 4874166.23 -> -1
xdiv215 divide 987126721.E-725794834 4874166.23 -> 2.02522170E-725794832 Inexact Rounded
xdvi215 divideint 987126721.E-725794834 4874166.23 -> 0
xmul215 multiply 987126721.E-725794834 4874166.23 -> 4.81141973E-725794819 Inexact Rounded
xpow215 power 987126721.E-725794834 4874166 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem215 remainder 987126721.E-725794834 4874166.23 -> 9.87126721E-725794826
xsub215 subtract 987126721.E-725794834 4874166.23 -> -4874166.23 Inexact Rounded
xadd216 add 728148726.E-661695938 32798.5202 -> 32798.5202 Inexact Rounded
xcom216 compare 728148726.E-661695938 32798.5202 -> -1
xdiv216 divide 728148726.E-661695938 32798.5202 -> 2.22006579E-661695934 Inexact Rounded
xdvi216 divideint 728148726.E-661695938 32798.5202 -> 0
xmul216 multiply 728148726.E-661695938 32798.5202 -> 2.38822007E-661695925 Inexact Rounded
xpow216 power 728148726.E-661695938 32799 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem216 remainder 728148726.E-661695938 32798.5202 -> 7.28148726E-661695930
xsub216 subtract 728148726.E-661695938 32798.5202 -> -32798.5202 Inexact Rounded
xadd217 add 7428219.97 667.326760 -> 7428887.30 Inexact Rounded
xcom217 compare 7428219.97 667.326760 -> 1
xdiv217 divide 7428219.97 667.326760 -> 11131.3084 Inexact Rounded
xdvi217 divideint 7428219.97 667.326760 -> 11131
xmul217 multiply 7428219.97 667.326760 -> 4.95704997E+9 Inexact Rounded
xpow217 power 7428219.97 667 -> 7.58808509E+4582 Inexact Rounded
xrem217 remainder 7428219.97 667.326760 -> 205.804440
xsub217 subtract 7428219.97 667.326760 -> 7427552.64 Inexact Rounded
xadd218 add -7291.19212 209.64966E-588526476 -> -7291.19212 Inexact Rounded
xcom218 compare -7291.19212 209.64966E-588526476 -> -1
xdiv218 divide -7291.19212 209.64966E-588526476 -> -3.47779821E+588526477 Inexact Rounded
xdvi218 divideint -7291.19212 209.64966E-588526476 -> NaN Division_impossible
xmul218 multiply -7291.19212 209.64966E-588526476 -> -1.52859595E-588526470 Inexact Rounded
xpow218 power -7291.19212 2 -> 53161482.5 Inexact Rounded
xrem218 remainder -7291.19212 209.64966E-588526476 -> NaN Division_impossible
xsub218 subtract -7291.19212 209.64966E-588526476 -> -7291.19212 Inexact Rounded
xadd219 add -358.24550 -4447.78675E+601402509 -> -4.44778675E+601402512 Inexact Rounded
xcom219 compare -358.24550 -4447.78675E+601402509 -> 1
xdiv219 divide -358.24550 -4447.78675E+601402509 -> 8.05446664E-601402511 Inexact Rounded
xdvi219 divideint -358.24550 -4447.78675E+601402509 -> 0
xmul219 multiply -358.24550 -4447.78675E+601402509 -> 1.59339959E+601402515 Inexact Rounded
xpow219 power -358.24550 -4 -> 6.07123474E-11 Inexact Rounded
xrem219 remainder -358.24550 -4447.78675E+601402509 -> -358.24550
xsub219 subtract -358.24550 -4447.78675E+601402509 -> 4.44778675E+601402512 Inexact Rounded
xadd220 add 118.621826 -2.72010038 -> 115.901726 Inexact Rounded
xcom220 compare 118.621826 -2.72010038 -> 1
xdiv220 divide 118.621826 -2.72010038 -> -43.6093561 Inexact Rounded
xdvi220 divideint 118.621826 -2.72010038 -> -43
xmul220 multiply 118.621826 -2.72010038 -> -322.663274 Inexact Rounded
xpow220 power 118.621826 -3 -> 5.99109471E-7 Inexact Rounded
xrem220 remainder 118.621826 -2.72010038 -> 1.65750966
xsub220 subtract 118.621826 -2.72010038 -> 121.341926 Inexact Rounded
xadd221 add 8071961.94 -135533740.E-102451543 -> 8071961.94 Inexact Rounded
xcom221 compare 8071961.94 -135533740.E-102451543 -> 1
xdiv221 divide 8071961.94 -135533740.E-102451543 -> -5.95568450E+102451541 Inexact Rounded
xdvi221 divideint 8071961.94 -135533740.E-102451543 -> NaN Division_impossible
xmul221 multiply 8071961.94 -135533740.E-102451543 -> -1.09402319E-102451528 Inexact Rounded
xpow221 power 8071961.94 -1 -> 1.23885619E-7 Inexact Rounded
xrem221 remainder 8071961.94 -135533740.E-102451543 -> NaN Division_impossible
xsub221 subtract 8071961.94 -135533740.E-102451543 -> 8071961.94 Inexact Rounded
xadd222 add 64262528.5E+812118682 -8692.94447E-732186947 -> 6.42625285E+812118689 Inexact Rounded
xcom222 compare 64262528.5E+812118682 -8692.94447E-732186947 -> 1
xdiv222 divide 64262528.5E+812118682 -8692.94447E-732186947 -> -Infinity Inexact Overflow Rounded
xdvi222 divideint 64262528.5E+812118682 -8692.94447E-732186947 -> NaN Division_impossible
xmul222 multiply 64262528.5E+812118682 -8692.94447E-732186947 -> -5.58630592E+79931746 Inexact Rounded
xpow222 power 64262528.5E+812118682 -9 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem222 remainder 64262528.5E+812118682 -8692.94447E-732186947 -> NaN Division_impossible
xsub222 subtract 64262528.5E+812118682 -8692.94447E-732186947 -> 6.42625285E+812118689 Inexact Rounded
xadd223 add -35544.4029 -567830.130 -> -603374.533 Inexact Rounded
xcom223 compare -35544.4029 -567830.130 -> 1
xdiv223 divide -35544.4029 -567830.130 -> 0.0625968948 Inexact Rounded
xdvi223 divideint -35544.4029 -567830.130 -> 0
xmul223 multiply -35544.4029 -567830.130 -> 2.01831829E+10 Inexact Rounded
xpow223 power -35544.4029 -567830 -> 3.77069368E-2584065 Inexact Rounded
xrem223 remainder -35544.4029 -567830.130 -> -35544.4029
xsub223 subtract -35544.4029 -567830.130 -> 532285.727 Inexact Rounded
xadd224 add -7.16513047E+59297103 87767.8211 -> -7.16513047E+59297103 Inexact Rounded
xcom224 compare -7.16513047E+59297103 87767.8211 -> -1
xdiv224 divide -7.16513047E+59297103 87767.8211 -> -8.16373288E+59297098 Inexact Rounded
xdvi224 divideint -7.16513047E+59297103 87767.8211 -> NaN Division_impossible
xmul224 multiply -7.16513047E+59297103 87767.8211 -> -6.28867889E+59297108 Inexact Rounded
xpow224 power -7.16513047E+59297103 87768 -> Infinity Overflow Inexact Rounded
xrem224 remainder -7.16513047E+59297103 87767.8211 -> NaN Division_impossible
xsub224 subtract -7.16513047E+59297103 87767.8211 -> -7.16513047E+59297103 Inexact Rounded
xadd225 add -509.483395 -147242915. -> -147243424 Inexact Rounded
xcom225 compare -509.483395 -147242915. -> 1
xdiv225 divide -509.483395 -147242915. -> 0.00000346015559 Inexact Rounded
xdvi225 divideint -509.483395 -147242915. -> 0
xmul225 multiply -509.483395 -147242915. -> 7.50178202E+10 Inexact Rounded
xpow225 power -509.483395 -147242915 -> -3.10760519E-398605718 Inexact Rounded
xrem225 remainder -509.483395 -147242915. -> -509.483395
xsub225 subtract -509.483395 -147242915. -> 147242406 Inexact Rounded
xadd226 add -7919047.28E+956041629 -367667329 -> -7.91904728E+956041635 Inexact Rounded
xcom226 compare -7919047.28E+956041629 -367667329 -> -1
xdiv226 divide -7919047.28E+956041629 -367667329 -> 2.15386211E+956041627 Inexact Rounded
xdvi226 divideint -7919047.28E+956041629 -367667329 -> NaN Division_impossible
xmul226 multiply -7919047.28E+956041629 -367667329 -> 2.91157496E+956041644 Inexact Rounded
xpow226 power -7919047.28E+956041629 -367667329 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem226 remainder -7919047.28E+956041629 -367667329 -> NaN Division_impossible
xsub226 subtract -7919047.28E+956041629 -367667329 -> -7.91904728E+956041635 Inexact Rounded
xadd227 add 895612630. -36.4104040 -> 895612594 Inexact Rounded
xcom227 compare 895612630. -36.4104040 -> 1
xdiv227 divide 895612630. -36.4104040 -> -24597712.0 Inexact Rounded
xdvi227 divideint 895612630. -36.4104040 -> -24597711
xmul227 multiply 895612630. -36.4104040 -> -3.26096177E+10 Inexact Rounded
xpow227 power 895612630. -36 -> 5.29264130E-323 Inexact Rounded
xrem227 remainder 895612630. -36.4104040 -> 35.0147560
xsub227 subtract 895612630. -36.4104040 -> 895612666 Inexact Rounded
xadd228 add 25455.4973 2955.00006E+528196218 -> 2.95500006E+528196221 Inexact Rounded
xcom228 compare 25455.4973 2955.00006E+528196218 -> -1
xdiv228 divide 25455.4973 2955.00006E+528196218 -> 8.61438131E-528196218 Inexact Rounded
xdvi228 divideint 25455.4973 2955.00006E+528196218 -> 0
xmul228 multiply 25455.4973 2955.00006E+528196218 -> 7.52209960E+528196225 Inexact Rounded
xpow228 power 25455.4973 3 -> 1.64947128E+13 Inexact Rounded
xrem228 remainder 25455.4973 2955.00006E+528196218 -> 25455.4973
xsub228 subtract 25455.4973 2955.00006E+528196218 -> -2.95500006E+528196221 Inexact Rounded
xadd229 add -112.294144E+273414172 -71448007.7 -> -1.12294144E+273414174 Inexact Rounded
xcom229 compare -112.294144E+273414172 -71448007.7 -> -1
xdiv229 divide -112.294144E+273414172 -71448007.7 -> 1.57169035E+273414166 Inexact Rounded
xdvi229 divideint -112.294144E+273414172 -71448007.7 -> NaN Division_impossible
xmul229 multiply -112.294144E+273414172 -71448007.7 -> 8.02319287E+273414181 Inexact Rounded
xpow229 power -112.294144E+273414172 -71448008 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem229 remainder -112.294144E+273414172 -71448007.7 -> NaN Division_impossible
xsub229 subtract -112.294144E+273414172 -71448007.7 -> -1.12294144E+273414174 Inexact Rounded
xadd230 add 62871.2202 2484.0382E+211662557 -> 2.48403820E+211662560 Inexact Rounded
xcom230 compare 62871.2202 2484.0382E+211662557 -> -1
xdiv230 divide 62871.2202 2484.0382E+211662557 -> 2.53100859E-211662556 Inexact Rounded
xdvi230 divideint 62871.2202 2484.0382E+211662557 -> 0
xmul230 multiply 62871.2202 2484.0382E+211662557 -> 1.56174513E+211662565 Inexact Rounded
xpow230 power 62871.2202 2 -> 3.95279033E+9 Inexact Rounded
xrem230 remainder 62871.2202 2484.0382E+211662557 -> 62871.2202
xsub230 subtract 62871.2202 2484.0382E+211662557 -> -2.48403820E+211662560 Inexact Rounded
xadd231 add 71.9281575 -9810012.5 -> -9809940.57 Inexact Rounded
xcom231 compare 71.9281575 -9810012.5 -> 1
xdiv231 divide 71.9281575 -9810012.5 -> -0.00000733211680 Inexact Rounded
xdvi231 divideint 71.9281575 -9810012.5 -> -0
xmul231 multiply 71.9281575 -9810012.5 -> -705616124 Inexact Rounded
xpow231 power 71.9281575 -9810013 -> 2.00363798E-18216203 Inexact Rounded
xrem231 remainder 71.9281575 -9810012.5 -> 71.9281575
xsub231 subtract 71.9281575 -9810012.5 -> 9810084.43 Inexact Rounded
xadd232 add -6388022. -88.042967 -> -6388110.04 Inexact Rounded
xcom232 compare -6388022. -88.042967 -> -1
xdiv232 divide -6388022. -88.042967 -> 72555.7329 Inexact Rounded
xdvi232 divideint -6388022. -88.042967 -> 72555
xmul232 multiply -6388022. -88.042967 -> 562420410 Inexact Rounded
xpow232 power -6388022. -88 -> 1.34201238E-599 Inexact Rounded
xrem232 remainder -6388022. -88.042967 -> -64.529315
xsub232 subtract -6388022. -88.042967 -> -6387933.96 Inexact Rounded
xadd233 add 372567445. 96.0992141 -> 372567541 Inexact Rounded
xcom233 compare 372567445. 96.0992141 -> 1
xdiv233 divide 372567445. 96.0992141 -> 3876904.18 Inexact Rounded
xdvi233 divideint 372567445. 96.0992141 -> 3876904
xmul233 multiply 372567445. 96.0992141 -> 3.58034387E+10 Inexact Rounded
xpow233 power 372567445. 96 -> 6.84968715E+822 Inexact Rounded
xrem233 remainder 372567445. 96.0992141 -> 17.4588536
xsub233 subtract 372567445. 96.0992141 -> 372567349 Inexact Rounded
xadd234 add 802.156517 -174409310.E-255338020 -> 802.156517 Inexact Rounded
xcom234 compare 802.156517 -174409310.E-255338020 -> 1
xdiv234 divide 802.156517 -174409310.E-255338020 -> -4.59927579E+255338014 Inexact Rounded
xdvi234 divideint 802.156517 -174409310.E-255338020 -> NaN Division_impossible
xmul234 multiply 802.156517 -174409310.E-255338020 -> -1.39903565E-255338009 Inexact Rounded
xpow234 power 802.156517 -2 -> 0.00000155411005 Inexact Rounded
xrem234 remainder 802.156517 -174409310.E-255338020 -> NaN Division_impossible
xsub234 subtract 802.156517 -174409310.E-255338020 -> 802.156517 Inexact Rounded
xadd235 add -3.65207541 74501982.0 -> 74501978.3 Inexact Rounded
xcom235 compare -3.65207541 74501982.0 -> -1
xdiv235 divide -3.65207541 74501982.0 -> -4.90198423E-8 Inexact Rounded
xdvi235 divideint -3.65207541 74501982.0 -> -0
xmul235 multiply -3.65207541 74501982.0 -> -272086856 Inexact Rounded
xpow235 power -3.65207541 74501982 -> 2.10339452E+41910325 Inexact Rounded
xrem235 remainder -3.65207541 74501982.0 -> -3.65207541
xsub235 subtract -3.65207541 74501982.0 -> -74501985.7 Inexact Rounded
xadd236 add -5297.76981 -859.719404 -> -6157.48921 Inexact Rounded
xcom236 compare -5297.76981 -859.719404 -> -1
xdiv236 divide -5297.76981 -859.719404 -> 6.16220802 Inexact Rounded
xdvi236 divideint -5297.76981 -859.719404 -> 6
xmul236 multiply -5297.76981 -859.719404 -> 4554595.50 Inexact Rounded
xpow236 power -5297.76981 -860 -> 1.90523108E-3203 Inexact Rounded
xrem236 remainder -5297.76981 -859.719404 -> -139.453386
xsub236 subtract -5297.76981 -859.719404 -> -4438.05041 Inexact Rounded
xadd237 add -684172.592 766.448597E+288361959 -> 7.66448597E+288361961 Inexact Rounded
xcom237 compare -684172.592 766.448597E+288361959 -> -1
xdiv237 divide -684172.592 766.448597E+288361959 -> -8.92652938E-288361957 Inexact Rounded
xdvi237 divideint -684172.592 766.448597E+288361959 -> -0
xmul237 multiply -684172.592 766.448597E+288361959 -> -5.24383123E+288361967 Inexact Rounded
xpow237 power -684172.592 8 -> 4.80093005E+46 Inexact Rounded
xrem237 remainder -684172.592 766.448597E+288361959 -> -684172.592
xsub237 subtract -684172.592 766.448597E+288361959 -> -7.66448597E+288361961 Inexact Rounded
xadd238 add 626919.219 57469.8727E+13188610 -> 5.74698727E+13188614 Inexact Rounded
xcom238 compare 626919.219 57469.8727E+13188610 -> -1
xdiv238 divide 626919.219 57469.8727E+13188610 -> 1.09086586E-13188609 Inexact Rounded
xdvi238 divideint 626919.219 57469.8727E+13188610 -> 0
xmul238 multiply 626919.219 57469.8727E+13188610 -> 3.60289677E+13188620 Inexact Rounded
xpow238 power 626919.219 6 -> 6.07112959E+34 Inexact Rounded
xrem238 remainder 626919.219 57469.8727E+13188610 -> 626919.219
xsub238 subtract 626919.219 57469.8727E+13188610 -> -5.74698727E+13188614 Inexact Rounded
xadd239 add -77480.5840 893265.594E+287982552 -> 8.93265594E+287982557 Inexact Rounded
xcom239 compare -77480.5840 893265.594E+287982552 -> -1
xdiv239 divide -77480.5840 893265.594E+287982552 -> -8.67385742E-287982554 Inexact Rounded
xdvi239 divideint -77480.5840 893265.594E+287982552 -> -0
xmul239 multiply -77480.5840 893265.594E+287982552 -> -6.92107399E+287982562 Inexact Rounded
xpow239 power -77480.5840 9 -> -1.00631969E+44 Inexact Rounded
xrem239 remainder -77480.5840 893265.594E+287982552 -> -77480.5840
xsub239 subtract -77480.5840 893265.594E+287982552 -> -8.93265594E+287982557 Inexact Rounded
xadd240 add -7177620.29 7786343.83 -> 608723.54
xcom240 compare -7177620.29 7786343.83 -> -1
xdiv240 divide -7177620.29 7786343.83 -> -0.921821647 Inexact Rounded
xdvi240 divideint -7177620.29 7786343.83 -> -0
xmul240 multiply -7177620.29 7786343.83 -> -5.58874195E+13 Inexact Rounded
xpow240 power -7177620.29 7786344 -> 2.96037074E+53383022 Inexact Rounded
xrem240 remainder -7177620.29 7786343.83 -> -7177620.29
xsub240 subtract -7177620.29 7786343.83 -> -14963964.1 Inexact Rounded
xadd241 add 9.6224130 4.50355112 -> 14.1259641 Inexact Rounded
xcom241 compare 9.6224130 4.50355112 -> 1
xdiv241 divide 9.6224130 4.50355112 -> 2.13662791 Inexact Rounded
xdvi241 divideint 9.6224130 4.50355112 -> 2
xmul241 multiply 9.6224130 4.50355112 -> 43.3350288 Inexact Rounded
xpow241 power 9.6224130 5 -> 82493.5448 Inexact Rounded
xrem241 remainder 9.6224130 4.50355112 -> 0.61531076
xsub241 subtract 9.6224130 4.50355112 -> 5.11886188
xadd242 add -66.6337347E-597410086 -818812885 -> -818812885 Inexact Rounded
xcom242 compare -66.6337347E-597410086 -818812885 -> 1
xdiv242 divide -66.6337347E-597410086 -818812885 -> 8.13784638E-597410094 Inexact Rounded
xdvi242 divideint -66.6337347E-597410086 -818812885 -> 0
xmul242 multiply -66.6337347E-597410086 -818812885 -> 5.45605605E-597410076 Inexact Rounded
xpow242 power -66.6337347E-597410086 -818812885 -> -Infinity Overflow Inexact Rounded
xrem242 remainder -66.6337347E-597410086 -818812885 -> -6.66337347E-597410085
xsub242 subtract -66.6337347E-597410086 -818812885 -> 818812885 Inexact Rounded
xadd243 add 65587553.7 600574.736 -> 66188128.4 Inexact Rounded
xcom243 compare 65587553.7 600574.736 -> 1
xdiv243 divide 65587553.7 600574.736 -> 109.207980 Inexact Rounded
xdvi243 divideint 65587553.7 600574.736 -> 109
xmul243 multiply 65587553.7 600574.736 -> 3.93902277E+13 Inexact Rounded
xpow243 power 65587553.7 600575 -> 3.40404817E+4694587 Inexact Rounded
xrem243 remainder 65587553.7 600574.736 -> 124907.476
xsub243 subtract 65587553.7 600574.736 -> 64986979.0 Inexact Rounded
xadd244 add -32401.939 -585200217. -> -585232619 Inexact Rounded
xcom244 compare -32401.939 -585200217. -> 1
xdiv244 divide -32401.939 -585200217. -> 0.0000553689798 Inexact Rounded
xdvi244 divideint -32401.939 -585200217. -> 0
xmul244 multiply -32401.939 -585200217. -> 1.89616217E+13 Inexact Rounded
xpow244 power -32401.939 -585200217 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem244 remainder -32401.939 -585200217. -> -32401.939
xsub244 subtract -32401.939 -585200217. -> 585167815 Inexact Rounded
xadd245 add 69573.988 -9.77003465E+740933668 -> -9.77003465E+740933668 Inexact Rounded
xcom245 compare 69573.988 -9.77003465E+740933668 -> 1
xdiv245 divide 69573.988 -9.77003465E+740933668 -> -7.12116082E-740933665 Inexact Rounded
xdvi245 divideint 69573.988 -9.77003465E+740933668 -> -0
xmul245 multiply 69573.988 -9.77003465E+740933668 -> -6.79740273E+740933673 Inexact Rounded
xpow245 power 69573.988 -10 -> 3.76297229E-49 Inexact Rounded
xrem245 remainder 69573.988 -9.77003465E+740933668 -> 69573.988
xsub245 subtract 69573.988 -9.77003465E+740933668 -> 9.77003465E+740933668 Inexact Rounded
xadd246 add 2362.06251 -433149546.E-152643629 -> 2362.06251 Inexact Rounded
xcom246 compare 2362.06251 -433149546.E-152643629 -> 1
xdiv246 divide 2362.06251 -433149546.E-152643629 -> -5.45322633E+152643623 Inexact Rounded
xdvi246 divideint 2362.06251 -433149546.E-152643629 -> NaN Division_impossible
xmul246 multiply 2362.06251 -433149546.E-152643629 -> -1.02312630E-152643617 Inexact Rounded
xpow246 power 2362.06251 -4 -> 3.21243577E-14 Inexact Rounded
xrem246 remainder 2362.06251 -433149546.E-152643629 -> NaN Division_impossible
xsub246 subtract 2362.06251 -433149546.E-152643629 -> 2362.06251 Inexact Rounded
xadd247 add -615.23488E+249953452 -21437483.7 -> -6.15234880E+249953454 Inexact Rounded
xcom247 compare -615.23488E+249953452 -21437483.7 -> -1
xdiv247 divide -615.23488E+249953452 -21437483.7 -> 2.86990250E+249953447 Inexact Rounded
xdvi247 divideint -615.23488E+249953452 -21437483.7 -> NaN Division_impossible
xmul247 multiply -615.23488E+249953452 -21437483.7 -> 1.31890877E+249953462 Inexact Rounded
xpow247 power -615.23488E+249953452 -21437484 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem247 remainder -615.23488E+249953452 -21437483.7 -> NaN Division_impossible
xsub247 subtract -615.23488E+249953452 -21437483.7 -> -6.15234880E+249953454 Inexact Rounded
xadd248 add 216741082. 250290244 -> 467031326
xcom248 compare 216741082. 250290244 -> -1
xdiv248 divide 216741082. 250290244 -> 0.865958970 Inexact Rounded
xdvi248 divideint 216741082. 250290244 -> 0
xmul248 multiply 216741082. 250290244 -> 5.42481783E+16 Inexact Rounded
xpow248 power 216741082. 250290244 -> Infinity Overflow Inexact Rounded
xrem248 remainder 216741082. 250290244 -> 216741082
xsub248 subtract 216741082. 250290244 -> -33549162
xadd249 add -6364720.49 5539245.64 -> -825474.85
xcom249 compare -6364720.49 5539245.64 -> -1
xdiv249 divide -6364720.49 5539245.64 -> -1.14902297 Inexact Rounded
xdvi249 divideint -6364720.49 5539245.64 -> -1
xmul249 multiply -6364720.49 5539245.64 -> -3.52557502E+13 Inexact Rounded
xpow249 power -6364720.49 5539246 -> 2.96894641E+37687807 Inexact Rounded
xrem249 remainder -6364720.49 5539245.64 -> -825474.85
xsub249 subtract -6364720.49 5539245.64 -> -11903966.1 Inexact Rounded
xadd250 add -814599.475 -14.5431191 -> -814614.018 Inexact Rounded
xcom250 compare -814599.475 -14.5431191 -> -1
xdiv250 divide -814599.475 -14.5431191 -> 56012.7074 Inexact Rounded
xdvi250 divideint -814599.475 -14.5431191 -> 56012
xmul250 multiply -814599.475 -14.5431191 -> 11846817.2 Inexact Rounded
xpow250 power -814599.475 -15 -> -2.16689622E-89 Inexact Rounded
xrem250 remainder -814599.475 -14.5431191 -> -10.2879708
xsub250 subtract -814599.475 -14.5431191 -> -814584.932 Inexact Rounded
xadd251 add -877498.755 507408724E-168628106 -> -877498.755 Inexact Rounded
xcom251 compare -877498.755 507408724E-168628106 -> -1
xdiv251 divide -877498.755 507408724E-168628106 -> -1.72937262E+168628103 Inexact Rounded
xdvi251 divideint -877498.755 507408724E-168628106 -> NaN Division_impossible
xmul251 multiply -877498.755 507408724E-168628106 -> -4.45250524E-168628092 Inexact Rounded
xpow251 power -877498.755 5 -> -5.20274505E+29 Inexact Rounded
xrem251 remainder -877498.755 507408724E-168628106 -> NaN Division_impossible
xsub251 subtract -877498.755 507408724E-168628106 -> -877498.755 Inexact Rounded
xadd252 add 10634446.5E+475783861 50.7213056E+17807809 -> 1.06344465E+475783868 Inexact Rounded
xcom252 compare 10634446.5E+475783861 50.7213056E+17807809 -> 1
xdiv252 divide 10634446.5E+475783861 50.7213056E+17807809 -> 2.09664289E+457976057 Inexact Rounded
xdvi252 divideint 10634446.5E+475783861 50.7213056E+17807809 -> NaN Division_impossible
xmul252 multiply 10634446.5E+475783861 50.7213056E+17807809 -> 5.39393011E+493591678 Inexact Rounded
xpow252 power 10634446.5E+475783861 5 -> Infinity Overflow Inexact Rounded
xrem252 remainder 10634446.5E+475783861 50.7213056E+17807809 -> NaN Division_impossible
xsub252 subtract 10634446.5E+475783861 50.7213056E+17807809 -> 1.06344465E+475783868 Inexact Rounded
xadd253 add -162726.257E-597285918 -4391.54799 -> -4391.54799 Inexact Rounded
xcom253 compare -162726.257E-597285918 -4391.54799 -> 1
xdiv253 divide -162726.257E-597285918 -4391.54799 -> 3.70544185E-597285917 Inexact Rounded
xdvi253 divideint -162726.257E-597285918 -4391.54799 -> 0
xmul253 multiply -162726.257E-597285918 -4391.54799 -> 7.14620167E-597285910 Inexact Rounded
xpow253 power -162726.257E-597285918 -4392 -> Infinity Overflow Inexact Rounded
xrem253 remainder -162726.257E-597285918 -4391.54799 -> -1.62726257E-597285913
xsub253 subtract -162726.257E-597285918 -4391.54799 -> 4391.54799 Inexact Rounded
xadd254 add 700354586.E-99856707 7198.0493E+436250299 -> 7.19804930E+436250302 Inexact Rounded
xcom254 compare 700354586.E-99856707 7198.0493E+436250299 -> -1
xdiv254 divide 700354586.E-99856707 7198.0493E+436250299 -> 9.72978312E-536107002 Inexact Rounded
xdvi254 divideint 700354586.E-99856707 7198.0493E+436250299 -> 0
xmul254 multiply 700354586.E-99856707 7198.0493E+436250299 -> 5.04118684E+336393604 Inexact Rounded
xpow254 power 700354586.E-99856707 7 -> 8.26467610E-698996888 Inexact Rounded
xrem254 remainder 700354586.E-99856707 7198.0493E+436250299 -> 7.00354586E-99856699
xsub254 subtract 700354586.E-99856707 7198.0493E+436250299 -> -7.19804930E+436250302 Inexact Rounded
xadd255 add 39617663E-463704664 -895.290346 -> -895.290346 Inexact Rounded
xcom255 compare 39617663E-463704664 -895.290346 -> 1
xdiv255 divide 39617663E-463704664 -895.290346 -> -4.42511898E-463704660 Inexact Rounded
xdvi255 divideint 39617663E-463704664 -895.290346 -> -0
xmul255 multiply 39617663E-463704664 -895.290346 -> -3.54693112E-463704654 Inexact Rounded
xpow255 power 39617663E-463704664 -895 -> Infinity Overflow Inexact Rounded
xrem255 remainder 39617663E-463704664 -895.290346 -> 3.9617663E-463704657
xsub255 subtract 39617663E-463704664 -895.290346 -> 895.290346 Inexact Rounded
xadd256 add 5350882.59 -36329829 -> -30978946.4 Inexact Rounded
xcom256 compare 5350882.59 -36329829 -> 1
xdiv256 divide 5350882.59 -36329829 -> -0.147286204 Inexact Rounded
xdvi256 divideint 5350882.59 -36329829 -> -0
xmul256 multiply 5350882.59 -36329829 -> -1.94396649E+14 Inexact Rounded
xpow256 power 5350882.59 -36329829 -> 9.77006107E-244442546 Inexact Rounded
xrem256 remainder 5350882.59 -36329829 -> 5350882.59
xsub256 subtract 5350882.59 -36329829 -> 41680711.6 Inexact Rounded
xadd257 add 91966.4084E+210382952 166740.46E-42001390 -> 9.19664084E+210382956 Inexact Rounded
xcom257 compare 91966.4084E+210382952 166740.46E-42001390 -> 1
xdiv257 divide 91966.4084E+210382952 166740.46E-42001390 -> 5.51554244E+252384341 Inexact Rounded
xdvi257 divideint 91966.4084E+210382952 166740.46E-42001390 -> NaN Division_impossible
xmul257 multiply 91966.4084E+210382952 166740.46E-42001390 -> 1.53345212E+168381572 Inexact Rounded
xpow257 power 91966.4084E+210382952 2 -> 8.45782027E+420765913 Inexact Rounded
xrem257 remainder 91966.4084E+210382952 166740.46E-42001390 -> NaN Division_impossible
xsub257 subtract 91966.4084E+210382952 166740.46E-42001390 -> 9.19664084E+210382956 Inexact Rounded
xadd258 add 231899031.E-481759076 726.337100 -> 726.337100 Inexact Rounded
xcom258 compare 231899031.E-481759076 726.337100 -> -1
xdiv258 divide 231899031.E-481759076 726.337100 -> 3.19271907E-481759071 Inexact Rounded
xdvi258 divideint 231899031.E-481759076 726.337100 -> 0
xmul258 multiply 231899031.E-481759076 726.337100 -> 1.68436870E-481759065 Inexact Rounded
xpow258 power 231899031.E-481759076 726 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem258 remainder 231899031.E-481759076 726.337100 -> 2.31899031E-481759068
xsub258 subtract 231899031.E-481759076 726.337100 -> -726.337100 Inexact Rounded
xadd259 add -9611312.33 22109735.9 -> 12498423.6 Inexact Rounded
xcom259 compare -9611312.33 22109735.9 -> -1
xdiv259 divide -9611312.33 22109735.9 -> -0.434709504 Inexact Rounded
xdvi259 divideint -9611312.33 22109735.9 -> -0
xmul259 multiply -9611312.33 22109735.9 -> -2.12503577E+14 Inexact Rounded
xpow259 power -9611312.33 22109736 -> 6.74530828E+154387481 Inexact Rounded
xrem259 remainder -9611312.33 22109735.9 -> -9611312.33
xsub259 subtract -9611312.33 22109735.9 -> -31721048.2 Inexact Rounded
xadd260 add -5604938.15E-36812542 735937577. -> 735937577 Inexact Rounded
xcom260 compare -5604938.15E-36812542 735937577. -> -1
xdiv260 divide -5604938.15E-36812542 735937577. -> -7.61605104E-36812545 Inexact Rounded
xdvi260 divideint -5604938.15E-36812542 735937577. -> -0
xmul260 multiply -5604938.15E-36812542 735937577. -> -4.12488460E-36812527 Inexact Rounded
xpow260 power -5604938.15E-36812542 735937577 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem260 remainder -5604938.15E-36812542 735937577. -> -5.60493815E-36812536
xsub260 subtract -5604938.15E-36812542 735937577. -> -735937577 Inexact Rounded
xadd261 add 693881413. 260547224E-480281418 -> 693881413 Inexact Rounded
xcom261 compare 693881413. 260547224E-480281418 -> 1
xdiv261 divide 693881413. 260547224E-480281418 -> 2.66316947E+480281418 Inexact Rounded
xdvi261 divideint 693881413. 260547224E-480281418 -> NaN Division_impossible
xmul261 multiply 693881413. 260547224E-480281418 -> 1.80788876E-480281401 Inexact Rounded
xpow261 power 693881413. 3 -> 3.34084066E+26 Inexact Rounded
xrem261 remainder 693881413. 260547224E-480281418 -> NaN Division_impossible
xsub261 subtract 693881413. 260547224E-480281418 -> 693881413 Inexact Rounded
xadd262 add -34865.7378E-368768024 2297117.88 -> 2297117.88 Inexact Rounded
xcom262 compare -34865.7378E-368768024 2297117.88 -> -1
xdiv262 divide -34865.7378E-368768024 2297117.88 -> -1.51780360E-368768026 Inexact Rounded
xdvi262 divideint -34865.7378E-368768024 2297117.88 -> -0
xmul262 multiply -34865.7378E-368768024 2297117.88 -> -8.00907097E-368768014 Inexact Rounded
xpow262 power -34865.7378E-368768024 2297118 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem262 remainder -34865.7378E-368768024 2297117.88 -> -3.48657378E-368768020
xsub262 subtract -34865.7378E-368768024 2297117.88 -> -2297117.88 Inexact Rounded
xadd263 add 1123.32456 7.86747918E+930888796 -> 7.86747918E+930888796 Inexact Rounded
xcom263 compare 1123.32456 7.86747918E+930888796 -> -1
xdiv263 divide 1123.32456 7.86747918E+930888796 -> 1.42780748E-930888794 Inexact Rounded
xdvi263 divideint 1123.32456 7.86747918E+930888796 -> 0
xmul263 multiply 1123.32456 7.86747918E+930888796 -> 8.83773259E+930888799 Inexact Rounded
xpow263 power 1123.32456 8 -> 2.53537401E+24 Inexact Rounded
xrem263 remainder 1123.32456 7.86747918E+930888796 -> 1123.32456
xsub263 subtract 1123.32456 7.86747918E+930888796 -> -7.86747918E+930888796 Inexact Rounded
xadd264 add 56.6607465E+467812565 909552512E+764516200 -> 9.09552512E+764516208 Inexact Rounded
xcom264 compare 56.6607465E+467812565 909552512E+764516200 -> -1
xdiv264 divide 56.6607465E+467812565 909552512E+764516200 -> 6.22951899E-296703643 Inexact Rounded
xdvi264 divideint 56.6607465E+467812565 909552512E+764516200 -> 0
xmul264 multiply 56.6607465E+467812565 909552512E+764516200 -> Infinity Inexact Overflow Rounded
xpow264 power 56.6607465E+467812565 9 -> Infinity Overflow Inexact Rounded
xrem264 remainder 56.6607465E+467812565 909552512E+764516200 -> 5.66607465E+467812566
xsub264 subtract 56.6607465E+467812565 909552512E+764516200 -> -9.09552512E+764516208 Inexact Rounded
xadd265 add -1.85771840E+365552540 -73028339.7 -> -1.85771840E+365552540 Inexact Rounded
xcom265 compare -1.85771840E+365552540 -73028339.7 -> -1
xdiv265 divide -1.85771840E+365552540 -73028339.7 -> 2.54383217E+365552532 Inexact Rounded
xdvi265 divideint -1.85771840E+365552540 -73028339.7 -> NaN Division_impossible
xmul265 multiply -1.85771840E+365552540 -73028339.7 -> 1.35666090E+365552548 Inexact Rounded
xpow265 power -1.85771840E+365552540 -73028340 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem265 remainder -1.85771840E+365552540 -73028339.7 -> NaN Division_impossible
xsub265 subtract -1.85771840E+365552540 -73028339.7 -> -1.85771840E+365552540 Inexact Rounded
xadd266 add 34.1935525 -40767.6450 -> -40733.4514 Inexact Rounded
xcom266 compare 34.1935525 -40767.6450 -> 1
xdiv266 divide 34.1935525 -40767.6450 -> -0.000838742402 Inexact Rounded
xdvi266 divideint 34.1935525 -40767.6450 -> -0
xmul266 multiply 34.1935525 -40767.6450 -> -1393990.61 Inexact Rounded
xpow266 power 34.1935525 -40768 -> 1.45174210E-62536 Inexact Rounded
xrem266 remainder 34.1935525 -40767.6450 -> 34.1935525
xsub266 subtract 34.1935525 -40767.6450 -> 40801.8386 Inexact Rounded
xadd267 add 26.0009168E+751618294 -304019.929 -> 2.60009168E+751618295 Inexact Rounded
xcom267 compare 26.0009168E+751618294 -304019.929 -> 1
xdiv267 divide 26.0009168E+751618294 -304019.929 -> -8.55237250E+751618289 Inexact Rounded
xdvi267 divideint 26.0009168E+751618294 -304019.929 -> NaN Division_impossible
xmul267 multiply 26.0009168E+751618294 -304019.929 -> -7.90479688E+751618300 Inexact Rounded
xpow267 power 26.0009168E+751618294 -304020 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem267 remainder 26.0009168E+751618294 -304019.929 -> NaN Division_impossible
xsub267 subtract 26.0009168E+751618294 -304019.929 -> 2.60009168E+751618295 Inexact Rounded
xadd268 add -58.4853072E+588540055 -4647.3205 -> -5.84853072E+588540056 Inexact Rounded
xcom268 compare -58.4853072E+588540055 -4647.3205 -> -1
xdiv268 divide -58.4853072E+588540055 -4647.3205 -> 1.25847372E+588540053 Inexact Rounded
xdvi268 divideint -58.4853072E+588540055 -4647.3205 -> NaN Division_impossible
xmul268 multiply -58.4853072E+588540055 -4647.3205 -> 2.71799967E+588540060 Inexact Rounded
xpow268 power -58.4853072E+588540055 -4647 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem268 remainder -58.4853072E+588540055 -4647.3205 -> NaN Division_impossible
xsub268 subtract -58.4853072E+588540055 -4647.3205 -> -5.84853072E+588540056 Inexact Rounded
xadd269 add 51.025101 -4467691.57 -> -4467640.54 Inexact Rounded
xcom269 compare 51.025101 -4467691.57 -> 1
xdiv269 divide 51.025101 -4467691.57 -> -0.0000114209095 Inexact Rounded
xdvi269 divideint 51.025101 -4467691.57 -> -0
xmul269 multiply 51.025101 -4467691.57 -> -227964414 Inexact Rounded
xpow269 power 51.025101 -4467692 -> 4.49462589E-7629853 Inexact Rounded
xrem269 remainder 51.025101 -4467691.57 -> 51.025101
xsub269 subtract 51.025101 -4467691.57 -> 4467742.60 Inexact Rounded
xadd270 add -2214.76582 379785372E+223117572 -> 3.79785372E+223117580 Inexact Rounded
xcom270 compare -2214.76582 379785372E+223117572 -> -1
xdiv270 divide -2214.76582 379785372E+223117572 -> -5.83162487E-223117578 Inexact Rounded
xdvi270 divideint -2214.76582 379785372E+223117572 -> -0
xmul270 multiply -2214.76582 379785372E+223117572 -> -8.41135661E+223117583 Inexact Rounded
xpow270 power -2214.76582 4 -> 2.40608658E+13 Inexact Rounded
xrem270 remainder -2214.76582 379785372E+223117572 -> -2214.76582
xsub270 subtract -2214.76582 379785372E+223117572 -> -3.79785372E+223117580 Inexact Rounded
xadd271 add -2564.75207E-841443929 -653498187 -> -653498187 Inexact Rounded
xcom271 compare -2564.75207E-841443929 -653498187 -> 1
xdiv271 divide -2564.75207E-841443929 -653498187 -> 3.92465063E-841443935 Inexact Rounded
xdvi271 divideint -2564.75207E-841443929 -653498187 -> 0
xmul271 multiply -2564.75207E-841443929 -653498187 -> 1.67606083E-841443917 Inexact Rounded
xpow271 power -2564.75207E-841443929 -653498187 -> -Infinity Overflow Inexact Rounded
xrem271 remainder -2564.75207E-841443929 -653498187 -> -2.56475207E-841443926
xsub271 subtract -2564.75207E-841443929 -653498187 -> 653498187 Inexact Rounded
xadd272 add 513115529. 27775075.6E+217133352 -> 2.77750756E+217133359 Inexact Rounded
xcom272 compare 513115529. 27775075.6E+217133352 -> -1
xdiv272 divide 513115529. 27775075.6E+217133352 -> 1.84739562E-217133351 Inexact Rounded
xdvi272 divideint 513115529. 27775075.6E+217133352 -> 0
xmul272 multiply 513115529. 27775075.6E+217133352 -> 1.42518226E+217133368 Inexact Rounded
xpow272 power 513115529. 3 -> 1.35096928E+26 Inexact Rounded
xrem272 remainder 513115529. 27775075.6E+217133352 -> 513115529
xsub272 subtract 513115529. 27775075.6E+217133352 -> -2.77750756E+217133359 Inexact Rounded
xadd273 add -247157.208 -532990.453 -> -780147.661
xcom273 compare -247157.208 -532990.453 -> 1
xdiv273 divide -247157.208 -532990.453 -> 0.463717890 Inexact Rounded
xdvi273 divideint -247157.208 -532990.453 -> 0
xmul273 multiply -247157.208 -532990.453 -> 1.31732432E+11 Inexact Rounded
xpow273 power -247157.208 -532990 -> 1.48314033E-2874401 Inexact Rounded
xrem273 remainder -247157.208 -532990.453 -> -247157.208
xsub273 subtract -247157.208 -532990.453 -> 285833.245
xadd274 add 40.2490764E-339482253 7626.85442E+594264540 -> 7.62685442E+594264543 Inexact Rounded
xcom274 compare 40.2490764E-339482253 7626.85442E+594264540 -> -1
xdiv274 divide 40.2490764E-339482253 7626.85442E+594264540 -> 5.27728395E-933746796 Inexact Rounded
xdvi274 divideint 40.2490764E-339482253 7626.85442E+594264540 -> 0
xmul274 multiply 40.2490764E-339482253 7626.85442E+594264540 -> 3.06973846E+254782292 Inexact Rounded
xpow274 power 40.2490764E-339482253 8 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem274 remainder 40.2490764E-339482253 7626.85442E+594264540 -> 4.02490764E-339482252
xsub274 subtract 40.2490764E-339482253 7626.85442E+594264540 -> -7.62685442E+594264543 Inexact Rounded
xadd275 add -1156008.8 -8870382.36 -> -10026391.2 Inexact Rounded
xcom275 compare -1156008.8 -8870382.36 -> 1
xdiv275 divide -1156008.8 -8870382.36 -> 0.130322319 Inexact Rounded
xdvi275 divideint -1156008.8 -8870382.36 -> 0
xmul275 multiply -1156008.8 -8870382.36 -> 1.02542401E+13 Inexact Rounded
xpow275 power -1156008.8 -8870382 -> 4.32494996E-53780782 Inexact Rounded
xrem275 remainder -1156008.8 -8870382.36 -> -1156008.80
xsub275 subtract -1156008.8 -8870382.36 -> 7714373.56
xadd276 add 880097928. -52455011.1E+204538218 -> -5.24550111E+204538225 Inexact Rounded
xcom276 compare 880097928. -52455011.1E+204538218 -> 1
xdiv276 divide 880097928. -52455011.1E+204538218 -> -1.67781478E-204538217 Inexact Rounded
xdvi276 divideint 880097928. -52455011.1E+204538218 -> -0
xmul276 multiply 880097928. -52455011.1E+204538218 -> -4.61655466E+204538234 Inexact Rounded
xpow276 power 880097928. -5 -> 1.89384751E-45 Inexact Rounded
xrem276 remainder 880097928. -52455011.1E+204538218 -> 880097928
xsub276 subtract 880097928. -52455011.1E+204538218 -> 5.24550111E+204538225 Inexact Rounded
xadd277 add 5796.2524 34458329.7E+832129426 -> 3.44583297E+832129433 Inexact Rounded
xcom277 compare 5796.2524 34458329.7E+832129426 -> -1
xdiv277 divide 5796.2524 34458329.7E+832129426 -> 1.68210486E-832129430 Inexact Rounded
xdvi277 divideint 5796.2524 34458329.7E+832129426 -> 0
xmul277 multiply 5796.2524 34458329.7E+832129426 -> 1.99729176E+832129437 Inexact Rounded
xpow277 power 5796.2524 3 -> 1.94734037E+11 Inexact Rounded
xrem277 remainder 5796.2524 34458329.7E+832129426 -> 5796.2524
xsub277 subtract 5796.2524 34458329.7E+832129426 -> -3.44583297E+832129433 Inexact Rounded
xadd278 add 27.1000923E-218032223 -45.0198341 -> -45.0198341 Inexact Rounded
xcom278 compare 27.1000923E-218032223 -45.0198341 -> 1
xdiv278 divide 27.1000923E-218032223 -45.0198341 -> -6.01958955E-218032224 Inexact Rounded
xdvi278 divideint 27.1000923E-218032223 -45.0198341 -> -0
xmul278 multiply 27.1000923E-218032223 -45.0198341 -> -1.22004166E-218032220 Inexact Rounded
xpow278 power 27.1000923E-218032223 -45 -> Infinity Overflow Inexact Rounded
xrem278 remainder 27.1000923E-218032223 -45.0198341 -> 2.71000923E-218032222
xsub278 subtract 27.1000923E-218032223 -45.0198341 -> 45.0198341 Inexact Rounded
xadd279 add 42643477.8 26118465E-730390549 -> 42643477.8 Inexact Rounded
xcom279 compare 42643477.8 26118465E-730390549 -> 1
xdiv279 divide 42643477.8 26118465E-730390549 -> 1.63269464E+730390549 Inexact Rounded
xdvi279 divideint 42643477.8 26118465E-730390549 -> NaN Division_impossible
xmul279 multiply 42643477.8 26118465E-730390549 -> 1.11378218E-730390534 Inexact Rounded
xpow279 power 42643477.8 3 -> 7.75457230E+22 Inexact Rounded
xrem279 remainder 42643477.8 26118465E-730390549 -> NaN Division_impossible
xsub279 subtract 42643477.8 26118465E-730390549 -> 42643477.8 Inexact Rounded
xadd280 add -31918.9176E-163031657 -21.5422824E-807317258 -> -3.19189176E-163031653 Inexact Rounded
xcom280 compare -31918.9176E-163031657 -21.5422824E-807317258 -> -1
xdiv280 divide -31918.9176E-163031657 -21.5422824E-807317258 -> 1.48168690E+644285604 Inexact Rounded
xdvi280 divideint -31918.9176E-163031657 -21.5422824E-807317258 -> NaN Division_impossible
xmul280 multiply -31918.9176E-163031657 -21.5422824E-807317258 -> 6.87606337E-970348910 Inexact Rounded
xpow280 power -31918.9176E-163031657 -2 -> 9.81530250E+326063304 Inexact Rounded
xrem280 remainder -31918.9176E-163031657 -21.5422824E-807317258 -> NaN Division_impossible
xsub280 subtract -31918.9176E-163031657 -21.5422824E-807317258 -> -3.19189176E-163031653 Inexact Rounded
xadd281 add 84224841.0 2.62548255E+647087608 -> 2.62548255E+647087608 Inexact Rounded
xcom281 compare 84224841.0 2.62548255E+647087608 -> -1
xdiv281 divide 84224841.0 2.62548255E+647087608 -> 3.20797565E-647087601 Inexact Rounded
xdvi281 divideint 84224841.0 2.62548255E+647087608 -> 0
xmul281 multiply 84224841.0 2.62548255E+647087608 -> 2.21130850E+647087616 Inexact Rounded
xpow281 power 84224841.0 3 -> 5.97476185E+23 Inexact Rounded
xrem281 remainder 84224841.0 2.62548255E+647087608 -> 84224841.0
xsub281 subtract 84224841.0 2.62548255E+647087608 -> -2.62548255E+647087608 Inexact Rounded
xadd282 add -64413698.9 -6674.1055E-701047852 -> -64413698.9 Inexact Rounded
xcom282 compare -64413698.9 -6674.1055E-701047852 -> -1
xdiv282 divide -64413698.9 -6674.1055E-701047852 -> 9.65128569E+701047855 Inexact Rounded
xdvi282 divideint -64413698.9 -6674.1055E-701047852 -> NaN Division_impossible
xmul282 multiply -64413698.9 -6674.1055E-701047852 -> 4.29903822E-701047841 Inexact Rounded
xpow282 power -64413698.9 -7 -> -2.17346338E-55 Inexact Rounded
xrem282 remainder -64413698.9 -6674.1055E-701047852 -> NaN Division_impossible
xsub282 subtract -64413698.9 -6674.1055E-701047852 -> -64413698.9 Inexact Rounded
xadd283 add -62.5059208 9.5795779E-898350012 -> -62.5059208 Inexact Rounded
xcom283 compare -62.5059208 9.5795779E-898350012 -> -1
xdiv283 divide -62.5059208 9.5795779E-898350012 -> -6.52491388E+898350012 Inexact Rounded
xdvi283 divideint -62.5059208 9.5795779E-898350012 -> NaN Division_impossible
xmul283 multiply -62.5059208 9.5795779E-898350012 -> -5.98780338E-898350010 Inexact Rounded
xpow283 power -62.5059208 10 -> 9.10356659E+17 Inexact Rounded
xrem283 remainder -62.5059208 9.5795779E-898350012 -> NaN Division_impossible
xsub283 subtract -62.5059208 9.5795779E-898350012 -> -62.5059208 Inexact Rounded
xadd284 add 9090950.80 436.400932 -> 9091387.20 Inexact Rounded
xcom284 compare 9090950.80 436.400932 -> 1
xdiv284 divide 9090950.80 436.400932 -> 20831.6485 Inexact Rounded
xdvi284 divideint 9090950.80 436.400932 -> 20831
xmul284 multiply 9090950.80 436.400932 -> 3.96729940E+9 Inexact Rounded
xpow284 power 9090950.80 436 -> 8.98789557E+3033 Inexact Rounded
xrem284 remainder 9090950.80 436.400932 -> 282.985508
xsub284 subtract 9090950.80 436.400932 -> 9090514.40 Inexact Rounded
xadd285 add -89833825.7E+329205393 -779430.194 -> -8.98338257E+329205400 Inexact Rounded
xcom285 compare -89833825.7E+329205393 -779430.194 -> -1
xdiv285 divide -89833825.7E+329205393 -779430.194 -> 1.15255768E+329205395 Inexact Rounded
xdvi285 divideint -89833825.7E+329205393 -779430.194 -> NaN Division_impossible
xmul285 multiply -89833825.7E+329205393 -779430.194 -> 7.00191962E+329205406 Inexact Rounded
xpow285 power -89833825.7E+329205393 -779430 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem285 remainder -89833825.7E+329205393 -779430.194 -> NaN Division_impossible
xsub285 subtract -89833825.7E+329205393 -779430.194 -> -8.98338257E+329205400 Inexact Rounded
xadd286 add -714562.019E+750205688 704079764 -> -7.14562019E+750205693 Inexact Rounded
xcom286 compare -714562.019E+750205688 704079764 -> -1
xdiv286 divide -714562.019E+750205688 704079764 -> -1.01488788E+750205685 Inexact Rounded
xdvi286 divideint -714562.019E+750205688 704079764 -> NaN Division_impossible
xmul286 multiply -714562.019E+750205688 704079764 -> -5.03108658E+750205702 Inexact Rounded
xpow286 power -714562.019E+750205688 704079764 -> Infinity Overflow Inexact Rounded
xrem286 remainder -714562.019E+750205688 704079764 -> NaN Division_impossible
xsub286 subtract -714562.019E+750205688 704079764 -> -7.14562019E+750205693 Inexact Rounded
xadd287 add -584537670. 31139.7737E-146687560 -> -584537670 Inexact Rounded
xcom287 compare -584537670. 31139.7737E-146687560 -> -1
xdiv287 divide -584537670. 31139.7737E-146687560 -> -1.87714168E+146687564 Inexact Rounded
xdvi287 divideint -584537670. 31139.7737E-146687560 -> NaN Division_impossible
xmul287 multiply -584537670. 31139.7737E-146687560 -> -1.82023708E-146687547 Inexact Rounded
xpow287 power -584537670. 3 -> -1.99727337E+26 Inexact Rounded
xrem287 remainder -584537670. 31139.7737E-146687560 -> NaN Division_impossible
xsub287 subtract -584537670. 31139.7737E-146687560 -> -584537670 Inexact Rounded
xadd288 add -4.18074650E-858746879 571035.277E-279409165 -> 5.71035277E-279409160 Inexact Rounded
xcom288 compare -4.18074650E-858746879 571035.277E-279409165 -> -1
xdiv288 divide -4.18074650E-858746879 571035.277E-279409165 -> -7.32134540E-579337720 Inexact Rounded
xdvi288 divideint -4.18074650E-858746879 571035.277E-279409165 -> -0
xmul288 multiply -4.18074650E-858746879 571035.277E-279409165 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xpow288 power -4.18074650E-858746879 6 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem288 remainder -4.18074650E-858746879 571035.277E-279409165 -> -4.18074650E-858746879
xsub288 subtract -4.18074650E-858746879 571035.277E-279409165 -> -5.71035277E-279409160 Inexact Rounded
xadd289 add 5.15309635 -695649.219E+451948183 -> -6.95649219E+451948188 Inexact Rounded
xcom289 compare 5.15309635 -695649.219E+451948183 -> 1
xdiv289 divide 5.15309635 -695649.219E+451948183 -> -7.40760747E-451948189 Inexact Rounded
xdvi289 divideint 5.15309635 -695649.219E+451948183 -> -0
xmul289 multiply 5.15309635 -695649.219E+451948183 -> -3.58474745E+451948189 Inexact Rounded
xpow289 power 5.15309635 -7 -> 0.0000103638749 Inexact Rounded
xrem289 remainder 5.15309635 -695649.219E+451948183 -> 5.15309635
xsub289 subtract 5.15309635 -695649.219E+451948183 -> 6.95649219E+451948188 Inexact Rounded
xadd290 add -940030153.E+83797657 -4.11510193 -> -9.40030153E+83797665 Inexact Rounded
xcom290 compare -940030153.E+83797657 -4.11510193 -> -1
xdiv290 divide -940030153.E+83797657 -4.11510193 -> 2.28434233E+83797665 Inexact Rounded
xdvi290 divideint -940030153.E+83797657 -4.11510193 -> NaN Division_impossible
xmul290 multiply -940030153.E+83797657 -4.11510193 -> 3.86831990E+83797666 Inexact Rounded
xpow290 power -940030153.E+83797657 -4 -> 1.28065710E-335190664 Inexact Rounded
xrem290 remainder -940030153.E+83797657 -4.11510193 -> NaN Division_impossible
xsub290 subtract -940030153.E+83797657 -4.11510193 -> -9.40030153E+83797665 Inexact Rounded
xadd291 add 89088.9683E+587739290 1.31932110 -> 8.90889683E+587739294 Inexact Rounded
xcom291 compare 89088.9683E+587739290 1.31932110 -> 1
xdiv291 divide 89088.9683E+587739290 1.31932110 -> 6.75263727E+587739294 Inexact Rounded
xdvi291 divideint 89088.9683E+587739290 1.31932110 -> NaN Division_impossible
xmul291 multiply 89088.9683E+587739290 1.31932110 -> 1.17536956E+587739295 Inexact Rounded
xpow291 power 89088.9683E+587739290 1 -> 8.90889683E+587739294
xrem291 remainder 89088.9683E+587739290 1.31932110 -> NaN Division_impossible
xsub291 subtract 89088.9683E+587739290 1.31932110 -> 8.90889683E+587739294 Inexact Rounded
xadd292 add 3336750 6.47961126 -> 3336756.48 Inexact Rounded
xcom292 compare 3336750 6.47961126 -> 1
xdiv292 divide 3336750 6.47961126 -> 514961.448 Inexact Rounded
xdvi292 divideint 3336750 6.47961126 -> 514961
xmul292 multiply 3336750 6.47961126 -> 21620842.9 Inexact Rounded
xpow292 power 3336750 6 -> 1.38019997E+39 Inexact Rounded
xrem292 remainder 3336750 6.47961126 -> 2.90593914
xsub292 subtract 3336750 6.47961126 -> 3336743.52 Inexact Rounded
xadd293 add 904654622. 692065270.E+329081915 -> 6.92065270E+329081923 Inexact Rounded
xcom293 compare 904654622. 692065270.E+329081915 -> -1
xdiv293 divide 904654622. 692065270.E+329081915 -> 1.30718107E-329081915 Inexact Rounded
xdvi293 divideint 904654622. 692065270.E+329081915 -> 0
xmul293 multiply 904654622. 692065270.E+329081915 -> 6.26080045E+329081932 Inexact Rounded
xpow293 power 904654622. 7 -> 4.95883485E+62 Inexact Rounded
xrem293 remainder 904654622. 692065270.E+329081915 -> 904654622
xsub293 subtract 904654622. 692065270.E+329081915 -> -6.92065270E+329081923 Inexact Rounded
xadd294 add 304804380 -4681.23698 -> 304799699 Inexact Rounded
xcom294 compare 304804380 -4681.23698 -> 1
xdiv294 divide 304804380 -4681.23698 -> -65111.9312 Inexact Rounded
xdvi294 divideint 304804380 -4681.23698 -> -65111
xmul294 multiply 304804380 -4681.23698 -> -1.42686154E+12 Inexact Rounded
xpow294 power 304804380 -4681 -> 1.98037102E-39714 Inexact Rounded
xrem294 remainder 304804380 -4681.23698 -> 4358.99522
xsub294 subtract 304804380 -4681.23698 -> 304809061 Inexact Rounded
xadd295 add 674.55569 -82981.2684E+852890752 -> -8.29812684E+852890756 Inexact Rounded
xcom295 compare 674.55569 -82981.2684E+852890752 -> 1
xdiv295 divide 674.55569 -82981.2684E+852890752 -> -8.12901156E-852890755 Inexact Rounded
xdvi295 divideint 674.55569 -82981.2684E+852890752 -> -0
xmul295 multiply 674.55569 -82981.2684E+852890752 -> -5.59754868E+852890759 Inexact Rounded
xpow295 power 674.55569 -8 -> 2.33269265E-23 Inexact Rounded
xrem295 remainder 674.55569 -82981.2684E+852890752 -> 674.55569
xsub295 subtract 674.55569 -82981.2684E+852890752 -> 8.29812684E+852890756 Inexact Rounded
xadd296 add -5111.51025E-108006096 5448870.4E+279212255 -> 5.44887040E+279212261 Inexact Rounded
xcom296 compare -5111.51025E-108006096 5448870.4E+279212255 -> -1
xdiv296 divide -5111.51025E-108006096 5448870.4E+279212255 -> -9.38086222E-387218355 Inexact Rounded
xdvi296 divideint -5111.51025E-108006096 5448870.4E+279212255 -> -0
xmul296 multiply -5111.51025E-108006096 5448870.4E+279212255 -> -2.78519569E+171206169 Inexact Rounded
xpow296 power -5111.51025E-108006096 5 -> -3.48936323E-540030462 Inexact Rounded
xrem296 remainder -5111.51025E-108006096 5448870.4E+279212255 -> -5.11151025E-108006093
xsub296 subtract -5111.51025E-108006096 5448870.4E+279212255 -> -5.44887040E+279212261 Inexact Rounded
xadd297 add -2623.45068 -466463938. -> -466466561 Inexact Rounded
xcom297 compare -2623.45068 -466463938. -> 1
xdiv297 divide -2623.45068 -466463938. -> 0.00000562412325 Inexact Rounded
xdvi297 divideint -2623.45068 -466463938. -> 0
xmul297 multiply -2623.45068 -466463938. -> 1.22374514E+12 Inexact Rounded
xpow297 power -2623.45068 -466463938 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem297 remainder -2623.45068 -466463938. -> -2623.45068
xsub297 subtract -2623.45068 -466463938. -> 466461315 Inexact Rounded
xadd298 add 299350.435 3373.33551 -> 302723.771 Inexact Rounded
xcom298 compare 299350.435 3373.33551 -> 1
xdiv298 divide 299350.435 3373.33551 -> 88.7401903 Inexact Rounded
xdvi298 divideint 299350.435 3373.33551 -> 88
xmul298 multiply 299350.435 3373.33551 -> 1.00980945E+9 Inexact Rounded
xpow298 power 299350.435 3373 -> 1.42817370E+18471 Inexact Rounded
xrem298 remainder 299350.435 3373.33551 -> 2496.91012
xsub298 subtract 299350.435 3373.33551 -> 295977.099 Inexact Rounded
xadd299 add -6589947.80 -2448.75933E-591549734 -> -6589947.80 Inexact Rounded
xcom299 compare -6589947.80 -2448.75933E-591549734 -> -1
xdiv299 divide -6589947.80 -2448.75933E-591549734 -> 2.69113739E+591549737 Inexact Rounded
xdvi299 divideint -6589947.80 -2448.75933E-591549734 -> NaN Division_impossible
xmul299 multiply -6589947.80 -2448.75933E-591549734 -> 1.61371962E-591549724 Inexact Rounded
xpow299 power -6589947.80 -2 -> 2.30269305E-14 Inexact Rounded
xrem299 remainder -6589947.80 -2448.75933E-591549734 -> NaN Division_impossible
xsub299 subtract -6589947.80 -2448.75933E-591549734 -> -6589947.80 Inexact Rounded
xadd300 add 3774.5358E-491090520 173.060090 -> 173.060090 Inexact Rounded
xcom300 compare 3774.5358E-491090520 173.060090 -> -1
xdiv300 divide 3774.5358E-491090520 173.060090 -> 2.18105503E-491090519 Inexact Rounded
xdvi300 divideint 3774.5358E-491090520 173.060090 -> 0
xmul300 multiply 3774.5358E-491090520 173.060090 -> 6.53221505E-491090515 Inexact Rounded
xpow300 power 3774.5358E-491090520 173 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem300 remainder 3774.5358E-491090520 173.060090 -> 3.7745358E-491090517
xsub300 subtract 3774.5358E-491090520 173.060090 -> -173.060090 Inexact Rounded
xadd301 add -13.6783690 -453.610117 -> -467.288486 Rounded
xcom301 compare -13.6783690 -453.610117 -> 1
xdiv301 divide -13.6783690 -453.610117 -> 0.0301544619 Inexact Rounded
xdvi301 divideint -13.6783690 -453.610117 -> 0
xmul301 multiply -13.6783690 -453.610117 -> 6204.64656 Inexact Rounded
xpow301 power -13.6783690 -454 -> 1.73948535E-516 Inexact Rounded
xrem301 remainder -13.6783690 -453.610117 -> -13.6783690
xsub301 subtract -13.6783690 -453.610117 -> 439.931748 Rounded
xadd302 add -990100927.E-615244634 223801.421E+247632618 -> 2.23801421E+247632623 Inexact Rounded
xcom302 compare -990100927.E-615244634 223801.421E+247632618 -> -1
xdiv302 divide -990100927.E-615244634 223801.421E+247632618 -> -4.42401537E-862877249 Inexact Rounded
xdvi302 divideint -990100927.E-615244634 223801.421E+247632618 -> -0
xmul302 multiply -990100927.E-615244634 223801.421E+247632618 -> -2.21585994E-367612002 Inexact Rounded
xpow302 power -990100927.E-615244634 2 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem302 remainder -990100927.E-615244634 223801.421E+247632618 -> -9.90100927E-615244626
xsub302 subtract -990100927.E-615244634 223801.421E+247632618 -> -2.23801421E+247632623 Inexact Rounded
xadd303 add 1275.10292 -667965353 -> -667964078 Inexact Rounded
xcom303 compare 1275.10292 -667965353 -> 1
xdiv303 divide 1275.10292 -667965353 -> -0.00000190893572 Inexact Rounded
xdvi303 divideint 1275.10292 -667965353 -> -0
xmul303 multiply 1275.10292 -667965353 -> -8.51724572E+11 Inexact Rounded
xpow303 power 1275.10292 -667965353 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem303 remainder 1275.10292 -667965353 -> 1275.10292
xsub303 subtract 1275.10292 -667965353 -> 667966628 Inexact Rounded
xadd304 add -8.76375480E-596792197 992.077361 -> 992.077361 Inexact Rounded
xcom304 compare -8.76375480E-596792197 992.077361 -> -1
xdiv304 divide -8.76375480E-596792197 992.077361 -> -8.83374134E-596792200 Inexact Rounded
xdvi304 divideint -8.76375480E-596792197 992.077361 -> -0
xmul304 multiply -8.76375480E-596792197 992.077361 -> -8.69432273E-596792194 Inexact Rounded
xpow304 power -8.76375480E-596792197 992 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem304 remainder -8.76375480E-596792197 992.077361 -> -8.76375480E-596792197
xsub304 subtract -8.76375480E-596792197 992.077361 -> -992.077361 Inexact Rounded
xadd305 add 953.976935E+385444720 96503.3378 -> 9.53976935E+385444722 Inexact Rounded
xcom305 compare 953.976935E+385444720 96503.3378 -> 1
xdiv305 divide 953.976935E+385444720 96503.3378 -> 9.88542942E+385444717 Inexact Rounded
xdvi305 divideint 953.976935E+385444720 96503.3378 -> NaN Division_impossible
xmul305 multiply 953.976935E+385444720 96503.3378 -> 9.20619584E+385444727 Inexact Rounded
xpow305 power 953.976935E+385444720 96503 -> Infinity Overflow Inexact Rounded
xrem305 remainder 953.976935E+385444720 96503.3378 -> NaN Division_impossible
xsub305 subtract 953.976935E+385444720 96503.3378 -> 9.53976935E+385444722 Inexact Rounded
xadd306 add 213577152 -986710073E+31900046 -> -9.86710073E+31900054 Inexact Rounded
xcom306 compare 213577152 -986710073E+31900046 -> 1
xdiv306 divide 213577152 -986710073E+31900046 -> -2.16453807E-31900047 Inexact Rounded
xdvi306 divideint 213577152 -986710073E+31900046 -> -0
xmul306 multiply 213577152 -986710073E+31900046 -> -2.10738727E+31900063 Inexact Rounded
xpow306 power 213577152 -10 -> 5.06351487E-84 Inexact Rounded
xrem306 remainder 213577152 -986710073E+31900046 -> 213577152
xsub306 subtract 213577152 -986710073E+31900046 -> 9.86710073E+31900054 Inexact Rounded
xadd307 add 91393.9398E-323439228 -135.701000 -> -135.701000 Inexact Rounded
xcom307 compare 91393.9398E-323439228 -135.701000 -> 1
xdiv307 divide 91393.9398E-323439228 -135.701000 -> -6.73494962E-323439226 Inexact Rounded
xdvi307 divideint 91393.9398E-323439228 -135.701000 -> -0
xmul307 multiply 91393.9398E-323439228 -135.701000 -> -1.24022490E-323439221 Inexact Rounded
xpow307 power 91393.9398E-323439228 -136 -> Infinity Overflow Inexact Rounded
xrem307 remainder 91393.9398E-323439228 -135.701000 -> 9.13939398E-323439224
xsub307 subtract 91393.9398E-323439228 -135.701000 -> 135.701000 Inexact Rounded
xadd308 add -396.503557 45757264.E-254363788 -> -396.503557 Inexact Rounded
xcom308 compare -396.503557 45757264.E-254363788 -> -1
xdiv308 divide -396.503557 45757264.E-254363788 -> -8.66536856E+254363782 Inexact Rounded
xdvi308 divideint -396.503557 45757264.E-254363788 -> NaN Division_impossible
xmul308 multiply -396.503557 45757264.E-254363788 -> -1.81429179E-254363778 Inexact Rounded
xpow308 power -396.503557 5 -> -9.80021128E+12 Inexact Rounded
xrem308 remainder -396.503557 45757264.E-254363788 -> NaN Division_impossible
xsub308 subtract -396.503557 45757264.E-254363788 -> -396.503557 Inexact Rounded
xadd309 add 59807846.1 1.53345254 -> 59807847.6 Inexact Rounded
xcom309 compare 59807846.1 1.53345254 -> 1
xdiv309 divide 59807846.1 1.53345254 -> 39002084.9 Inexact Rounded
xdvi309 divideint 59807846.1 1.53345254 -> 39002084
xmul309 multiply 59807846.1 1.53345254 -> 91712493.5 Inexact Rounded
xpow309 power 59807846.1 2 -> 3.57697846E+15 Inexact Rounded
xrem309 remainder 59807846.1 1.53345254 -> 1.32490664
xsub309 subtract 59807846.1 1.53345254 -> 59807844.6 Inexact Rounded
xadd310 add -8046158.45 8.3635397 -> -8046150.09 Inexact Rounded
xcom310 compare -8046158.45 8.3635397 -> -1
xdiv310 divide -8046158.45 8.3635397 -> -962051.803 Inexact Rounded
xdvi310 divideint -8046158.45 8.3635397 -> -962051
xmul310 multiply -8046158.45 8.3635397 -> -67294365.6 Inexact Rounded
xpow310 power -8046158.45 8 -> 1.75674467E+55 Inexact Rounded
xrem310 remainder -8046158.45 8.3635397 -> -6.7180753
xsub310 subtract -8046158.45 8.3635397 -> -8046166.81 Inexact Rounded
xadd311 add 55.1123381E+50627250 -94.0355047E-162540316 -> 5.51123381E+50627251 Inexact Rounded
xcom311 compare 55.1123381E+50627250 -94.0355047E-162540316 -> 1
xdiv311 divide 55.1123381E+50627250 -94.0355047E-162540316 -> -5.86080101E+213167565 Inexact Rounded
xdvi311 divideint 55.1123381E+50627250 -94.0355047E-162540316 -> NaN Division_impossible
xmul311 multiply 55.1123381E+50627250 -94.0355047E-162540316 -> -5.18251653E-111913063 Inexact Rounded
xpow311 power 55.1123381E+50627250 -9 -> 2.13186881E-455645266 Inexact Rounded
xrem311 remainder 55.1123381E+50627250 -94.0355047E-162540316 -> NaN Division_impossible
xsub311 subtract 55.1123381E+50627250 -94.0355047E-162540316 -> 5.51123381E+50627251 Inexact Rounded
xadd312 add -948.038054 3580.84510 -> 2632.80705 Inexact Rounded
xcom312 compare -948.038054 3580.84510 -> -1
xdiv312 divide -948.038054 3580.84510 -> -0.264752601 Inexact Rounded
xdvi312 divideint -948.038054 3580.84510 -> -0
xmul312 multiply -948.038054 3580.84510 -> -3394777.42 Inexact Rounded
xpow312 power -948.038054 3581 -> -1.03058288E+10660 Inexact Rounded
xrem312 remainder -948.038054 3580.84510 -> -948.038054
xsub312 subtract -948.038054 3580.84510 -> -4528.88315 Inexact Rounded
xadd313 add -6026.42752 -14.2286406E-334921364 -> -6026.42752 Inexact Rounded
xcom313 compare -6026.42752 -14.2286406E-334921364 -> -1
xdiv313 divide -6026.42752 -14.2286406E-334921364 -> 4.23542044E+334921366 Inexact Rounded
xdvi313 divideint -6026.42752 -14.2286406E-334921364 -> NaN Division_impossible
xmul313 multiply -6026.42752 -14.2286406E-334921364 -> 8.57478713E-334921360 Inexact Rounded
xpow313 power -6026.42752 -1 -> -0.000165935788 Inexact Rounded
xrem313 remainder -6026.42752 -14.2286406E-334921364 -> NaN Division_impossible
xsub313 subtract -6026.42752 -14.2286406E-334921364 -> -6026.42752 Inexact Rounded
xadd314 add 79551.5014 -538.186229 -> 79013.3152 Inexact Rounded
xcom314 compare 79551.5014 -538.186229 -> 1
xdiv314 divide 79551.5014 -538.186229 -> -147.814078 Inexact Rounded
xdvi314 divideint 79551.5014 -538.186229 -> -147
xmul314 multiply 79551.5014 -538.186229 -> -42813522.5 Inexact Rounded
xpow314 power 79551.5014 -538 -> 2.82599389E-2637 Inexact Rounded
xrem314 remainder 79551.5014 -538.186229 -> 438.125737
xsub314 subtract 79551.5014 -538.186229 -> 80089.6876 Inexact Rounded
xadd315 add 42706056.E+623578292 -690.327745 -> 4.27060560E+623578299 Inexact Rounded
xcom315 compare 42706056.E+623578292 -690.327745 -> 1
xdiv315 divide 42706056.E+623578292 -690.327745 -> -6.18634501E+623578296 Inexact Rounded
xdvi315 divideint 42706056.E+623578292 -690.327745 -> NaN Division_impossible
xmul315 multiply 42706056.E+623578292 -690.327745 -> -2.94811753E+623578302 Inexact Rounded
xpow315 power 42706056.E+623578292 -690 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem315 remainder 42706056.E+623578292 -690.327745 -> NaN Division_impossible
xsub315 subtract 42706056.E+623578292 -690.327745 -> 4.27060560E+623578299 Inexact Rounded
xadd316 add 2454136.08E+502374077 856268.795E-356664934 -> 2.45413608E+502374083 Inexact Rounded
xcom316 compare 2454136.08E+502374077 856268.795E-356664934 -> 1
xdiv316 divide 2454136.08E+502374077 856268.795E-356664934 -> 2.86608142E+859039011 Inexact Rounded
xdvi316 divideint 2454136.08E+502374077 856268.795E-356664934 -> NaN Division_impossible
xmul316 multiply 2454136.08E+502374077 856268.795E-356664934 -> 2.10140014E+145709155 Inexact Rounded
xpow316 power 2454136.08E+502374077 9 -> Infinity Overflow Inexact Rounded
xrem316 remainder 2454136.08E+502374077 856268.795E-356664934 -> NaN Division_impossible
xsub316 subtract 2454136.08E+502374077 856268.795E-356664934 -> 2.45413608E+502374083 Inexact Rounded
xadd317 add -3264204.54 -42704.501 -> -3306909.04 Inexact Rounded
xcom317 compare -3264204.54 -42704.501 -> -1
xdiv317 divide -3264204.54 -42704.501 -> 76.4370140 Inexact Rounded
xdvi317 divideint -3264204.54 -42704.501 -> 76
xmul317 multiply -3264204.54 -42704.501 -> 1.39396226E+11 Inexact Rounded
xpow317 power -3264204.54 -42705 -> -1.37293410E-278171 Inexact Rounded
xrem317 remainder -3264204.54 -42704.501 -> -18662.464
xsub317 subtract -3264204.54 -42704.501 -> -3221500.04 Inexact Rounded
xadd318 add 1.21265492 44102.6073 -> 44103.8200 Inexact Rounded
xcom318 compare 1.21265492 44102.6073 -> -1
xdiv318 divide 1.21265492 44102.6073 -> 0.0000274962183 Inexact Rounded
xdvi318 divideint 1.21265492 44102.6073 -> 0
xmul318 multiply 1.21265492 44102.6073 -> 53481.2437 Inexact Rounded
xpow318 power 1.21265492 44103 -> 1.15662573E+3693 Inexact Rounded
xrem318 remainder 1.21265492 44102.6073 -> 1.21265492
xsub318 subtract 1.21265492 44102.6073 -> -44101.3946 Inexact Rounded
xadd319 add -19.054711E+975514652 -22144.0822 -> -1.90547110E+975514653 Inexact Rounded
xcom319 compare -19.054711E+975514652 -22144.0822 -> -1
xdiv319 divide -19.054711E+975514652 -22144.0822 -> 8.60487729E+975514648 Inexact Rounded
xdvi319 divideint -19.054711E+975514652 -22144.0822 -> NaN Division_impossible
xmul319 multiply -19.054711E+975514652 -22144.0822 -> 4.21949087E+975514657 Inexact Rounded
xpow319 power -19.054711E+975514652 -22144 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem319 remainder -19.054711E+975514652 -22144.0822 -> NaN Division_impossible
xsub319 subtract -19.054711E+975514652 -22144.0822 -> -1.90547110E+975514653 Inexact Rounded
xadd320 add 745.78452 -1922.00670E+375923302 -> -1.92200670E+375923305 Inexact Rounded
xcom320 compare 745.78452 -1922.00670E+375923302 -> 1
xdiv320 divide 745.78452 -1922.00670E+375923302 -> -3.88023892E-375923303 Inexact Rounded
xdvi320 divideint 745.78452 -1922.00670E+375923302 -> -0
xmul320 multiply 745.78452 -1922.00670E+375923302 -> -1.43340284E+375923308 Inexact Rounded
xpow320 power 745.78452 -2 -> 0.00000179793204 Inexact Rounded
xrem320 remainder 745.78452 -1922.00670E+375923302 -> 745.78452
xsub320 subtract 745.78452 -1922.00670E+375923302 -> 1.92200670E+375923305 Inexact Rounded
xadd321 add -963717836 -823989308 -> -1.78770714E+9 Inexact Rounded
xcom321 compare -963717836 -823989308 -> -1
xdiv321 divide -963717836 -823989308 -> 1.16957566 Inexact Rounded
xdvi321 divideint -963717836 -823989308 -> 1
xmul321 multiply -963717836 -823989308 -> 7.94093193E+17 Inexact Rounded
xpow321 power -963717836 -823989308 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem321 remainder -963717836 -823989308 -> -139728528
xsub321 subtract -963717836 -823989308 -> -139728528
xadd322 add 82.4185291E-321919303 -215747737.E-995147400 -> 8.24185291E-321919302 Inexact Rounded
xcom322 compare 82.4185291E-321919303 -215747737.E-995147400 -> 1
xdiv322 divide 82.4185291E-321919303 -215747737.E-995147400 -> -3.82013412E+673228090 Inexact Rounded
xdvi322 divideint 82.4185291E-321919303 -215747737.E-995147400 -> NaN Division_impossible
xmul322 multiply 82.4185291E-321919303 -215747737.E-995147400 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xpow322 power 82.4185291E-321919303 -2 -> 1.47214396E+643838602 Inexact Rounded
xrem322 remainder 82.4185291E-321919303 -215747737.E-995147400 -> NaN Division_impossible
xsub322 subtract 82.4185291E-321919303 -215747737.E-995147400 -> 8.24185291E-321919302 Inexact Rounded
xadd323 add -808328.607E-790810342 53075.7082 -> 53075.7082 Inexact Rounded
xcom323 compare -808328.607E-790810342 53075.7082 -> -1
xdiv323 divide -808328.607E-790810342 53075.7082 -> -1.52297281E-790810341 Inexact Rounded
xdvi323 divideint -808328.607E-790810342 53075.7082 -> -0
xmul323 multiply -808328.607E-790810342 53075.7082 -> -4.29026133E-790810332 Inexact Rounded
xpow323 power -808328.607E-790810342 53076 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem323 remainder -808328.607E-790810342 53075.7082 -> -8.08328607E-790810337
xsub323 subtract -808328.607E-790810342 53075.7082 -> -53075.7082 Inexact Rounded
xadd324 add 700592.720 -698485.085 -> 2107.635
xcom324 compare 700592.720 -698485.085 -> 1
xdiv324 divide 700592.720 -698485.085 -> -1.00301744 Inexact Rounded
xdvi324 divideint 700592.720 -698485.085 -> -1
xmul324 multiply 700592.720 -698485.085 -> -4.89353566E+11 Inexact Rounded
xpow324 power 700592.720 -698485 -> 8.83690000E-4082971 Inexact Rounded
xrem324 remainder 700592.720 -698485.085 -> 2107.635
xsub324 subtract 700592.720 -698485.085 -> 1399077.81 Inexact Rounded
xadd325 add -80273928.0 661346.239 -> -79612581.8 Inexact Rounded
xcom325 compare -80273928.0 661346.239 -> -1
xdiv325 divide -80273928.0 661346.239 -> -121.379579 Inexact Rounded
xdvi325 divideint -80273928.0 661346.239 -> -121
xmul325 multiply -80273928.0 661346.239 -> -5.30888604E+13 Inexact Rounded
xpow325 power -80273928.0 661346 -> 5.45664856E+5227658 Inexact Rounded
xrem325 remainder -80273928.0 661346.239 -> -251033.081
xsub325 subtract -80273928.0 661346.239 -> -80935274.2 Inexact Rounded
xadd326 add -24018251.0E+819786764 59141.9600E-167165065 -> -2.40182510E+819786771 Inexact Rounded
xcom326 compare -24018251.0E+819786764 59141.9600E-167165065 -> -1
xdiv326 divide -24018251.0E+819786764 59141.9600E-167165065 -> -4.06111854E+986951831 Inexact Rounded
xdvi326 divideint -24018251.0E+819786764 59141.9600E-167165065 -> NaN Division_impossible
xmul326 multiply -24018251.0E+819786764 59141.9600E-167165065 -> -1.42048644E+652621711 Inexact Rounded
xpow326 power -24018251.0E+819786764 6 -> Infinity Overflow Inexact Rounded
xrem326 remainder -24018251.0E+819786764 59141.9600E-167165065 -> NaN Division_impossible
xsub326 subtract -24018251.0E+819786764 59141.9600E-167165065 -> -2.40182510E+819786771 Inexact Rounded
xadd327 add 2512953.3 -3769170.35E-993621645 -> 2512953.30 Inexact Rounded
xcom327 compare 2512953.3 -3769170.35E-993621645 -> 1
xdiv327 divide 2512953.3 -3769170.35E-993621645 -> -6.66712583E+993621644 Inexact Rounded
xdvi327 divideint 2512953.3 -3769170.35E-993621645 -> NaN Division_impossible
xmul327 multiply 2512953.3 -3769170.35E-993621645 -> -9.47174907E-993621633 Inexact Rounded
xpow327 power 2512953.3 -4 -> 2.50762348E-26 Inexact Rounded
xrem327 remainder 2512953.3 -3769170.35E-993621645 -> NaN Division_impossible
xsub327 subtract 2512953.3 -3769170.35E-993621645 -> 2512953.30 Inexact Rounded
xadd328 add -682.796370 71131.0224 -> 70448.2260 Inexact Rounded
xcom328 compare -682.796370 71131.0224 -> -1
xdiv328 divide -682.796370 71131.0224 -> -0.00959913617 Inexact Rounded
xdvi328 divideint -682.796370 71131.0224 -> -0
xmul328 multiply -682.796370 71131.0224 -> -48568003.9 Inexact Rounded
xpow328 power -682.796370 71131 -> -9.28114741E+201605 Inexact Rounded
xrem328 remainder -682.796370 71131.0224 -> -682.796370
xsub328 subtract -682.796370 71131.0224 -> -71813.8188 Inexact Rounded
xadd329 add 89.9997490 -4993.69831 -> -4903.69856 Inexact Rounded
xcom329 compare 89.9997490 -4993.69831 -> 1
xdiv329 divide 89.9997490 -4993.69831 -> -0.0180226644 Inexact Rounded
xdvi329 divideint 89.9997490 -4993.69831 -> -0
xmul329 multiply 89.9997490 -4993.69831 -> -449431.594 Inexact Rounded
xpow329 power 89.9997490 -4994 -> 3.30336525E-9760 Inexact Rounded
xrem329 remainder 89.9997490 -4993.69831 -> 89.9997490
xsub329 subtract 89.9997490 -4993.69831 -> 5083.69806 Inexact Rounded
xadd330 add 76563354.6E-112338836 278271.585E-511481095 -> 7.65633546E-112338829 Inexact Rounded
xcom330 compare 76563354.6E-112338836 278271.585E-511481095 -> 1
xdiv330 divide 76563354.6E-112338836 278271.585E-511481095 -> 2.75138960E+399142261 Inexact Rounded
xdvi330 divideint 76563354.6E-112338836 278271.585E-511481095 -> NaN Division_impossible
xmul330 multiply 76563354.6E-112338836 278271.585E-511481095 -> 2.13054060E-623819918 Inexact Rounded
xpow330 power 76563354.6E-112338836 3 -> 4.48810347E-337016485 Inexact Rounded
xrem330 remainder 76563354.6E-112338836 278271.585E-511481095 -> NaN Division_impossible
xsub330 subtract 76563354.6E-112338836 278271.585E-511481095 -> 7.65633546E-112338829 Inexact Rounded
xadd331 add -932499.010 873.377701E-502190452 -> -932499.010 Inexact Rounded
xcom331 compare -932499.010 873.377701E-502190452 -> -1
xdiv331 divide -932499.010 873.377701E-502190452 -> -1.06769272E+502190455 Inexact Rounded
xdvi331 divideint -932499.010 873.377701E-502190452 -> NaN Division_impossible
xmul331 multiply -932499.010 873.377701E-502190452 -> -8.14423842E-502190444 Inexact Rounded
xpow331 power -932499.010 9 -> -5.33132815E+53 Inexact Rounded
xrem331 remainder -932499.010 873.377701E-502190452 -> NaN Division_impossible
xsub331 subtract -932499.010 873.377701E-502190452 -> -932499.010 Inexact Rounded
xadd332 add -7735918.21E+799514797 -7748.78023 -> -7.73591821E+799514803 Inexact Rounded
xcom332 compare -7735918.21E+799514797 -7748.78023 -> -1
xdiv332 divide -7735918.21E+799514797 -7748.78023 -> 9.98340123E+799514799 Inexact Rounded
xdvi332 divideint -7735918.21E+799514797 -7748.78023 -> NaN Division_impossible
xmul332 multiply -7735918.21E+799514797 -7748.78023 -> 5.99439301E+799514807 Inexact Rounded
xpow332 power -7735918.21E+799514797 -7749 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem332 remainder -7735918.21E+799514797 -7748.78023 -> NaN Division_impossible
xsub332 subtract -7735918.21E+799514797 -7748.78023 -> -7.73591821E+799514803 Inexact Rounded
xadd333 add -3708780.75E+445232787 980.006567E-780728623 -> -3.70878075E+445232793 Inexact Rounded
xcom333 compare -3708780.75E+445232787 980.006567E-780728623 -> -1
xdiv333 divide -3708780.75E+445232787 980.006567E-780728623 -> -Infinity Inexact Overflow Rounded
xdvi333 divideint -3708780.75E+445232787 980.006567E-780728623 -> NaN Division_impossible
xmul333 multiply -3708780.75E+445232787 980.006567E-780728623 -> -3.63462949E-335495827 Inexact Rounded
xpow333 power -3708780.75E+445232787 10 -> Infinity Overflow Inexact Rounded
xrem333 remainder -3708780.75E+445232787 980.006567E-780728623 -> NaN Division_impossible
xsub333 subtract -3708780.75E+445232787 980.006567E-780728623 -> -3.70878075E+445232793 Inexact Rounded
xadd334 add -5205124.44E-140588661 -495394029.E-620856313 -> -5.20512444E-140588655 Inexact Rounded
xcom334 compare -5205124.44E-140588661 -495394029.E-620856313 -> -1
xdiv334 divide -5205124.44E-140588661 -495394029.E-620856313 -> 1.05070391E+480267650 Inexact Rounded
xdvi334 divideint -5205124.44E-140588661 -495394029.E-620856313 -> NaN Division_impossible
xmul334 multiply -5205124.44E-140588661 -495394029.E-620856313 -> 2.57858757E-761444959 Inexact Rounded
xpow334 power -5205124.44E-140588661 -5 -> -2.61724523E+702943271 Inexact Rounded
xrem334 remainder -5205124.44E-140588661 -495394029.E-620856313 -> NaN Division_impossible
xsub334 subtract -5205124.44E-140588661 -495394029.E-620856313 -> -5.20512444E-140588655 Inexact Rounded
xadd335 add -8868.72074 5592399.93 -> 5583531.21 Inexact Rounded
xcom335 compare -8868.72074 5592399.93 -> -1
xdiv335 divide -8868.72074 5592399.93 -> -0.00158585238 Inexact Rounded
xdvi335 divideint -8868.72074 5592399.93 -> -0
xmul335 multiply -8868.72074 5592399.93 -> -4.95974332E+10 Inexact Rounded
xpow335 power -8868.72074 5592400 -> 5.55074142E+22078017 Inexact Rounded
xrem335 remainder -8868.72074 5592399.93 -> -8868.72074
xsub335 subtract -8868.72074 5592399.93 -> -5601268.65 Inexact Rounded
xadd336 add -74.7852037E-175205809 4.14316542 -> 4.14316542 Inexact Rounded
xcom336 compare -74.7852037E-175205809 4.14316542 -> -1
xdiv336 divide -74.7852037E-175205809 4.14316542 -> -1.80502577E-175205808 Inexact Rounded
xdvi336 divideint -74.7852037E-175205809 4.14316542 -> -0
xmul336 multiply -74.7852037E-175205809 4.14316542 -> -3.09847470E-175205807 Inexact Rounded
xpow336 power -74.7852037E-175205809 4 -> 3.12797104E-700823229 Inexact Rounded
xrem336 remainder -74.7852037E-175205809 4.14316542 -> -7.47852037E-175205808
xsub336 subtract -74.7852037E-175205809 4.14316542 -> -4.14316542 Inexact Rounded
xadd337 add 84196.1091E+242628748 8.07523036E-288231467 -> 8.41961091E+242628752 Inexact Rounded
xcom337 compare 84196.1091E+242628748 8.07523036E-288231467 -> 1
xdiv337 divide 84196.1091E+242628748 8.07523036E-288231467 -> 1.04264653E+530860219 Inexact Rounded
xdvi337 divideint 84196.1091E+242628748 8.07523036E-288231467 -> NaN Division_impossible
xmul337 multiply 84196.1091E+242628748 8.07523036E-288231467 -> 6.79902976E-45602714 Inexact Rounded
xpow337 power 84196.1091E+242628748 8 -> Infinity Overflow Inexact Rounded
xrem337 remainder 84196.1091E+242628748 8.07523036E-288231467 -> NaN Division_impossible
xsub337 subtract 84196.1091E+242628748 8.07523036E-288231467 -> 8.41961091E+242628752 Inexact Rounded
xadd338 add 38660103.1 -6671.73085E+900998477 -> -6.67173085E+900998480 Inexact Rounded
xcom338 compare 38660103.1 -6671.73085E+900998477 -> 1
xdiv338 divide 38660103.1 -6671.73085E+900998477 -> -5.79461372E-900998474 Inexact Rounded
xdvi338 divideint 38660103.1 -6671.73085E+900998477 -> -0
xmul338 multiply 38660103.1 -6671.73085E+900998477 -> -2.57929803E+900998488 Inexact Rounded
xpow338 power 38660103.1 -7 -> 7.74745290E-54 Inexact Rounded
xrem338 remainder 38660103.1 -6671.73085E+900998477 -> 38660103.1
xsub338 subtract 38660103.1 -6671.73085E+900998477 -> 6.67173085E+900998480 Inexact Rounded
xadd339 add -52.2659460 -296404199E+372050476 -> -2.96404199E+372050484 Inexact Rounded
xcom339 compare -52.2659460 -296404199E+372050476 -> 1
xdiv339 divide -52.2659460 -296404199E+372050476 -> 1.76333352E-372050483 Inexact Rounded
xdvi339 divideint -52.2659460 -296404199E+372050476 -> 0
xmul339 multiply -52.2659460 -296404199E+372050476 -> 1.54918459E+372050486 Inexact Rounded
xpow339 power -52.2659460 -3 -> -0.00000700395833 Inexact Rounded
xrem339 remainder -52.2659460 -296404199E+372050476 -> -52.2659460
xsub339 subtract -52.2659460 -296404199E+372050476 -> 2.96404199E+372050484 Inexact Rounded
xadd340 add 6.06625013 -276.359186 -> -270.292936 Inexact Rounded
xcom340 compare 6.06625013 -276.359186 -> 1
xdiv340 divide 6.06625013 -276.359186 -> -0.0219506007 Inexact Rounded
xdvi340 divideint 6.06625013 -276.359186 -> -0
xmul340 multiply 6.06625013 -276.359186 -> -1676.46395 Inexact Rounded
xpow340 power 6.06625013 -276 -> 8.20339149E-217 Inexact Rounded
xrem340 remainder 6.06625013 -276.359186 -> 6.06625013
xsub340 subtract 6.06625013 -276.359186 -> 282.425436 Inexact Rounded
xadd341 add -62971617.5E-241444744 46266799.3 -> 46266799.3 Inexact Rounded
xcom341 compare -62971617.5E-241444744 46266799.3 -> -1
xdiv341 divide -62971617.5E-241444744 46266799.3 -> -1.36105411E-241444744 Inexact Rounded
xdvi341 divideint -62971617.5E-241444744 46266799.3 -> -0
xmul341 multiply -62971617.5E-241444744 46266799.3 -> -2.91349519E-241444729 Inexact Rounded
xpow341 power -62971617.5E-241444744 46266799 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem341 remainder -62971617.5E-241444744 46266799.3 -> -6.29716175E-241444737
xsub341 subtract -62971617.5E-241444744 46266799.3 -> -46266799.3 Inexact Rounded
xadd342 add -5.36917800 -311124593.E-976066491 -> -5.36917800 Inexact Rounded
xcom342 compare -5.36917800 -311124593.E-976066491 -> -1
xdiv342 divide -5.36917800 -311124593.E-976066491 -> 1.72573243E+976066483 Inexact Rounded
xdvi342 divideint -5.36917800 -311124593.E-976066491 -> NaN Division_impossible
xmul342 multiply -5.36917800 -311124593.E-976066491 -> 1.67048332E-976066482 Inexact Rounded
xpow342 power -5.36917800 -3 -> -0.00646065565 Inexact Rounded
xrem342 remainder -5.36917800 -311124593.E-976066491 -> NaN Division_impossible
xsub342 subtract -5.36917800 -311124593.E-976066491 -> -5.36917800 Inexact Rounded
xadd343 add 2467915.01 -92.5558322 -> 2467822.45 Inexact Rounded
xcom343 compare 2467915.01 -92.5558322 -> 1
xdiv343 divide 2467915.01 -92.5558322 -> -26664.0681 Inexact Rounded
xdvi343 divideint 2467915.01 -92.5558322 -> -26664
xmul343 multiply 2467915.01 -92.5558322 -> -228419928 Inexact Rounded
xpow343 power 2467915.01 -93 -> 3.26055444E-595 Inexact Rounded
xrem343 remainder 2467915.01 -92.5558322 -> 6.3002192
xsub343 subtract 2467915.01 -92.5558322 -> 2468007.57 Inexact Rounded
xadd344 add 187.232671 -840.469347 -> -653.236676
xcom344 compare 187.232671 -840.469347 -> 1
xdiv344 divide 187.232671 -840.469347 -> -0.222771564 Inexact Rounded
xdvi344 divideint 187.232671 -840.469347 -> -0
xmul344 multiply 187.232671 -840.469347 -> -157363.321 Inexact Rounded
xpow344 power 187.232671 -840 -> 1.58280862E-1909 Inexact Rounded
xrem344 remainder 187.232671 -840.469347 -> 187.232671
xsub344 subtract 187.232671 -840.469347 -> 1027.70202 Inexact Rounded
xadd345 add 81233.6823 -5192.21666E+309315093 -> -5.19221666E+309315096 Inexact Rounded
xcom345 compare 81233.6823 -5192.21666E+309315093 -> 1
xdiv345 divide 81233.6823 -5192.21666E+309315093 -> -1.56452798E-309315092 Inexact Rounded
xdvi345 divideint 81233.6823 -5192.21666E+309315093 -> -0
xmul345 multiply 81233.6823 -5192.21666E+309315093 -> -4.21782879E+309315101 Inexact Rounded
xpow345 power 81233.6823 -5 -> 2.82695763E-25 Inexact Rounded
xrem345 remainder 81233.6823 -5192.21666E+309315093 -> 81233.6823
xsub345 subtract 81233.6823 -5192.21666E+309315093 -> 5.19221666E+309315096 Inexact Rounded
xadd346 add -854.586113 -79.8715762E-853065103 -> -854.586113 Inexact Rounded
xcom346 compare -854.586113 -79.8715762E-853065103 -> -1
xdiv346 divide -854.586113 -79.8715762E-853065103 -> 1.06995023E+853065104 Inexact Rounded
xdvi346 divideint -854.586113 -79.8715762E-853065103 -> NaN Division_impossible
xmul346 multiply -854.586113 -79.8715762E-853065103 -> 6.82571398E-853065099 Inexact Rounded
xpow346 power -854.586113 -8 -> 3.51522679E-24 Inexact Rounded
xrem346 remainder -854.586113 -79.8715762E-853065103 -> NaN Division_impossible
xsub346 subtract -854.586113 -79.8715762E-853065103 -> -854.586113 Inexact Rounded
xadd347 add 78872665.3 172.102119 -> 78872837.4 Inexact Rounded
xcom347 compare 78872665.3 172.102119 -> 1
xdiv347 divide 78872665.3 172.102119 -> 458289.914 Inexact Rounded
xdvi347 divideint 78872665.3 172.102119 -> 458289
xmul347 multiply 78872665.3 172.102119 -> 1.35741528E+10 Inexact Rounded
xpow347 power 78872665.3 172 -> 1.86793137E+1358 Inexact Rounded
xrem347 remainder 78872665.3 172.102119 -> 157.285609
xsub347 subtract 78872665.3 172.102119 -> 78872493.2 Inexact Rounded
xadd348 add 328268.1E-436315617 -204.522245 -> -204.522245 Inexact Rounded
xcom348 compare 328268.1E-436315617 -204.522245 -> 1
xdiv348 divide 328268.1E-436315617 -204.522245 -> -1.60504839E-436315614 Inexact Rounded
xdvi348 divideint 328268.1E-436315617 -204.522245 -> -0
xmul348 multiply 328268.1E-436315617 -204.522245 -> -6.71381288E-436315610 Inexact Rounded
xpow348 power 328268.1E-436315617 -205 -> Infinity Overflow Inexact Rounded
xrem348 remainder 328268.1E-436315617 -204.522245 -> 3.282681E-436315612
xsub348 subtract 328268.1E-436315617 -204.522245 -> 204.522245 Inexact Rounded
xadd349 add -4037911.02E+641367645 29.5713010 -> -4.03791102E+641367651 Inexact Rounded
xcom349 compare -4037911.02E+641367645 29.5713010 -> -1
xdiv349 divide -4037911.02E+641367645 29.5713010 -> -1.36548305E+641367650 Inexact Rounded
xdvi349 divideint -4037911.02E+641367645 29.5713010 -> NaN Division_impossible
xmul349 multiply -4037911.02E+641367645 29.5713010 -> -1.19406282E+641367653 Inexact Rounded
xpow349 power -4037911.02E+641367645 30 -> Infinity Overflow Inexact Rounded
xrem349 remainder -4037911.02E+641367645 29.5713010 -> NaN Division_impossible
xsub349 subtract -4037911.02E+641367645 29.5713010 -> -4.03791102E+641367651 Inexact Rounded
xadd350 add -688755561.E-95301699 978.275312E+913812609 -> 9.78275312E+913812611 Inexact Rounded
xcom350 compare -688755561.E-95301699 978.275312E+913812609 -> -1
xdiv350 divide -688755561.E-95301699 978.275312E+913812609 -> -0E-1000000007 Inexact Rounded Underflow Subnormal Clamped
xdvi350 divideint -688755561.E-95301699 978.275312E+913812609 -> -0
xmul350 multiply -688755561.E-95301699 978.275312E+913812609 -> -6.73792561E+818510921 Inexact Rounded
xpow350 power -688755561.E-95301699 10 -> 2.40243244E-953016902 Inexact Rounded
xrem350 remainder -688755561.E-95301699 978.275312E+913812609 -> -6.88755561E-95301691
xsub350 subtract -688755561.E-95301699 978.275312E+913812609 -> -9.78275312E+913812611 Inexact Rounded
xadd351 add -5.47345502 59818.7580 -> 59813.2845 Inexact Rounded
xcom351 compare -5.47345502 59818.7580 -> -1
xdiv351 divide -5.47345502 59818.7580 -> -0.0000915006463 Inexact Rounded
xdvi351 divideint -5.47345502 59818.7580 -> -0
xmul351 multiply -5.47345502 59818.7580 -> -327415.281 Inexact Rounded
xpow351 power -5.47345502 59819 -> -1.16914146E+44162 Inexact Rounded
xrem351 remainder -5.47345502 59818.7580 -> -5.47345502
xsub351 subtract -5.47345502 59818.7580 -> -59824.2315 Inexact Rounded
xadd352 add 563891620E-361354567 -845900362. -> -845900362 Inexact Rounded
xcom352 compare 563891620E-361354567 -845900362. -> 1
xdiv352 divide 563891620E-361354567 -845900362. -> -6.66617069E-361354568 Inexact Rounded
xdvi352 divideint 563891620E-361354567 -845900362. -> -0
xmul352 multiply 563891620E-361354567 -845900362. -> -4.76996125E-361354550 Inexact Rounded
xpow352 power 563891620E-361354567 -845900362 -> Infinity Overflow Inexact Rounded
xrem352 remainder 563891620E-361354567 -845900362. -> 5.63891620E-361354559
xsub352 subtract 563891620E-361354567 -845900362. -> 845900362 Inexact Rounded
xadd353 add -69.7231286 85773.7504 -> 85704.0273 Inexact Rounded
xcom353 compare -69.7231286 85773.7504 -> -1
xdiv353 divide -69.7231286 85773.7504 -> -0.000812872566 Inexact Rounded
xdvi353 divideint -69.7231286 85773.7504 -> -0
xmul353 multiply -69.7231286 85773.7504 -> -5980414.23 Inexact Rounded
xpow353 power -69.7231286 85774 -> 6.41714261E+158113 Inexact Rounded
xrem353 remainder -69.7231286 85773.7504 -> -69.7231286
xsub353 subtract -69.7231286 85773.7504 -> -85843.4735 Inexact Rounded
xadd354 add 5125.51188 73814638.4E-500934741 -> 5125.51188 Inexact Rounded
xcom354 compare 5125.51188 73814638.4E-500934741 -> 1
xdiv354 divide 5125.51188 73814638.4E-500934741 -> 6.94376074E+500934736 Inexact Rounded
xdvi354 divideint 5125.51188 73814638.4E-500934741 -> NaN Division_impossible
xmul354 multiply 5125.51188 73814638.4E-500934741 -> 3.78337806E-500934730 Inexact Rounded
xpow354 power 5125.51188 7 -> 9.29310216E+25 Inexact Rounded
xrem354 remainder 5125.51188 73814638.4E-500934741 -> NaN Division_impossible
xsub354 subtract 5125.51188 73814638.4E-500934741 -> 5125.51188 Inexact Rounded
xadd355 add -54.6254096 -332921899. -> -332921954 Inexact Rounded
xcom355 compare -54.6254096 -332921899. -> 1
xdiv355 divide -54.6254096 -332921899. -> 1.64078752E-7 Inexact Rounded
xdvi355 divideint -54.6254096 -332921899. -> 0
xmul355 multiply -54.6254096 -332921899. -> 1.81859951E+10 Inexact Rounded
xpow355 power -54.6254096 -332921899 -> -1.01482569E-578416745 Inexact Rounded
xrem355 remainder -54.6254096 -332921899. -> -54.6254096
xsub355 subtract -54.6254096 -332921899. -> 332921844 Inexact Rounded
xadd356 add -9.04778095E-591874079 8719.40286 -> 8719.40286 Inexact Rounded
xcom356 compare -9.04778095E-591874079 8719.40286 -> -1
xdiv356 divide -9.04778095E-591874079 8719.40286 -> -1.03766062E-591874082 Inexact Rounded
xdvi356 divideint -9.04778095E-591874079 8719.40286 -> -0
xmul356 multiply -9.04778095E-591874079 8719.40286 -> -7.88912471E-591874075 Inexact Rounded
xpow356 power -9.04778095E-591874079 8719 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem356 remainder -9.04778095E-591874079 8719.40286 -> -9.04778095E-591874079
xsub356 subtract -9.04778095E-591874079 8719.40286 -> -8719.40286 Inexact Rounded
xadd357 add -21006.1733E+884684431 -48872.9175 -> -2.10061733E+884684435 Inexact Rounded
xcom357 compare -21006.1733E+884684431 -48872.9175 -> -1
xdiv357 divide -21006.1733E+884684431 -48872.9175 -> 4.29812141E+884684430 Inexact Rounded
xdvi357 divideint -21006.1733E+884684431 -48872.9175 -> NaN Division_impossible
xmul357 multiply -21006.1733E+884684431 -48872.9175 -> 1.02663297E+884684440 Inexact Rounded
xpow357 power -21006.1733E+884684431 -48873 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem357 remainder -21006.1733E+884684431 -48872.9175 -> NaN Division_impossible
xsub357 subtract -21006.1733E+884684431 -48872.9175 -> -2.10061733E+884684435 Inexact Rounded
xadd358 add -1546783 -51935370.4 -> -53482153.4
xcom358 compare -1546783 -51935370.4 -> 1
xdiv358 divide -1546783 -51935370.4 -> 0.0297828433 Inexact Rounded
xdvi358 divideint -1546783 -51935370.4 -> 0
xmul358 multiply -1546783 -51935370.4 -> 8.03327480E+13 Inexact Rounded
xpow358 power -1546783 -51935370 -> 3.36022461E-321450306 Inexact Rounded
xrem358 remainder -1546783 -51935370.4 -> -1546783.0
xsub358 subtract -1546783 -51935370.4 -> 50388587.4
xadd359 add 61302486.8 205.490417 -> 61302692.3 Inexact Rounded
xcom359 compare 61302486.8 205.490417 -> 1
xdiv359 divide 61302486.8 205.490417 -> 298322.850 Inexact Rounded
xdvi359 divideint 61302486.8 205.490417 -> 298322
xmul359 multiply 61302486.8 205.490417 -> 1.25970736E+10 Inexact Rounded
xpow359 power 61302486.8 205 -> 2.71024755E+1596 Inexact Rounded
xrem359 remainder 61302486.8 205.490417 -> 174.619726
xsub359 subtract 61302486.8 205.490417 -> 61302281.3 Inexact Rounded
xadd360 add -318180109. -54008744.6E-170931002 -> -318180109 Inexact Rounded
xcom360 compare -318180109. -54008744.6E-170931002 -> -1
xdiv360 divide -318180109. -54008744.6E-170931002 -> 5.89127023E+170931002 Inexact Rounded
xdvi360 divideint -318180109. -54008744.6E-170931002 -> NaN Division_impossible
xmul360 multiply -318180109. -54008744.6E-170931002 -> 1.71845082E-170930986 Inexact Rounded
xpow360 power -318180109. -5 -> -3.06644280E-43 Inexact Rounded
xrem360 remainder -318180109. -54008744.6E-170931002 -> NaN Division_impossible
xsub360 subtract -318180109. -54008744.6E-170931002 -> -318180109 Inexact Rounded
xadd361 add -28486137.1E+901441714 -42454.940 -> -2.84861371E+901441721 Inexact Rounded
xcom361 compare -28486137.1E+901441714 -42454.940 -> -1
xdiv361 divide -28486137.1E+901441714 -42454.940 -> 6.70973439E+901441716 Inexact Rounded
xdvi361 divideint -28486137.1E+901441714 -42454.940 -> NaN Division_impossible
xmul361 multiply -28486137.1E+901441714 -42454.940 -> 1.20937724E+901441726 Inexact Rounded
xpow361 power -28486137.1E+901441714 -42455 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem361 remainder -28486137.1E+901441714 -42454.940 -> NaN Division_impossible
xsub361 subtract -28486137.1E+901441714 -42454.940 -> -2.84861371E+901441721 Inexact Rounded
xadd362 add -546398328. -27.9149712 -> -546398356 Inexact Rounded
xcom362 compare -546398328. -27.9149712 -> -1
xdiv362 divide -546398328. -27.9149712 -> 19573666.2 Inexact Rounded
xdvi362 divideint -546398328. -27.9149712 -> 19573666
xmul362 multiply -546398328. -27.9149712 -> 1.52526936E+10 Inexact Rounded
xpow362 power -546398328. -28 -> 2.23737032E-245 Inexact Rounded
xrem362 remainder -546398328. -27.9149712 -> -5.3315808
xsub362 subtract -546398328. -27.9149712 -> -546398300 Inexact Rounded
xadd363 add 5402066.1E-284978216 622.751128 -> 622.751128 Inexact Rounded
xcom363 compare 5402066.1E-284978216 622.751128 -> -1
xdiv363 divide 5402066.1E-284978216 622.751128 -> 8.67451837E-284978213 Inexact Rounded
xdvi363 divideint 5402066.1E-284978216 622.751128 -> 0
xmul363 multiply 5402066.1E-284978216 622.751128 -> 3.36414276E-284978207 Inexact Rounded
xpow363 power 5402066.1E-284978216 623 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem363 remainder 5402066.1E-284978216 622.751128 -> 5.4020661E-284978210
xsub363 subtract 5402066.1E-284978216 622.751128 -> -622.751128 Inexact Rounded
xadd364 add 18845620 3129.43753 -> 18848749.4 Inexact Rounded
xcom364 compare 18845620 3129.43753 -> 1
xdiv364 divide 18845620 3129.43753 -> 6022.04704 Inexact Rounded
xdvi364 divideint 18845620 3129.43753 -> 6022
xmul364 multiply 18845620 3129.43753 -> 5.89761905E+10 Inexact Rounded
xpow364 power 18845620 3129 -> 1.35967443E+22764 Inexact Rounded
xrem364 remainder 18845620 3129.43753 -> 147.19434
xsub364 subtract 18845620 3129.43753 -> 18842490.6 Inexact Rounded
xadd365 add 50707.1412E+912475670 -198098.186E+701407524 -> 5.07071412E+912475674 Inexact Rounded
xcom365 compare 50707.1412E+912475670 -198098.186E+701407524 -> 1
xdiv365 divide 50707.1412E+912475670 -198098.186E+701407524 -> -2.55969740E+211068145 Inexact Rounded
xdvi365 divideint 50707.1412E+912475670 -198098.186E+701407524 -> NaN Division_impossible
xmul365 multiply 50707.1412E+912475670 -198098.186E+701407524 -> -Infinity Inexact Overflow Rounded
xpow365 power 50707.1412E+912475670 -2 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem365 remainder 50707.1412E+912475670 -198098.186E+701407524 -> NaN Division_impossible
xsub365 subtract 50707.1412E+912475670 -198098.186E+701407524 -> 5.07071412E+912475674 Inexact Rounded
xadd366 add 55.8245006E+928885991 99170843.9E-47402167 -> 5.58245006E+928885992 Inexact Rounded
xcom366 compare 55.8245006E+928885991 99170843.9E-47402167 -> 1
xdiv366 divide 55.8245006E+928885991 99170843.9E-47402167 -> 5.62912429E+976288151 Inexact Rounded
xdvi366 divideint 55.8245006E+928885991 99170843.9E-47402167 -> NaN Division_impossible
xmul366 multiply 55.8245006E+928885991 99170843.9E-47402167 -> 5.53616283E+881483833 Inexact Rounded
xpow366 power 55.8245006E+928885991 10 -> Infinity Overflow Inexact Rounded
xrem366 remainder 55.8245006E+928885991 99170843.9E-47402167 -> NaN Division_impossible
xsub366 subtract 55.8245006E+928885991 99170843.9E-47402167 -> 5.58245006E+928885992 Inexact Rounded
xadd367 add 13.8003883E-386224921 -84126481.9E-296378341 -> -8.41264819E-296378334 Inexact Rounded
xcom367 compare 13.8003883E-386224921 -84126481.9E-296378341 -> 1
xdiv367 divide 13.8003883E-386224921 -84126481.9E-296378341 -> -1.64043331E-89846587 Inexact Rounded
xdvi367 divideint 13.8003883E-386224921 -84126481.9E-296378341 -> -0
xmul367 multiply 13.8003883E-386224921 -84126481.9E-296378341 -> -1.16097812E-682603253 Inexact Rounded
xpow367 power 13.8003883E-386224921 -8 -> Infinity Overflow Inexact Rounded
xrem367 remainder 13.8003883E-386224921 -84126481.9E-296378341 -> 1.38003883E-386224920
xsub367 subtract 13.8003883E-386224921 -84126481.9E-296378341 -> 8.41264819E-296378334 Inexact Rounded
xadd368 add 9820.90457 46671.5915 -> 56492.4961 Inexact Rounded
xcom368 compare 9820.90457 46671.5915 -> -1
xdiv368 divide 9820.90457 46671.5915 -> 0.210425748 Inexact Rounded
xdvi368 divideint 9820.90457 46671.5915 -> 0
xmul368 multiply 9820.90457 46671.5915 -> 458357246 Inexact Rounded
xpow368 power 9820.90457 46672 -> 4.94753070E+186321 Inexact Rounded
xrem368 remainder 9820.90457 46671.5915 -> 9820.90457
xsub368 subtract 9820.90457 46671.5915 -> -36850.6869 Inexact Rounded
xadd369 add 7.22436006E+831949153 -11168830E+322331045 -> 7.22436006E+831949153 Inexact Rounded
xcom369 compare 7.22436006E+831949153 -11168830E+322331045 -> 1
xdiv369 divide 7.22436006E+831949153 -11168830E+322331045 -> -6.46832306E+509618101 Inexact Rounded
xdvi369 divideint 7.22436006E+831949153 -11168830E+322331045 -> NaN Division_impossible
xmul369 multiply 7.22436006E+831949153 -11168830E+322331045 -> -Infinity Inexact Overflow Rounded
xpow369 power 7.22436006E+831949153 -1 -> 1.38420565E-831949154 Inexact Rounded
xrem369 remainder 7.22436006E+831949153 -11168830E+322331045 -> NaN Division_impossible
xsub369 subtract 7.22436006E+831949153 -11168830E+322331045 -> 7.22436006E+831949153 Inexact Rounded
xadd370 add 472648900 -207.784153 -> 472648692 Inexact Rounded
xcom370 compare 472648900 -207.784153 -> 1
xdiv370 divide 472648900 -207.784153 -> -2274711.01 Inexact Rounded
xdvi370 divideint 472648900 -207.784153 -> -2274711
xmul370 multiply 472648900 -207.784153 -> -9.82089514E+10 Inexact Rounded
xpow370 power 472648900 -208 -> 4.96547145E-1805 Inexact Rounded
xrem370 remainder 472648900 -207.784153 -> 1.545217
xsub370 subtract 472648900 -207.784153 -> 472649108 Inexact Rounded
xadd371 add -8754.49306 -818.165153E+631475457 -> -8.18165153E+631475459 Inexact Rounded
xcom371 compare -8754.49306 -818.165153E+631475457 -> 1
xdiv371 divide -8754.49306 -818.165153E+631475457 -> 1.07001539E-631475456 Inexact Rounded
xdvi371 divideint -8754.49306 -818.165153E+631475457 -> 0
xmul371 multiply -8754.49306 -818.165153E+631475457 -> 7.16262115E+631475463 Inexact Rounded
xpow371 power -8754.49306 -8 -> 2.89835767E-32 Inexact Rounded
xrem371 remainder -8754.49306 -818.165153E+631475457 -> -8754.49306
xsub371 subtract -8754.49306 -818.165153E+631475457 -> 8.18165153E+631475459 Inexact Rounded
xadd372 add 98750864 191380.551 -> 98942244.6 Inexact Rounded
xcom372 compare 98750864 191380.551 -> 1
xdiv372 divide 98750864 191380.551 -> 515.992161 Inexact Rounded
xdvi372 divideint 98750864 191380.551 -> 515
xmul372 multiply 98750864 191380.551 -> 1.88989948E+13 Inexact Rounded
xpow372 power 98750864 191381 -> 1.70908809E+1530003 Inexact Rounded
xrem372 remainder 98750864 191380.551 -> 189880.235
xsub372 subtract 98750864 191380.551 -> 98559483.4 Inexact Rounded
xadd373 add 725292561. -768963606.E+340762986 -> -7.68963606E+340762994 Inexact Rounded
xcom373 compare 725292561. -768963606.E+340762986 -> 1
xdiv373 divide 725292561. -768963606.E+340762986 -> -9.43207917E-340762987 Inexact Rounded
xdvi373 divideint 725292561. -768963606.E+340762986 -> -0
xmul373 multiply 725292561. -768963606.E+340762986 -> -5.57723583E+340763003 Inexact Rounded
xpow373 power 725292561. -8 -> 1.30585277E-71 Inexact Rounded
xrem373 remainder 725292561. -768963606.E+340762986 -> 725292561
xsub373 subtract 725292561. -768963606.E+340762986 -> 7.68963606E+340762994 Inexact Rounded
xadd374 add 1862.80445 648254483. -> 648256346 Inexact Rounded
xcom374 compare 1862.80445 648254483. -> -1
xdiv374 divide 1862.80445 648254483. -> 0.00000287356972 Inexact Rounded
xdvi374 divideint 1862.80445 648254483. -> 0
xmul374 multiply 1862.80445 648254483. -> 1.20757134E+12 Inexact Rounded
xpow374 power 1862.80445 648254483 -> Infinity Overflow Inexact Rounded
xrem374 remainder 1862.80445 648254483. -> 1862.80445
xsub374 subtract 1862.80445 648254483. -> -648252620 Inexact Rounded
xadd375 add -5549320.1 -93580684.1 -> -99130004.2
xcom375 compare -5549320.1 -93580684.1 -> 1
xdiv375 divide -5549320.1 -93580684.1 -> 0.0592998454 Inexact Rounded
xdvi375 divideint -5549320.1 -93580684.1 -> 0
xmul375 multiply -5549320.1 -93580684.1 -> 5.19309171E+14 Inexact Rounded
xpow375 power -5549320.1 -93580684 -> 4.20662079E-631130572 Inexact Rounded
xrem375 remainder -5549320.1 -93580684.1 -> -5549320.1
xsub375 subtract -5549320.1 -93580684.1 -> 88031364.0
xadd376 add -14677053.1 -25784.7358 -> -14702837.8 Inexact Rounded
xcom376 compare -14677053.1 -25784.7358 -> -1
xdiv376 divide -14677053.1 -25784.7358 -> 569.214795 Inexact Rounded
xdvi376 divideint -14677053.1 -25784.7358 -> 569
xmul376 multiply -14677053.1 -25784.7358 -> 3.78443937E+11 Inexact Rounded
xpow376 power -14677053.1 -25785 -> -1.64760831E-184792 Inexact Rounded
xrem376 remainder -14677053.1 -25784.7358 -> -5538.4298
xsub376 subtract -14677053.1 -25784.7358 -> -14651268.4 Inexact Rounded
xadd377 add 547402.308E+571687617 -7835797.01E+500067364 -> 5.47402308E+571687622 Inexact Rounded
xcom377 compare 547402.308E+571687617 -7835797.01E+500067364 -> 1
xdiv377 divide 547402.308E+571687617 -7835797.01E+500067364 -> -6.98591742E+71620251 Inexact Rounded
xdvi377 divideint 547402.308E+571687617 -7835797.01E+500067364 -> NaN Division_impossible
xmul377 multiply 547402.308E+571687617 -7835797.01E+500067364 -> -Infinity Inexact Overflow Rounded
xpow377 power 547402.308E+571687617 -8 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem377 remainder 547402.308E+571687617 -7835797.01E+500067364 -> NaN Division_impossible
xsub377 subtract 547402.308E+571687617 -7835797.01E+500067364 -> 5.47402308E+571687622 Inexact Rounded
xadd378 add -4131738.09 7579.07566 -> -4124159.01 Inexact Rounded
xcom378 compare -4131738.09 7579.07566 -> -1
xdiv378 divide -4131738.09 7579.07566 -> -545.150659 Inexact Rounded
xdvi378 divideint -4131738.09 7579.07566 -> -545
xmul378 multiply -4131738.09 7579.07566 -> -3.13147556E+10 Inexact Rounded
xpow378 power -4131738.09 7579 -> -4.68132794E+50143 Inexact Rounded
xrem378 remainder -4131738.09 7579.07566 -> -1141.85530
xsub378 subtract -4131738.09 7579.07566 -> -4139317.17 Inexact Rounded
xadd379 add 504544.648 -7678.96133E-662143268 -> 504544.648 Inexact Rounded
xcom379 compare 504544.648 -7678.96133E-662143268 -> 1
xdiv379 divide 504544.648 -7678.96133E-662143268 -> -6.57048039E+662143269 Inexact Rounded
xdvi379 divideint 504544.648 -7678.96133E-662143268 -> NaN Division_impossible
xmul379 multiply 504544.648 -7678.96133E-662143268 -> -3.87437884E-662143259 Inexact Rounded
xpow379 power 504544.648 -8 -> 2.38124001E-46 Inexact Rounded
xrem379 remainder 504544.648 -7678.96133E-662143268 -> NaN Division_impossible
xsub379 subtract 504544.648 -7678.96133E-662143268 -> 504544.648 Inexact Rounded
xadd380 add 829898241 8912.99114E+929228149 -> 8.91299114E+929228152 Inexact Rounded
xcom380 compare 829898241 8912.99114E+929228149 -> -1
xdiv380 divide 829898241 8912.99114E+929228149 -> 9.31110811E-929228145 Inexact Rounded
xdvi380 divideint 829898241 8912.99114E+929228149 -> 0
xmul380 multiply 829898241 8912.99114E+929228149 -> 7.39687567E+929228161 Inexact Rounded
xpow380 power 829898241 9 -> 1.86734084E+80 Inexact Rounded
xrem380 remainder 829898241 8912.99114E+929228149 -> 829898241
xsub380 subtract 829898241 8912.99114E+929228149 -> -8.91299114E+929228152 Inexact Rounded
xadd381 add 53.6891691 -11.2371140 -> 42.4520551
xcom381 compare 53.6891691 -11.2371140 -> 1
xdiv381 divide 53.6891691 -11.2371140 -> -4.77784323 Inexact Rounded
xdvi381 divideint 53.6891691 -11.2371140 -> -4
xmul381 multiply 53.6891691 -11.2371140 -> -603.311314 Inexact Rounded
xpow381 power 53.6891691 -11 -> 9.35936725E-20 Inexact Rounded
xrem381 remainder 53.6891691 -11.2371140 -> 8.7407131
xsub381 subtract 53.6891691 -11.2371140 -> 64.9262831
xadd382 add -93951823.4 -25317.8645 -> -93977141.3 Inexact Rounded
xcom382 compare -93951823.4 -25317.8645 -> -1
xdiv382 divide -93951823.4 -25317.8645 -> 3710.89052 Inexact Rounded
xdvi382 divideint -93951823.4 -25317.8645 -> 3710
xmul382 multiply -93951823.4 -25317.8645 -> 2.37865953E+12 Inexact Rounded
xpow382 power -93951823.4 -25318 -> 9.67857714E-201859 Inexact Rounded
xrem382 remainder -93951823.4 -25317.8645 -> -22546.1050
xsub382 subtract -93951823.4 -25317.8645 -> -93926505.5 Inexact Rounded
xadd383 add 446919.123 951338490. -> 951785409 Inexact Rounded
xcom383 compare 446919.123 951338490. -> -1
xdiv383 divide 446919.123 951338490. -> 0.000469779293 Inexact Rounded
xdvi383 divideint 446919.123 951338490. -> 0
xmul383 multiply 446919.123 951338490. -> 4.25171364E+14 Inexact Rounded
xpow383 power 446919.123 951338490 -> Infinity Overflow Inexact Rounded
xrem383 remainder 446919.123 951338490. -> 446919.123
xsub383 subtract 446919.123 951338490. -> -950891571 Inexact Rounded
xadd384 add -8.01787748 -88.3076852 -> -96.3255627 Inexact Rounded
xcom384 compare -8.01787748 -88.3076852 -> 1
xdiv384 divide -8.01787748 -88.3076852 -> 0.0907947871 Inexact Rounded
xdvi384 divideint -8.01787748 -88.3076852 -> 0
xmul384 multiply -8.01787748 -88.3076852 -> 708.040200 Inexact Rounded
xpow384 power -8.01787748 -88 -> 2.77186088E-80 Inexact Rounded
xrem384 remainder -8.01787748 -88.3076852 -> -8.01787748
xsub384 subtract -8.01787748 -88.3076852 -> 80.2898077 Inexact Rounded
xadd385 add 517458139 -999731.548 -> 516458407 Inexact Rounded
xcom385 compare 517458139 -999731.548 -> 1
xdiv385 divide 517458139 -999731.548 -> -517.597089 Inexact Rounded
xdvi385 divideint 517458139 -999731.548 -> -517
xmul385 multiply 517458139 -999731.548 -> -5.17319226E+14 Inexact Rounded
xpow385 power 517458139 -999732 -> 1.24821346E-8711540 Inexact Rounded
xrem385 remainder 517458139 -999731.548 -> 596928.684
xsub385 subtract 517458139 -999731.548 -> 518457871 Inexact Rounded
xadd386 add -405543440 -4013.18295 -> -405547453 Inexact Rounded
xcom386 compare -405543440 -4013.18295 -> -1
xdiv386 divide -405543440 -4013.18295 -> 101052.816 Inexact Rounded
xdvi386 divideint -405543440 -4013.18295 -> 101052
xmul386 multiply -405543440 -4013.18295 -> 1.62752002E+12 Inexact Rounded
xpow386 power -405543440 -4013 -> -8.83061932E-34545 Inexact Rounded
xrem386 remainder -405543440 -4013.18295 -> -3276.53660
xsub386 subtract -405543440 -4013.18295 -> -405539427 Inexact Rounded
xadd387 add -49245250.1E+682760825 -848776.637 -> -4.92452501E+682760832 Inexact Rounded
xcom387 compare -49245250.1E+682760825 -848776.637 -> -1
xdiv387 divide -49245250.1E+682760825 -848776.637 -> 5.80190924E+682760826 Inexact Rounded
xdvi387 divideint -49245250.1E+682760825 -848776.637 -> NaN Division_impossible
xmul387 multiply -49245250.1E+682760825 -848776.637 -> 4.17982178E+682760838 Inexact Rounded
xpow387 power -49245250.1E+682760825 -848777 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem387 remainder -49245250.1E+682760825 -848776.637 -> NaN Division_impossible
xsub387 subtract -49245250.1E+682760825 -848776.637 -> -4.92452501E+682760832 Inexact Rounded
xadd388 add -151144455 -170371.29 -> -151314826 Inexact Rounded
xcom388 compare -151144455 -170371.29 -> -1
xdiv388 divide -151144455 -170371.29 -> 887.147447 Inexact Rounded
xdvi388 divideint -151144455 -170371.29 -> 887
xmul388 multiply -151144455 -170371.29 -> 2.57506758E+13 Inexact Rounded
xpow388 power -151144455 -170371 -> -5.86496369E-1393532 Inexact Rounded
xrem388 remainder -151144455 -170371.29 -> -25120.77
xsub388 subtract -151144455 -170371.29 -> -150974084 Inexact Rounded
xadd389 add -729236746.E+662737067 9.10823602 -> -7.29236746E+662737075 Inexact Rounded
xcom389 compare -729236746.E+662737067 9.10823602 -> -1
xdiv389 divide -729236746.E+662737067 9.10823602 -> -8.00634442E+662737074 Inexact Rounded
xdvi389 divideint -729236746.E+662737067 9.10823602 -> NaN Division_impossible
xmul389 multiply -729236746.E+662737067 9.10823602 -> -6.64206040E+662737076 Inexact Rounded
xpow389 power -729236746.E+662737067 9 -> -Infinity Overflow Inexact Rounded
xrem389 remainder -729236746.E+662737067 9.10823602 -> NaN Division_impossible
xsub389 subtract -729236746.E+662737067 9.10823602 -> -7.29236746E+662737075 Inexact Rounded
xadd390 add 534.394729 -2369839.37 -> -2369304.98 Inexact Rounded
xcom390 compare 534.394729 -2369839.37 -> 1
xdiv390 divide 534.394729 -2369839.37 -> -0.000225498291 Inexact Rounded
xdvi390 divideint 534.394729 -2369839.37 -> -0
xmul390 multiply 534.394729 -2369839.37 -> -1.26642967E+9 Inexact Rounded
xpow390 power 534.394729 -2369839 -> 7.12522896E-6464595 Inexact Rounded
xrem390 remainder 534.394729 -2369839.37 -> 534.394729
xsub390 subtract 534.394729 -2369839.37 -> 2370373.76 Inexact Rounded
xadd391 add 89100.1797 224.370309 -> 89324.5500 Inexact Rounded
xcom391 compare 89100.1797 224.370309 -> 1
xdiv391 divide 89100.1797 224.370309 -> 397.112167 Inexact Rounded
xdvi391 divideint 89100.1797 224.370309 -> 397
xmul391 multiply 89100.1797 224.370309 -> 19991434.9 Inexact Rounded
xpow391 power 89100.1797 224 -> 5.92654936E+1108 Inexact Rounded
xrem391 remainder 89100.1797 224.370309 -> 25.167027
xsub391 subtract 89100.1797 224.370309 -> 88875.8094 Inexact Rounded
xadd392 add -821377.777 38.552821 -> -821339.224 Inexact Rounded
xcom392 compare -821377.777 38.552821 -> -1
xdiv392 divide -821377.777 38.552821 -> -21305.2575 Inexact Rounded
xdvi392 divideint -821377.777 38.552821 -> -21305
xmul392 multiply -821377.777 38.552821 -> -31666430.4 Inexact Rounded
xpow392 power -821377.777 39 -> -4.64702482E+230 Inexact Rounded
xrem392 remainder -821377.777 38.552821 -> -9.925595
xsub392 subtract -821377.777 38.552821 -> -821416.330 Inexact Rounded
xadd393 add -392640.782 -2571619.5E+113237865 -> -2.57161950E+113237871 Inexact Rounded
xcom393 compare -392640.782 -2571619.5E+113237865 -> 1
xdiv393 divide -392640.782 -2571619.5E+113237865 -> 1.52682301E-113237866 Inexact Rounded
xdvi393 divideint -392640.782 -2571619.5E+113237865 -> 0
xmul393 multiply -392640.782 -2571619.5E+113237865 -> 1.00972269E+113237877 Inexact Rounded
xpow393 power -392640.782 -3 -> -1.65201422E-17 Inexact Rounded
xrem393 remainder -392640.782 -2571619.5E+113237865 -> -392640.782
xsub393 subtract -392640.782 -2571619.5E+113237865 -> 2.57161950E+113237871 Inexact Rounded
xadd394 add -651397.712 -723.59673 -> -652121.309 Inexact Rounded
xcom394 compare -651397.712 -723.59673 -> -1
xdiv394 divide -651397.712 -723.59673 -> 900.222023 Inexact Rounded
xdvi394 divideint -651397.712 -723.59673 -> 900
xmul394 multiply -651397.712 -723.59673 -> 471349254 Inexact Rounded
xpow394 power -651397.712 -724 -> 5.96115415E-4210 Inexact Rounded
xrem394 remainder -651397.712 -723.59673 -> -160.65500
xsub394 subtract -651397.712 -723.59673 -> -650674.115 Inexact Rounded
xadd395 add 86.6890892 940380864 -> 940380951 Inexact Rounded
xcom395 compare 86.6890892 940380864 -> -1
xdiv395 divide 86.6890892 940380864 -> 9.21850843E-8 Inexact Rounded
xdvi395 divideint 86.6890892 940380864 -> 0
xmul395 multiply 86.6890892 940380864 -> 8.15207606E+10 Inexact Rounded
xpow395 power 86.6890892 940380864 -> Infinity Overflow Inexact Rounded
xrem395 remainder 86.6890892 940380864 -> 86.6890892
xsub395 subtract 86.6890892 940380864 -> -940380777 Inexact Rounded
xadd396 add 4880.06442E-382222621 -115627239E-912834031 -> 4.88006442E-382222618 Inexact Rounded
xcom396 compare 4880.06442E-382222621 -115627239E-912834031 -> 1
xdiv396 divide 4880.06442E-382222621 -115627239E-912834031 -> -4.22051453E+530611405 Inexact Rounded
xdvi396 divideint 4880.06442E-382222621 -115627239E-912834031 -> NaN Division_impossible
xmul396 multiply 4880.06442E-382222621 -115627239E-912834031 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xpow396 power 4880.06442E-382222621 -1 -> 2.04915328E+382222617 Inexact Rounded
xrem396 remainder 4880.06442E-382222621 -115627239E-912834031 -> NaN Division_impossible
xsub396 subtract 4880.06442E-382222621 -115627239E-912834031 -> 4.88006442E-382222618 Inexact Rounded
xadd397 add 173398265E-532383158 3462.51450E+80986915 -> 3.46251450E+80986918 Inexact Rounded
xcom397 compare 173398265E-532383158 3462.51450E+80986915 -> -1
xdiv397 divide 173398265E-532383158 3462.51450E+80986915 -> 5.00787116E-613370069 Inexact Rounded
xdvi397 divideint 173398265E-532383158 3462.51450E+80986915 -> 0
xmul397 multiply 173398265E-532383158 3462.51450E+80986915 -> 6.00394007E-451396232 Inexact Rounded
xpow397 power 173398265E-532383158 3 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem397 remainder 173398265E-532383158 3462.51450E+80986915 -> 1.73398265E-532383150
xsub397 subtract 173398265E-532383158 3462.51450E+80986915 -> -3.46251450E+80986918 Inexact Rounded
xadd398 add -1522176.78 -6631061.77 -> -8153238.55
xcom398 compare -1522176.78 -6631061.77 -> 1
xdiv398 divide -1522176.78 -6631061.77 -> 0.229552496 Inexact Rounded
xdvi398 divideint -1522176.78 -6631061.77 -> 0
xmul398 multiply -1522176.78 -6631061.77 -> 1.00936483E+13 Inexact Rounded
xpow398 power -1522176.78 -6631062 -> 4.54268854E-40996310 Inexact Rounded
xrem398 remainder -1522176.78 -6631061.77 -> -1522176.78
xsub398 subtract -1522176.78 -6631061.77 -> 5108884.99
xadd399 add 538.10453 522934310 -> 522934848 Inexact Rounded
xcom399 compare 538.10453 522934310 -> -1
xdiv399 divide 538.10453 522934310 -> 0.00000102900980 Inexact Rounded
xdvi399 divideint 538.10453 522934310 -> 0
xmul399 multiply 538.10453 522934310 -> 2.81393321E+11 Inexact Rounded
xpow399 power 538.10453 522934310 -> Infinity Overflow Inexact Rounded
xrem399 remainder 538.10453 522934310 -> 538.10453
xsub399 subtract 538.10453 522934310 -> -522933772 Inexact Rounded
xadd400 add 880243.444E-750940977 -354.601578E-204943740 -> -3.54601578E-204943738 Inexact Rounded
xcom400 compare 880243.444E-750940977 -354.601578E-204943740 -> 1
xdiv400 divide 880243.444E-750940977 -354.601578E-204943740 -> -2.48234497E-545997234 Inexact Rounded
xdvi400 divideint 880243.444E-750940977 -354.601578E-204943740 -> -0
xmul400 multiply 880243.444E-750940977 -354.601578E-204943740 -> -3.12135714E-955884709 Inexact Rounded
xpow400 power 880243.444E-750940977 -4 -> Infinity Overflow Inexact Rounded
xrem400 remainder 880243.444E-750940977 -354.601578E-204943740 -> 8.80243444E-750940972
xsub400 subtract 880243.444E-750940977 -354.601578E-204943740 -> 3.54601578E-204943738 Inexact Rounded
xadd401 add 968370.780 6677268.73 -> 7645639.51 Rounded
xcom401 compare 968370.780 6677268.73 -> -1
xdiv401 divide 968370.780 6677268.73 -> 0.145024982 Inexact Rounded
xdvi401 divideint 968370.780 6677268.73 -> 0
xmul401 multiply 968370.780 6677268.73 -> 6.46607193E+12 Inexact Rounded
xpow401 power 968370.780 6677269 -> 3.29990931E+39970410 Inexact Rounded
xrem401 remainder 968370.780 6677268.73 -> 968370.780
xsub401 subtract 968370.780 6677268.73 -> -5708897.95 Rounded
xadd402 add -97.7474945 31248241.5 -> 31248143.8 Inexact Rounded
xcom402 compare -97.7474945 31248241.5 -> -1
xdiv402 divide -97.7474945 31248241.5 -> -0.00000312809585 Inexact Rounded
xdvi402 divideint -97.7474945 31248241.5 -> -0
xmul402 multiply -97.7474945 31248241.5 -> -3.05443731E+9 Inexact Rounded
xpow402 power -97.7474945 31248242 -> 2.90714257E+62187302 Inexact Rounded
xrem402 remainder -97.7474945 31248241.5 -> -97.7474945
xsub402 subtract -97.7474945 31248241.5 -> -31248339.2 Inexact Rounded
xadd403 add -187582786.E+369916952 957840435E+744365567 -> 9.57840435E+744365575 Inexact Rounded
xcom403 compare -187582786.E+369916952 957840435E+744365567 -> -1
xdiv403 divide -187582786.E+369916952 957840435E+744365567 -> -1.95839285E-374448616 Inexact Rounded
xdvi403 divideint -187582786.E+369916952 957840435E+744365567 -> -0
xmul403 multiply -187582786.E+369916952 957840435E+744365567 -> -Infinity Inexact Overflow Rounded
xpow403 power -187582786.E+369916952 10 -> Infinity Overflow Inexact Rounded
xrem403 remainder -187582786.E+369916952 957840435E+744365567 -> -1.87582786E+369916960
xsub403 subtract -187582786.E+369916952 957840435E+744365567 -> -9.57840435E+744365575 Inexact Rounded
xadd404 add -328026144. -125499735. -> -453525879
xcom404 compare -328026144. -125499735. -> -1
xdiv404 divide -328026144. -125499735. -> 2.61375965 Inexact Rounded
xdvi404 divideint -328026144. -125499735. -> 2
xmul404 multiply -328026144. -125499735. -> 4.11671941E+16 Inexact Rounded
xpow404 power -328026144. -125499735 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem404 remainder -328026144. -125499735. -> -77026674
xsub404 subtract -328026144. -125499735. -> -202526409
xadd405 add -99424150.2E-523662102 3712.35030 -> 3712.35030 Inexact Rounded
xcom405 compare -99424150.2E-523662102 3712.35030 -> -1
xdiv405 divide -99424150.2E-523662102 3712.35030 -> -2.67819958E-523662098 Inexact Rounded
xdvi405 divideint -99424150.2E-523662102 3712.35030 -> -0
xmul405 multiply -99424150.2E-523662102 3712.35030 -> -3.69097274E-523662091 Inexact Rounded
xpow405 power -99424150.2E-523662102 3712 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem405 remainder -99424150.2E-523662102 3712.35030 -> -9.94241502E-523662095
xsub405 subtract -99424150.2E-523662102 3712.35030 -> -3712.35030 Inexact Rounded
xadd406 add 14838.0718 9489893.28E+830631266 -> 9.48989328E+830631272 Inexact Rounded
xcom406 compare 14838.0718 9489893.28E+830631266 -> -1
xdiv406 divide 14838.0718 9489893.28E+830631266 -> 1.56356572E-830631269 Inexact Rounded
xdvi406 divideint 14838.0718 9489893.28E+830631266 -> 0
xmul406 multiply 14838.0718 9489893.28E+830631266 -> 1.40811718E+830631277 Inexact Rounded
xpow406 power 14838.0718 9 -> 3.48656057E+37 Inexact Rounded
xrem406 remainder 14838.0718 9489893.28E+830631266 -> 14838.0718
xsub406 subtract 14838.0718 9489893.28E+830631266 -> -9.48989328E+830631272 Inexact Rounded
xadd407 add 71207472.8 -31835.0809 -> 71175637.7 Inexact Rounded
xcom407 compare 71207472.8 -31835.0809 -> 1
xdiv407 divide 71207472.8 -31835.0809 -> -2236.76117 Inexact Rounded
xdvi407 divideint 71207472.8 -31835.0809 -> -2236
xmul407 multiply 71207472.8 -31835.0809 -> -2.26689566E+12 Inexact Rounded
xpow407 power 71207472.8 -31835 -> 7.05333953E-249986 Inexact Rounded
xrem407 remainder 71207472.8 -31835.0809 -> 24231.9076
xsub407 subtract 71207472.8 -31835.0809 -> 71239307.9 Inexact Rounded
xadd408 add -20440.4394 -44.4064328E+511085806 -> -4.44064328E+511085807 Inexact Rounded
xcom408 compare -20440.4394 -44.4064328E+511085806 -> 1
xdiv408 divide -20440.4394 -44.4064328E+511085806 -> 4.60303567E-511085804 Inexact Rounded
xdvi408 divideint -20440.4394 -44.4064328E+511085806 -> 0
xmul408 multiply -20440.4394 -44.4064328E+511085806 -> 9.07686999E+511085811 Inexact Rounded
xpow408 power -20440.4394 -4 -> 5.72847590E-18 Inexact Rounded
xrem408 remainder -20440.4394 -44.4064328E+511085806 -> -20440.4394
xsub408 subtract -20440.4394 -44.4064328E+511085806 -> 4.44064328E+511085807 Inexact Rounded
xadd409 add -54.3684171E-807210192 1.04592973E-984041807 -> -5.43684171E-807210191 Inexact Rounded
xcom409 compare -54.3684171E-807210192 1.04592973E-984041807 -> -1
xdiv409 divide -54.3684171E-807210192 1.04592973E-984041807 -> -5.19809463E+176831616 Inexact Rounded
xdvi409 divideint -54.3684171E-807210192 1.04592973E-984041807 -> NaN Division_impossible
xmul409 multiply -54.3684171E-807210192 1.04592973E-984041807 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xpow409 power -54.3684171E-807210192 1 -> -5.43684171E-807210191
xrem409 remainder -54.3684171E-807210192 1.04592973E-984041807 -> NaN Division_impossible
xsub409 subtract -54.3684171E-807210192 1.04592973E-984041807 -> -5.43684171E-807210191 Inexact Rounded
xadd410 add 54310060.5E+948159739 274320701.E+205880484 -> 5.43100605E+948159746 Inexact Rounded
xcom410 compare 54310060.5E+948159739 274320701.E+205880484 -> 1
xdiv410 divide 54310060.5E+948159739 274320701.E+205880484 -> 1.97980175E+742279254 Inexact Rounded
xdvi410 divideint 54310060.5E+948159739 274320701.E+205880484 -> NaN Division_impossible
xmul410 multiply 54310060.5E+948159739 274320701.E+205880484 -> Infinity Inexact Overflow Rounded
xpow410 power 54310060.5E+948159739 3 -> Infinity Overflow Inexact Rounded
xrem410 remainder 54310060.5E+948159739 274320701.E+205880484 -> NaN Division_impossible
xsub410 subtract 54310060.5E+948159739 274320701.E+205880484 -> 5.43100605E+948159746 Inexact Rounded
xadd411 add -657.186702 426844.39 -> 426187.203 Inexact Rounded
xcom411 compare -657.186702 426844.39 -> -1
xdiv411 divide -657.186702 426844.39 -> -0.00153964001 Inexact Rounded
xdvi411 divideint -657.186702 426844.39 -> -0
xmul411 multiply -657.186702 426844.39 -> -280516457 Inexact Rounded
xpow411 power -657.186702 426844 -> 3.50000575E+1202713 Inexact Rounded
xrem411 remainder -657.186702 426844.39 -> -657.186702
xsub411 subtract -657.186702 426844.39 -> -427501.577 Inexact Rounded
xadd412 add -41593077.0 -688607.564 -> -42281684.6 Inexact Rounded
xcom412 compare -41593077.0 -688607.564 -> -1
xdiv412 divide -41593077.0 -688607.564 -> 60.4017138 Inexact Rounded
xdvi412 divideint -41593077.0 -688607.564 -> 60
xmul412 multiply -41593077.0 -688607.564 -> 2.86413074E+13 Inexact Rounded
xpow412 power -41593077.0 -688608 -> 1.42150750E-5246519 Inexact Rounded
xrem412 remainder -41593077.0 -688607.564 -> -276623.160
xsub412 subtract -41593077.0 -688607.564 -> -40904469.4 Inexact Rounded
xadd413 add -5786.38132 190556652.E+177045877 -> 1.90556652E+177045885 Inexact Rounded
xcom413 compare -5786.38132 190556652.E+177045877 -> -1
xdiv413 divide -5786.38132 190556652.E+177045877 -> -3.03656748E-177045882 Inexact Rounded
xdvi413 divideint -5786.38132 190556652.E+177045877 -> -0
xmul413 multiply -5786.38132 190556652.E+177045877 -> -1.10263345E+177045889 Inexact Rounded
xpow413 power -5786.38132 2 -> 33482208.8 Inexact Rounded
xrem413 remainder -5786.38132 190556652.E+177045877 -> -5786.38132
xsub413 subtract -5786.38132 190556652.E+177045877 -> -1.90556652E+177045885 Inexact Rounded
xadd414 add 737622.974 -241560693E+249506565 -> -2.41560693E+249506573 Inexact Rounded
xcom414 compare 737622.974 -241560693E+249506565 -> 1
xdiv414 divide 737622.974 -241560693E+249506565 -> -3.05357202E-249506568 Inexact Rounded
xdvi414 divideint 737622.974 -241560693E+249506565 -> -0
xmul414 multiply 737622.974 -241560693E+249506565 -> -1.78180717E+249506579 Inexact Rounded
xpow414 power 737622.974 -2 -> 1.83793916E-12 Inexact Rounded
xrem414 remainder 737622.974 -241560693E+249506565 -> 737622.974
xsub414 subtract 737622.974 -241560693E+249506565 -> 2.41560693E+249506573 Inexact Rounded
xadd415 add 5615373.52 -7.27583808E-949781048 -> 5615373.52 Inexact Rounded
xcom415 compare 5615373.52 -7.27583808E-949781048 -> 1
xdiv415 divide 5615373.52 -7.27583808E-949781048 -> -7.71783739E+949781053 Inexact Rounded
xdvi415 divideint 5615373.52 -7.27583808E-949781048 -> NaN Division_impossible
xmul415 multiply 5615373.52 -7.27583808E-949781048 -> -4.08565485E-949781041 Inexact Rounded
xpow415 power 5615373.52 -7 -> 5.68001460E-48 Inexact Rounded
xrem415 remainder 5615373.52 -7.27583808E-949781048 -> NaN Division_impossible
xsub415 subtract 5615373.52 -7.27583808E-949781048 -> 5615373.52 Inexact Rounded
xadd416 add 644136.179 -835708.103 -> -191571.924
xcom416 compare 644136.179 -835708.103 -> 1
xdiv416 divide 644136.179 -835708.103 -> -0.770766942 Inexact Rounded
xdvi416 divideint 644136.179 -835708.103 -> -0
xmul416 multiply 644136.179 -835708.103 -> -5.38309824E+11 Inexact Rounded
xpow416 power 644136.179 -835708 -> 7.41936858E-4854610 Inexact Rounded
xrem416 remainder 644136.179 -835708.103 -> 644136.179
xsub416 subtract 644136.179 -835708.103 -> 1479844.28 Inexact Rounded
xadd417 add -307.419521E+466861843 -738689976.E-199032711 -> -3.07419521E+466861845 Inexact Rounded
xcom417 compare -307.419521E+466861843 -738689976.E-199032711 -> -1
xdiv417 divide -307.419521E+466861843 -738689976.E-199032711 -> 4.16168529E+665894547 Inexact Rounded
xdvi417 divideint -307.419521E+466861843 -738689976.E-199032711 -> NaN Division_impossible
xmul417 multiply -307.419521E+466861843 -738689976.E-199032711 -> 2.27087719E+267829143 Inexact Rounded
xpow417 power -307.419521E+466861843 -7 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem417 remainder -307.419521E+466861843 -738689976.E-199032711 -> NaN Division_impossible
xsub417 subtract -307.419521E+466861843 -738689976.E-199032711 -> -3.07419521E+466861845 Inexact Rounded
xadd418 add -619642.130 -226740537.E-902590153 -> -619642.130 Inexact Rounded
xcom418 compare -619642.130 -226740537.E-902590153 -> -1
xdiv418 divide -619642.130 -226740537.E-902590153 -> 2.73282466E+902590150 Inexact Rounded
xdvi418 divideint -619642.130 -226740537.E-902590153 -> NaN Division_impossible
xmul418 multiply -619642.130 -226740537.E-902590153 -> 1.40497989E-902590139 Inexact Rounded
xpow418 power -619642.130 -2 -> 2.60446259E-12 Inexact Rounded
xrem418 remainder -619642.130 -226740537.E-902590153 -> NaN Division_impossible
xsub418 subtract -619642.130 -226740537.E-902590153 -> -619642.130 Inexact Rounded
xadd419 add -31068.7549 -3.41495042E+86001379 -> -3.41495042E+86001379 Inexact Rounded
xcom419 compare -31068.7549 -3.41495042E+86001379 -> 1
xdiv419 divide -31068.7549 -3.41495042E+86001379 -> 9.09786412E-86001376 Inexact Rounded
xdvi419 divideint -31068.7549 -3.41495042E+86001379 -> 0
xmul419 multiply -31068.7549 -3.41495042E+86001379 -> 1.06098258E+86001384 Inexact Rounded
xpow419 power -31068.7549 -3 -> -3.33448258E-14 Inexact Rounded
xrem419 remainder -31068.7549 -3.41495042E+86001379 -> -31068.7549
xsub419 subtract -31068.7549 -3.41495042E+86001379 -> 3.41495042E+86001379 Inexact Rounded
xadd420 add -68951173. -211804977.E-97318126 -> -68951173.0 Inexact Rounded
xcom420 compare -68951173. -211804977.E-97318126 -> -1
xdiv420 divide -68951173. -211804977.E-97318126 -> 3.25540854E+97318125 Inexact Rounded
xdvi420 divideint -68951173. -211804977.E-97318126 -> NaN Division_impossible
xmul420 multiply -68951173. -211804977.E-97318126 -> 1.46042016E-97318110 Inexact Rounded
xpow420 power -68951173. -2 -> 2.10337488E-16 Inexact Rounded
xrem420 remainder -68951173. -211804977.E-97318126 -> NaN Division_impossible
xsub420 subtract -68951173. -211804977.E-97318126 -> -68951173.0 Inexact Rounded
xadd421 add -4.09492571E-301749490 434.20199E-749390952 -> -4.09492571E-301749490 Inexact Rounded
xcom421 compare -4.09492571E-301749490 434.20199E-749390952 -> -1
xdiv421 divide -4.09492571E-301749490 434.20199E-749390952 -> -9.43092341E+447641459 Inexact Rounded
xdvi421 divideint -4.09492571E-301749490 434.20199E-749390952 -> NaN Division_impossible
xmul421 multiply -4.09492571E-301749490 434.20199E-749390952 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xpow421 power -4.09492571E-301749490 4 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem421 remainder -4.09492571E-301749490 434.20199E-749390952 -> NaN Division_impossible
xsub421 subtract -4.09492571E-301749490 434.20199E-749390952 -> -4.09492571E-301749490 Inexact Rounded
xadd422 add 3898.03188 -82572.615 -> -78674.5831 Inexact Rounded
xcom422 compare 3898.03188 -82572.615 -> 1
xdiv422 divide 3898.03188 -82572.615 -> -0.0472073202 Inexact Rounded
xdvi422 divideint 3898.03188 -82572.615 -> -0
xmul422 multiply 3898.03188 -82572.615 -> -321870686 Inexact Rounded
xpow422 power 3898.03188 -82573 -> 1.33010737E-296507 Inexact Rounded
xrem422 remainder 3898.03188 -82572.615 -> 3898.03188
xsub422 subtract 3898.03188 -82572.615 -> 86470.6469 Inexact Rounded
xadd423 add -1.7619356 -2546.64043 -> -2548.40237 Inexact Rounded
xcom423 compare -1.7619356 -2546.64043 -> 1
xdiv423 divide -1.7619356 -2546.64043 -> 0.000691866657 Inexact Rounded
xdvi423 divideint -1.7619356 -2546.64043 -> 0
xmul423 multiply -1.7619356 -2546.64043 -> 4487.01643 Inexact Rounded
xpow423 power -1.7619356 -2547 -> -2.90664557E-627 Inexact Rounded
xrem423 remainder -1.7619356 -2546.64043 -> -1.7619356
xsub423 subtract -1.7619356 -2546.64043 -> 2544.87849 Inexact Rounded
xadd424 add 59714.1968 29734388.6E-564525525 -> 59714.1968 Inexact Rounded
xcom424 compare 59714.1968 29734388.6E-564525525 -> 1
xdiv424 divide 59714.1968 29734388.6E-564525525 -> 2.00825373E+564525522 Inexact Rounded
xdvi424 divideint 59714.1968 29734388.6E-564525525 -> NaN Division_impossible
xmul424 multiply 59714.1968 29734388.6E-564525525 -> 1.77556513E-564525513 Inexact Rounded
xpow424 power 59714.1968 3 -> 2.12928005E+14 Inexact Rounded
xrem424 remainder 59714.1968 29734388.6E-564525525 -> NaN Division_impossible
xsub424 subtract 59714.1968 29734388.6E-564525525 -> 59714.1968 Inexact Rounded
xadd425 add 6.88891136E-935467395 -785049.562E-741671442 -> -7.85049562E-741671437 Inexact Rounded
xcom425 compare 6.88891136E-935467395 -785049.562E-741671442 -> 1
xdiv425 divide 6.88891136E-935467395 -785049.562E-741671442 -> -8.77512923E-193795959 Inexact Rounded
xdvi425 divideint 6.88891136E-935467395 -785049.562E-741671442 -> -0
xmul425 multiply 6.88891136E-935467395 -785049.562E-741671442 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xpow425 power 6.88891136E-935467395 -8 -> Infinity Overflow Inexact Rounded
xrem425 remainder 6.88891136E-935467395 -785049.562E-741671442 -> 6.88891136E-935467395
xsub425 subtract 6.88891136E-935467395 -785049.562E-741671442 -> 7.85049562E-741671437 Inexact Rounded
xadd426 add 975566251 -519.858530 -> 975565731 Inexact Rounded
xcom426 compare 975566251 -519.858530 -> 1
xdiv426 divide 975566251 -519.858530 -> -1876599.49 Inexact Rounded
xdvi426 divideint 975566251 -519.858530 -> -1876599
xmul426 multiply 975566251 -519.858530 -> -5.07156437E+11 Inexact Rounded
xpow426 power 975566251 -520 -> 3.85905300E-4675 Inexact Rounded
xrem426 remainder 975566251 -519.858530 -> 253.460530
xsub426 subtract 975566251 -519.858530 -> 975566771 Inexact Rounded
xadd427 add 307401954 -231481582. -> 75920372
xcom427 compare 307401954 -231481582. -> 1
xdiv427 divide 307401954 -231481582. -> -1.32797586 Inexact Rounded
xdvi427 divideint 307401954 -231481582. -> -1
xmul427 multiply 307401954 -231481582. -> -7.11578906E+16 Inexact Rounded
xpow427 power 307401954 -231481582 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem427 remainder 307401954 -231481582. -> 75920372
xsub427 subtract 307401954 -231481582. -> 538883536
xadd428 add 2237645.48E+992947388 -60618055.3E-857316706 -> 2.23764548E+992947394 Inexact Rounded
xcom428 compare 2237645.48E+992947388 -60618055.3E-857316706 -> 1
xdiv428 divide 2237645.48E+992947388 -60618055.3E-857316706 -> -Infinity Inexact Overflow Rounded
xdvi428 divideint 2237645.48E+992947388 -60618055.3E-857316706 -> NaN Division_impossible
xmul428 multiply 2237645.48E+992947388 -60618055.3E-857316706 -> -1.35641717E+135630696 Inexact Rounded
xpow428 power 2237645.48E+992947388 -6 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem428 remainder 2237645.48E+992947388 -60618055.3E-857316706 -> NaN Division_impossible
xsub428 subtract 2237645.48E+992947388 -60618055.3E-857316706 -> 2.23764548E+992947394 Inexact Rounded
xadd429 add -403903.851 35.5049687E-72095155 -> -403903.851 Inexact Rounded
xcom429 compare -403903.851 35.5049687E-72095155 -> -1
xdiv429 divide -403903.851 35.5049687E-72095155 -> -1.13759810E+72095159 Inexact Rounded
xdvi429 divideint -403903.851 35.5049687E-72095155 -> NaN Division_impossible
xmul429 multiply -403903.851 35.5049687E-72095155 -> -1.43405936E-72095148 Inexact Rounded
xpow429 power -403903.851 4 -> 2.66141117E+22 Inexact Rounded
xrem429 remainder -403903.851 35.5049687E-72095155 -> NaN Division_impossible
xsub429 subtract -403903.851 35.5049687E-72095155 -> -403903.851 Inexact Rounded
xadd430 add 6.48674979 -621732.532E+422575800 -> -6.21732532E+422575805 Inexact Rounded
xcom430 compare 6.48674979 -621732.532E+422575800 -> 1
xdiv430 divide 6.48674979 -621732.532E+422575800 -> -1.04333447E-422575805 Inexact Rounded
xdvi430 divideint 6.48674979 -621732.532E+422575800 -> -0
xmul430 multiply 6.48674979 -621732.532E+422575800 -> -4.03302337E+422575806 Inexact Rounded
xpow430 power 6.48674979 -6 -> 0.0000134226146 Inexact Rounded
xrem430 remainder 6.48674979 -621732.532E+422575800 -> 6.48674979
xsub430 subtract 6.48674979 -621732.532E+422575800 -> 6.21732532E+422575805 Inexact Rounded
xadd431 add -31401.9418 36.3960679 -> -31365.5457 Inexact Rounded
xcom431 compare -31401.9418 36.3960679 -> -1
xdiv431 divide -31401.9418 36.3960679 -> -862.783911 Inexact Rounded
xdvi431 divideint -31401.9418 36.3960679 -> -862
xmul431 multiply -31401.9418 36.3960679 -> -1142907.21 Inexact Rounded
xpow431 power -31401.9418 36 -> 7.77023505E+161 Inexact Rounded
xrem431 remainder -31401.9418 36.3960679 -> -28.5312702
xsub431 subtract -31401.9418 36.3960679 -> -31438.3379 Inexact Rounded
xadd432 add 31345321.1 51.5482191 -> 31345372.6 Inexact Rounded
xcom432 compare 31345321.1 51.5482191 -> 1
xdiv432 divide 31345321.1 51.5482191 -> 608077.673 Inexact Rounded
xdvi432 divideint 31345321.1 51.5482191 -> 608077
xmul432 multiply 31345321.1 51.5482191 -> 1.61579548E+9 Inexact Rounded
xpow432 power 31345321.1 52 -> 6.32385059E+389 Inexact Rounded
xrem432 remainder 31345321.1 51.5482191 -> 34.6743293
xsub432 subtract 31345321.1 51.5482191 -> 31345269.6 Inexact Rounded
xadd433 add -64.172844 -28506227.2E-767965800 -> -64.1728440 Inexact Rounded
xcom433 compare -64.172844 -28506227.2E-767965800 -> -1
xdiv433 divide -64.172844 -28506227.2E-767965800 -> 2.25118686E+767965794 Inexact Rounded
xdvi433 divideint -64.172844 -28506227.2E-767965800 -> NaN Division_impossible
xmul433 multiply -64.172844 -28506227.2E-767965800 -> 1.82932567E-767965791 Inexact Rounded
xpow433 power -64.172844 -3 -> -0.00000378395654 Inexact Rounded
xrem433 remainder -64.172844 -28506227.2E-767965800 -> NaN Division_impossible
xsub433 subtract -64.172844 -28506227.2E-767965800 -> -64.1728440 Inexact Rounded
xadd434 add 70437.1551 -62916.1233 -> 7521.0318
xcom434 compare 70437.1551 -62916.1233 -> 1
xdiv434 divide 70437.1551 -62916.1233 -> -1.11954061 Inexact Rounded
xdvi434 divideint 70437.1551 -62916.1233 -> -1
xmul434 multiply 70437.1551 -62916.1233 -> -4.43163274E+9 Inexact Rounded
xpow434 power 70437.1551 -62916 -> 5.02945060E-305005 Inexact Rounded
xrem434 remainder 70437.1551 -62916.1233 -> 7521.0318
xsub434 subtract 70437.1551 -62916.1233 -> 133353.278 Inexact Rounded
xadd435 add 916260164 -58.4017325 -> 916260106 Inexact Rounded
xcom435 compare 916260164 -58.4017325 -> 1
xdiv435 divide 916260164 -58.4017325 -> -15688920.9 Inexact Rounded
xdvi435 divideint 916260164 -58.4017325 -> -15688920
xmul435 multiply 916260164 -58.4017325 -> -5.35111810E+10 Inexact Rounded
xpow435 power 916260164 -58 -> 1.59554587E-520 Inexact Rounded
xrem435 remainder 916260164 -58.4017325 -> 54.9461000
xsub435 subtract 916260164 -58.4017325 -> 916260222 Inexact Rounded
xadd436 add 19889085.3E-46816480 1581683.94 -> 1581683.94 Inexact Rounded
xcom436 compare 19889085.3E-46816480 1581683.94 -> -1
xdiv436 divide 19889085.3E-46816480 1581683.94 -> 1.25746268E-46816479 Inexact Rounded
xdvi436 divideint 19889085.3E-46816480 1581683.94 -> 0
xmul436 multiply 19889085.3E-46816480 1581683.94 -> 3.14582468E-46816467 Inexact Rounded
xpow436 power 19889085.3E-46816480 1581684 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem436 remainder 19889085.3E-46816480 1581683.94 -> 1.98890853E-46816473
xsub436 subtract 19889085.3E-46816480 1581683.94 -> -1581683.94 Inexact Rounded
xadd437 add -56312.3383 789.466064 -> -55522.8722 Inexact Rounded
xcom437 compare -56312.3383 789.466064 -> -1
xdiv437 divide -56312.3383 789.466064 -> -71.3296503 Inexact Rounded
xdvi437 divideint -56312.3383 789.466064 -> -71
xmul437 multiply -56312.3383 789.466064 -> -44456680.1 Inexact Rounded
xpow437 power -56312.3383 789 -> -1.68348724E+3748 Inexact Rounded
xrem437 remainder -56312.3383 789.466064 -> -260.247756
xsub437 subtract -56312.3383 789.466064 -> -57101.8044 Inexact Rounded
xadd438 add 183442.849 -925876106 -> -925692663 Inexact Rounded
xcom438 compare 183442.849 -925876106 -> 1
xdiv438 divide 183442.849 -925876106 -> -0.000198128937 Inexact Rounded
xdvi438 divideint 183442.849 -925876106 -> -0
xmul438 multiply 183442.849 -925876106 -> -1.69845351E+14 Inexact Rounded
xpow438 power 183442.849 -925876106 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem438 remainder 183442.849 -925876106 -> 183442.849
xsub438 subtract 183442.849 -925876106 -> 926059549 Inexact Rounded
xadd439 add 971113.655E-695540249 -419351120E-977743823 -> 9.71113655E-695540244 Inexact Rounded
xcom439 compare 971113.655E-695540249 -419351120E-977743823 -> 1
xdiv439 divide 971113.655E-695540249 -419351120E-977743823 -> -2.31575310E+282203571 Inexact Rounded
xdvi439 divideint 971113.655E-695540249 -419351120E-977743823 -> NaN Division_impossible
xmul439 multiply 971113.655E-695540249 -419351120E-977743823 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xpow439 power 971113.655E-695540249 -4 -> Infinity Overflow Inexact Rounded
xrem439 remainder 971113.655E-695540249 -419351120E-977743823 -> NaN Division_impossible
xsub439 subtract 971113.655E-695540249 -419351120E-977743823 -> 9.71113655E-695540244 Inexact Rounded
xadd440 add 859658551. 72338.2054 -> 859730889 Inexact Rounded
xcom440 compare 859658551. 72338.2054 -> 1
xdiv440 divide 859658551. 72338.2054 -> 11883.8800 Inexact Rounded
xdvi440 divideint 859658551. 72338.2054 -> 11883
xmul440 multiply 859658551. 72338.2054 -> 6.21861568E+13 Inexact Rounded
xpow440 power 859658551. 72338 -> 1.87620450E+646291 Inexact Rounded
xrem440 remainder 859658551. 72338.2054 -> 63656.2318
xsub440 subtract 859658551. 72338.2054 -> 859586213 Inexact Rounded
xadd441 add -3.86446630E+426816068 -664.534737 -> -3.86446630E+426816068 Inexact Rounded
xcom441 compare -3.86446630E+426816068 -664.534737 -> -1
xdiv441 divide -3.86446630E+426816068 -664.534737 -> 5.81529615E+426816065 Inexact Rounded
xdvi441 divideint -3.86446630E+426816068 -664.534737 -> NaN Division_impossible
xmul441 multiply -3.86446630E+426816068 -664.534737 -> 2.56807210E+426816071 Inexact Rounded
xpow441 power -3.86446630E+426816068 -665 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem441 remainder -3.86446630E+426816068 -664.534737 -> NaN Division_impossible
xsub441 subtract -3.86446630E+426816068 -664.534737 -> -3.86446630E+426816068 Inexact Rounded
xadd442 add -969.881818 31170.8555 -> 30200.9737 Inexact Rounded
xcom442 compare -969.881818 31170.8555 -> -1
xdiv442 divide -969.881818 31170.8555 -> -0.0311150208 Inexact Rounded
xdvi442 divideint -969.881818 31170.8555 -> -0
xmul442 multiply -969.881818 31170.8555 -> -30232046.0 Inexact Rounded
xpow442 power -969.881818 31171 -> -1.02865894E+93099 Inexact Rounded
xrem442 remainder -969.881818 31170.8555 -> -969.881818
xsub442 subtract -969.881818 31170.8555 -> -32140.7373 Inexact Rounded
xadd443 add 7980537.27 85.4040512 -> 7980622.67 Inexact Rounded
xcom443 compare 7980537.27 85.4040512 -> 1
xdiv443 divide 7980537.27 85.4040512 -> 93444.4814 Inexact Rounded
xdvi443 divideint 7980537.27 85.4040512 -> 93444
xmul443 multiply 7980537.27 85.4040512 -> 681570214 Inexact Rounded
xpow443 power 7980537.27 85 -> 4.70685763E+586 Inexact Rounded
xrem443 remainder 7980537.27 85.4040512 -> 41.1096672
xsub443 subtract 7980537.27 85.4040512 -> 7980451.87 Inexact Rounded
xadd444 add -114609916. 7525.14981 -> -114602391 Inexact Rounded
xcom444 compare -114609916. 7525.14981 -> -1
xdiv444 divide -114609916. 7525.14981 -> -15230.2504 Inexact Rounded
xdvi444 divideint -114609916. 7525.14981 -> -15230
xmul444 multiply -114609916. 7525.14981 -> -8.62456788E+11 Inexact Rounded
xpow444 power -114609916. 7525 -> -4.43620445E+60645 Inexact Rounded
xrem444 remainder -114609916. 7525.14981 -> -1884.39370
xsub444 subtract -114609916. 7525.14981 -> -114617441 Inexact Rounded
xadd445 add 8.43404682E-500572568 474526719 -> 474526719 Inexact Rounded
xcom445 compare 8.43404682E-500572568 474526719 -> -1
xdiv445 divide 8.43404682E-500572568 474526719 -> 1.77735973E-500572576 Inexact Rounded
xdvi445 divideint 8.43404682E-500572568 474526719 -> 0
xmul445 multiply 8.43404682E-500572568 474526719 -> 4.00218057E-500572559 Inexact Rounded
xpow445 power 8.43404682E-500572568 474526719 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem445 remainder 8.43404682E-500572568 474526719 -> 8.43404682E-500572568
xsub445 subtract 8.43404682E-500572568 474526719 -> -474526719 Inexact Rounded
xadd446 add 188006433 2260.17037E-978192525 -> 188006433 Inexact Rounded
xcom446 compare 188006433 2260.17037E-978192525 -> 1
xdiv446 divide 188006433 2260.17037E-978192525 -> 8.31824165E+978192529 Inexact Rounded
xdvi446 divideint 188006433 2260.17037E-978192525 -> NaN Division_impossible
xmul446 multiply 188006433 2260.17037E-978192525 -> 4.24926569E-978192514 Inexact Rounded
xpow446 power 188006433 2 -> 3.53464188E+16 Inexact Rounded
xrem446 remainder 188006433 2260.17037E-978192525 -> NaN Division_impossible
xsub446 subtract 188006433 2260.17037E-978192525 -> 188006433 Inexact Rounded
xadd447 add -9.95836312 -866466703 -> -866466713 Inexact Rounded
xcom447 compare -9.95836312 -866466703 -> 1
xdiv447 divide -9.95836312 -866466703 -> 1.14930707E-8 Inexact Rounded
xdvi447 divideint -9.95836312 -866466703 -> 0
xmul447 multiply -9.95836312 -866466703 -> 8.62859006E+9 Inexact Rounded
xpow447 power -9.95836312 -866466703 -> -6.71744369E-864896630 Inexact Rounded
xrem447 remainder -9.95836312 -866466703 -> -9.95836312
xsub447 subtract -9.95836312 -866466703 -> 866466693 Inexact Rounded
xadd448 add 80919339.2E-967231586 219.824266 -> 219.824266 Inexact Rounded
xcom448 compare 80919339.2E-967231586 219.824266 -> -1
xdiv448 divide 80919339.2E-967231586 219.824266 -> 3.68109220E-967231581 Inexact Rounded
xdvi448 divideint 80919339.2E-967231586 219.824266 -> 0
xmul448 multiply 80919339.2E-967231586 219.824266 -> 1.77880343E-967231576 Inexact Rounded
xpow448 power 80919339.2E-967231586 220 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem448 remainder 80919339.2E-967231586 219.824266 -> 8.09193392E-967231579
xsub448 subtract 80919339.2E-967231586 219.824266 -> -219.824266 Inexact Rounded
xadd449 add 159579.444 -89827.5229 -> 69751.9211
xcom449 compare 159579.444 -89827.5229 -> 1
xdiv449 divide 159579.444 -89827.5229 -> -1.77650946 Inexact Rounded
xdvi449 divideint 159579.444 -89827.5229 -> -1
xmul449 multiply 159579.444 -89827.5229 -> -1.43346262E+10 Inexact Rounded
xpow449 power 159579.444 -89828 -> 9.69955850E-467374 Inexact Rounded
xrem449 remainder 159579.444 -89827.5229 -> 69751.9211
xsub449 subtract 159579.444 -89827.5229 -> 249406.967 Inexact Rounded
xadd450 add -4.54000153 6966333.74 -> 6966329.20 Inexact Rounded
xcom450 compare -4.54000153 6966333.74 -> -1
xdiv450 divide -4.54000153 6966333.74 -> -6.51706005E-7 Inexact Rounded
xdvi450 divideint -4.54000153 6966333.74 -> -0
xmul450 multiply -4.54000153 6966333.74 -> -31627165.8 Inexact Rounded
xpow450 power -4.54000153 6966334 -> 3.52568913E+4577271 Inexact Rounded
xrem450 remainder -4.54000153 6966333.74 -> -4.54000153
xsub450 subtract -4.54000153 6966333.74 -> -6966338.28 Inexact Rounded
xadd451 add 28701538.7E-391015649 -920999192. -> -920999192 Inexact Rounded
xcom451 compare 28701538.7E-391015649 -920999192. -> 1
xdiv451 divide 28701538.7E-391015649 -920999192. -> -3.11634787E-391015651 Inexact Rounded
xdvi451 divideint 28701538.7E-391015649 -920999192. -> -0
xmul451 multiply 28701538.7E-391015649 -920999192. -> -2.64340940E-391015633 Inexact Rounded
xpow451 power 28701538.7E-391015649 -920999192 -> Infinity Overflow Inexact Rounded
xrem451 remainder 28701538.7E-391015649 -920999192. -> 2.87015387E-391015642
xsub451 subtract 28701538.7E-391015649 -920999192. -> 920999192 Inexact Rounded
xadd452 add -361382575. -7976.15286E+898491169 -> -7.97615286E+898491172 Inexact Rounded
xcom452 compare -361382575. -7976.15286E+898491169 -> 1
xdiv452 divide -361382575. -7976.15286E+898491169 -> 4.53078798E-898491165 Inexact Rounded
xdvi452 divideint -361382575. -7976.15286E+898491169 -> 0
xmul452 multiply -361382575. -7976.15286E+898491169 -> 2.88244266E+898491181 Inexact Rounded
xpow452 power -361382575. -8 -> 3.43765537E-69 Inexact Rounded
xrem452 remainder -361382575. -7976.15286E+898491169 -> -361382575
xsub452 subtract -361382575. -7976.15286E+898491169 -> 7.97615286E+898491172 Inexact Rounded
xadd453 add 7021805.61 1222952.83 -> 8244758.44
xcom453 compare 7021805.61 1222952.83 -> 1
xdiv453 divide 7021805.61 1222952.83 -> 5.74168148 Inexact Rounded
xdvi453 divideint 7021805.61 1222952.83 -> 5
xmul453 multiply 7021805.61 1222952.83 -> 8.58733704E+12 Inexact Rounded
xpow453 power 7021805.61 1222953 -> 1.26540553E+8372885 Inexact Rounded
xrem453 remainder 7021805.61 1222952.83 -> 907041.46
xsub453 subtract 7021805.61 1222952.83 -> 5798852.78
xadd454 add -40.4811667 -79655.5635 -> -79696.0447 Inexact Rounded
xcom454 compare -40.4811667 -79655.5635 -> 1
xdiv454 divide -40.4811667 -79655.5635 -> 0.000508202628 Inexact Rounded
xdvi454 divideint -40.4811667 -79655.5635 -> 0
xmul454 multiply -40.4811667 -79655.5635 -> 3224550.14 Inexact Rounded
xpow454 power -40.4811667 -79656 -> 4.50174275E-128028 Inexact Rounded
xrem454 remainder -40.4811667 -79655.5635 -> -40.4811667
xsub454 subtract -40.4811667 -79655.5635 -> 79615.0823 Inexact Rounded
xadd455 add -8755674.38E+117168177 148.903404 -> -8.75567438E+117168183 Inexact Rounded
xcom455 compare -8755674.38E+117168177 148.903404 -> -1
xdiv455 divide -8755674.38E+117168177 148.903404 -> -5.88010357E+117168181 Inexact Rounded
xdvi455 divideint -8755674.38E+117168177 148.903404 -> NaN Division_impossible
xmul455 multiply -8755674.38E+117168177 148.903404 -> -1.30374972E+117168186 Inexact Rounded
xpow455 power -8755674.38E+117168177 149 -> -Infinity Overflow Inexact Rounded
xrem455 remainder -8755674.38E+117168177 148.903404 -> NaN Division_impossible
xsub455 subtract -8755674.38E+117168177 148.903404 -> -8.75567438E+117168183 Inexact Rounded
xadd456 add 34.5329781E+382829392 -45.2177309 -> 3.45329781E+382829393 Inexact Rounded
xcom456 compare 34.5329781E+382829392 -45.2177309 -> 1
xdiv456 divide 34.5329781E+382829392 -45.2177309 -> -7.63704357E+382829391 Inexact Rounded
xdvi456 divideint 34.5329781E+382829392 -45.2177309 -> NaN Division_impossible
xmul456 multiply 34.5329781E+382829392 -45.2177309 -> -1.56150291E+382829395 Inexact Rounded
xpow456 power 34.5329781E+382829392 -45 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem456 remainder 34.5329781E+382829392 -45.2177309 -> NaN Division_impossible
xsub456 subtract 34.5329781E+382829392 -45.2177309 -> 3.45329781E+382829393 Inexact Rounded
xadd457 add -37958476.0 584367.935 -> -37374108.1 Inexact Rounded
xcom457 compare -37958476.0 584367.935 -> -1
xdiv457 divide -37958476.0 584367.935 -> -64.9564662 Inexact Rounded
xdvi457 divideint -37958476.0 584367.935 -> -64
xmul457 multiply -37958476.0 584367.935 -> -2.21817162E+13 Inexact Rounded
xpow457 power -37958476.0 584368 -> 3.20538268E+4429105 Inexact Rounded
xrem457 remainder -37958476.0 584367.935 -> -558928.160
xsub457 subtract -37958476.0 584367.935 -> -38542843.9 Inexact Rounded
xadd458 add 495233.553E-414152215 62352759.2 -> 62352759.2 Inexact Rounded
xcom458 compare 495233.553E-414152215 62352759.2 -> -1
xdiv458 divide 495233.553E-414152215 62352759.2 -> 7.94244809E-414152218 Inexact Rounded
xdvi458 divideint 495233.553E-414152215 62352759.2 -> 0
xmul458 multiply 495233.553E-414152215 62352759.2 -> 3.08791785E-414152202 Inexact Rounded
xpow458 power 495233.553E-414152215 62352759 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem458 remainder 495233.553E-414152215 62352759.2 -> 4.95233553E-414152210
xsub458 subtract 495233.553E-414152215 62352759.2 -> -62352759.2 Inexact Rounded
xadd459 add -502343060 -96828.994 -> -502439889 Inexact Rounded
xcom459 compare -502343060 -96828.994 -> -1
xdiv459 divide -502343060 -96828.994 -> 5187.94050 Inexact Rounded
xdvi459 divideint -502343060 -96828.994 -> 5187
xmul459 multiply -502343060 -96828.994 -> 4.86413731E+13 Inexact Rounded
xpow459 power -502343060 -96829 -> -6.78602119E-842510 Inexact Rounded
xrem459 remainder -502343060 -96828.994 -> -91068.122
xsub459 subtract -502343060 -96828.994 -> -502246231 Inexact Rounded
xadd460 add -22.439639E+916362878 -39.4037681 -> -2.24396390E+916362879 Inexact Rounded
xcom460 compare -22.439639E+916362878 -39.4037681 -> -1
xdiv460 divide -22.439639E+916362878 -39.4037681 -> 5.69479521E+916362877 Inexact Rounded
xdvi460 divideint -22.439639E+916362878 -39.4037681 -> NaN Division_impossible
xmul460 multiply -22.439639E+916362878 -39.4037681 -> 8.84206331E+916362880 Inexact Rounded
xpow460 power -22.439639E+916362878 -39 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem460 remainder -22.439639E+916362878 -39.4037681 -> NaN Division_impossible
xsub460 subtract -22.439639E+916362878 -39.4037681 -> -2.24396390E+916362879 Inexact Rounded
xadd461 add 718180.587E-957473722 1.66223443 -> 1.66223443 Inexact Rounded
xcom461 compare 718180.587E-957473722 1.66223443 -> -1
xdiv461 divide 718180.587E-957473722 1.66223443 -> 4.32057340E-957473717 Inexact Rounded
xdvi461 divideint 718180.587E-957473722 1.66223443 -> 0
xmul461 multiply 718180.587E-957473722 1.66223443 -> 1.19378450E-957473716 Inexact Rounded
xpow461 power 718180.587E-957473722 2 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem461 remainder 718180.587E-957473722 1.66223443 -> 7.18180587E-957473717
xsub461 subtract 718180.587E-957473722 1.66223443 -> -1.66223443 Inexact Rounded
xadd462 add -51592.2698 -713885.741 -> -765478.011 Inexact Rounded
xcom462 compare -51592.2698 -713885.741 -> 1
xdiv462 divide -51592.2698 -713885.741 -> 0.0722696460 Inexact Rounded
xdvi462 divideint -51592.2698 -713885.741 -> 0
xmul462 multiply -51592.2698 -713885.741 -> 3.68309858E+10 Inexact Rounded
xpow462 power -51592.2698 -713886 -> 6.38576920E-3364249 Inexact Rounded
xrem462 remainder -51592.2698 -713885.741 -> -51592.2698
xsub462 subtract -51592.2698 -713885.741 -> 662293.471 Inexact Rounded
xadd463 add 51.2279848E+80439745 207.55925E+865165070 -> 2.07559250E+865165072 Inexact Rounded
xcom463 compare 51.2279848E+80439745 207.55925E+865165070 -> -1
xdiv463 divide 51.2279848E+80439745 207.55925E+865165070 -> 2.46811379E-784725326 Inexact Rounded
xdvi463 divideint 51.2279848E+80439745 207.55925E+865165070 -> 0
xmul463 multiply 51.2279848E+80439745 207.55925E+865165070 -> 1.06328421E+945604819 Inexact Rounded
xpow463 power 51.2279848E+80439745 2 -> 2.62430643E+160879493 Inexact Rounded
xrem463 remainder 51.2279848E+80439745 207.55925E+865165070 -> 5.12279848E+80439746
xsub463 subtract 51.2279848E+80439745 207.55925E+865165070 -> -2.07559250E+865165072 Inexact Rounded
xadd464 add -5983.23468 -39.9544513 -> -6023.18913 Inexact Rounded
xcom464 compare -5983.23468 -39.9544513 -> -1
xdiv464 divide -5983.23468 -39.9544513 -> 149.751392 Inexact Rounded
xdvi464 divideint -5983.23468 -39.9544513 -> 149
xmul464 multiply -5983.23468 -39.9544513 -> 239056.859 Inexact Rounded
xpow464 power -5983.23468 -40 -> 8.36678291E-152 Inexact Rounded
xrem464 remainder -5983.23468 -39.9544513 -> -30.0214363
xsub464 subtract -5983.23468 -39.9544513 -> -5943.28023 Inexact Rounded
xadd465 add 921639332.E-917542963 287325.891 -> 287325.891 Inexact Rounded
xcom465 compare 921639332.E-917542963 287325.891 -> -1
xdiv465 divide 921639332.E-917542963 287325.891 -> 3.20764456E-917542960 Inexact Rounded
xdvi465 divideint 921639332.E-917542963 287325.891 -> 0
xmul465 multiply 921639332.E-917542963 287325.891 -> 2.64810842E-917542949 Inexact Rounded
xpow465 power 921639332.E-917542963 287326 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem465 remainder 921639332.E-917542963 287325.891 -> 9.21639332E-917542955
xsub465 subtract 921639332.E-917542963 287325.891 -> -287325.891 Inexact Rounded
xadd466 add 91095916.8E-787312969 -58643.418E+58189880 -> -5.86434180E+58189884 Inexact Rounded
xcom466 compare 91095916.8E-787312969 -58643.418E+58189880 -> 1
xdiv466 divide 91095916.8E-787312969 -58643.418E+58189880 -> -1.55338689E-845502846 Inexact Rounded
xdvi466 divideint 91095916.8E-787312969 -58643.418E+58189880 -> -0
xmul466 multiply 91095916.8E-787312969 -58643.418E+58189880 -> -5.34217593E-729123077 Inexact Rounded
xpow466 power 91095916.8E-787312969 -6 -> Infinity Overflow Inexact Rounded
xrem466 remainder 91095916.8E-787312969 -58643.418E+58189880 -> 9.10959168E-787312962
xsub466 subtract 91095916.8E-787312969 -58643.418E+58189880 -> 5.86434180E+58189884 Inexact Rounded
xadd467 add -6410.5555 -234964259 -> -234970670 Inexact Rounded
xcom467 compare -6410.5555 -234964259 -> 1
xdiv467 divide -6410.5555 -234964259 -> 0.0000272831090 Inexact Rounded
xdvi467 divideint -6410.5555 -234964259 -> 0
xmul467 multiply -6410.5555 -234964259 -> 1.50625142E+12 Inexact Rounded
xpow467 power -6410.5555 -234964259 -> -1.27064467E-894484419 Inexact Rounded
xrem467 remainder -6410.5555 -234964259 -> -6410.5555
xsub467 subtract -6410.5555 -234964259 -> 234957848 Inexact Rounded
xadd468 add -5.32711606 -8447286.21 -> -8447291.54 Inexact Rounded
xcom468 compare -5.32711606 -8447286.21 -> 1
xdiv468 divide -5.32711606 -8447286.21 -> 6.30630468E-7 Inexact Rounded
xdvi468 divideint -5.32711606 -8447286.21 -> 0
xmul468 multiply -5.32711606 -8447286.21 -> 44999674.0 Inexact Rounded
xpow468 power -5.32711606 -8447286 -> 9.09138728E-6136888 Inexact Rounded
xrem468 remainder -5.32711606 -8447286.21 -> -5.32711606
xsub468 subtract -5.32711606 -8447286.21 -> 8447280.88 Inexact Rounded
xadd469 add -82272171.8 -776.238587E-372690416 -> -82272171.8 Inexact Rounded
xcom469 compare -82272171.8 -776.238587E-372690416 -> -1
xdiv469 divide -82272171.8 -776.238587E-372690416 -> 1.05988253E+372690421 Inexact Rounded
xdvi469 divideint -82272171.8 -776.238587E-372690416 -> NaN Division_impossible
xmul469 multiply -82272171.8 -776.238587E-372690416 -> 6.38628344E-372690406 Inexact Rounded
xpow469 power -82272171.8 -8 -> 4.76404994E-64 Inexact Rounded
xrem469 remainder -82272171.8 -776.238587E-372690416 -> NaN Division_impossible
xsub469 subtract -82272171.8 -776.238587E-372690416 -> -82272171.8 Inexact Rounded
xadd470 add 412411244.E-774339264 866452.465 -> 866452.465 Inexact Rounded
xcom470 compare 412411244.E-774339264 866452.465 -> -1
xdiv470 divide 412411244.E-774339264 866452.465 -> 4.75976768E-774339262 Inexact Rounded
xdvi470 divideint 412411244.E-774339264 866452.465 -> 0
xmul470 multiply 412411244.E-774339264 866452.465 -> 3.57334739E-774339250 Inexact Rounded
xpow470 power 412411244.E-774339264 866452 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem470 remainder 412411244.E-774339264 866452.465 -> 4.12411244E-774339256
xsub470 subtract 412411244.E-774339264 866452.465 -> -866452.465 Inexact Rounded
xadd471 add -103.474598 -3.01660661E-446661257 -> -103.474598 Inexact Rounded
xcom471 compare -103.474598 -3.01660661E-446661257 -> -1
xdiv471 divide -103.474598 -3.01660661E-446661257 -> 3.43016546E+446661258 Inexact Rounded
xdvi471 divideint -103.474598 -3.01660661E-446661257 -> NaN Division_impossible
xmul471 multiply -103.474598 -3.01660661E-446661257 -> 3.12142156E-446661255 Inexact Rounded
xpow471 power -103.474598 -3 -> -9.02607123E-7 Inexact Rounded
xrem471 remainder -103.474598 -3.01660661E-446661257 -> NaN Division_impossible
xsub471 subtract -103.474598 -3.01660661E-446661257 -> -103.474598 Inexact Rounded
xadd472 add -31027.8323 -475378186. -> -475409214 Inexact Rounded
xcom472 compare -31027.8323 -475378186. -> 1
xdiv472 divide -31027.8323 -475378186. -> 0.0000652697856 Inexact Rounded
xdvi472 divideint -31027.8323 -475378186. -> 0
xmul472 multiply -31027.8323 -475378186. -> 1.47499546E+13 Inexact Rounded
xpow472 power -31027.8323 -475378186 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem472 remainder -31027.8323 -475378186. -> -31027.8323
xsub472 subtract -31027.8323 -475378186. -> 475347158 Inexact Rounded
xadd473 add -1199339.72 -5.73068392E+53774632 -> -5.73068392E+53774632 Inexact Rounded
xcom473 compare -1199339.72 -5.73068392E+53774632 -> 1
xdiv473 divide -1199339.72 -5.73068392E+53774632 -> 2.09283872E-53774627 Inexact Rounded
xdvi473 divideint -1199339.72 -5.73068392E+53774632 -> 0
xmul473 multiply -1199339.72 -5.73068392E+53774632 -> 6.87303685E+53774638 Inexact Rounded
xpow473 power -1199339.72 -6 -> 3.36005741E-37 Inexact Rounded
xrem473 remainder -1199339.72 -5.73068392E+53774632 -> -1199339.72
xsub473 subtract -1199339.72 -5.73068392E+53774632 -> 5.73068392E+53774632 Inexact Rounded
xadd474 add -732908.930E+364345433 -3486146.26 -> -7.32908930E+364345438 Inexact Rounded
xcom474 compare -732908.930E+364345433 -3486146.26 -> -1
xdiv474 divide -732908.930E+364345433 -3486146.26 -> 2.10234705E+364345432 Inexact Rounded
xdvi474 divideint -732908.930E+364345433 -3486146.26 -> NaN Division_impossible
xmul474 multiply -732908.930E+364345433 -3486146.26 -> 2.55502773E+364345445 Inexact Rounded
xpow474 power -732908.930E+364345433 -3486146 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem474 remainder -732908.930E+364345433 -3486146.26 -> NaN Division_impossible
xsub474 subtract -732908.930E+364345433 -3486146.26 -> -7.32908930E+364345438 Inexact Rounded
xadd475 add -2376150.83 -46777583.3 -> -49153734.1 Inexact Rounded
xcom475 compare -2376150.83 -46777583.3 -> 1
xdiv475 divide -2376150.83 -46777583.3 -> 0.0507967847 Inexact Rounded
xdvi475 divideint -2376150.83 -46777583.3 -> 0
xmul475 multiply -2376150.83 -46777583.3 -> 1.11150593E+14 Inexact Rounded
xpow475 power -2376150.83 -46777583 -> -3.51886193E-298247976 Inexact Rounded
xrem475 remainder -2376150.83 -46777583.3 -> -2376150.83
xsub475 subtract -2376150.83 -46777583.3 -> 44401432.5 Inexact Rounded
xadd476 add 6.3664211 -140854908. -> -140854902 Inexact Rounded
xcom476 compare 6.3664211 -140854908. -> 1
xdiv476 divide 6.3664211 -140854908. -> -4.51984328E-8 Inexact Rounded
xdvi476 divideint 6.3664211 -140854908. -> -0
xmul476 multiply 6.3664211 -140854908. -> -896741658 Inexact Rounded
xpow476 power 6.3664211 -140854908 -> 7.25432803E-113232608 Inexact Rounded
xrem476 remainder 6.3664211 -140854908. -> 6.3664211
xsub476 subtract 6.3664211 -140854908. -> 140854914 Inexact Rounded
xadd477 add -15.791522 1902.30210E+90741844 -> 1.90230210E+90741847 Inexact Rounded
xcom477 compare -15.791522 1902.30210E+90741844 -> -1
xdiv477 divide -15.791522 1902.30210E+90741844 -> -8.30126929E-90741847 Inexact Rounded
xdvi477 divideint -15.791522 1902.30210E+90741844 -> -0
xmul477 multiply -15.791522 1902.30210E+90741844 -> -3.00402455E+90741848 Inexact Rounded
xpow477 power -15.791522 2 -> 249.372167 Inexact Rounded
xrem477 remainder -15.791522 1902.30210E+90741844 -> -15.791522
xsub477 subtract -15.791522 1902.30210E+90741844 -> -1.90230210E+90741847 Inexact Rounded
xadd478 add 15356.1505E+373950429 2.88020400 -> 1.53561505E+373950433 Inexact Rounded
xcom478 compare 15356.1505E+373950429 2.88020400 -> 1
xdiv478 divide 15356.1505E+373950429 2.88020400 -> 5.33161905E+373950432 Inexact Rounded
xdvi478 divideint 15356.1505E+373950429 2.88020400 -> NaN Division_impossible
xmul478 multiply 15356.1505E+373950429 2.88020400 -> 4.42288461E+373950433 Inexact Rounded
xpow478 power 15356.1505E+373950429 3 -> Infinity Overflow Inexact Rounded
xrem478 remainder 15356.1505E+373950429 2.88020400 -> NaN Division_impossible
xsub478 subtract 15356.1505E+373950429 2.88020400 -> 1.53561505E+373950433 Inexact Rounded
xadd479 add -3.12001326E+318884762 9567.21595 -> -3.12001326E+318884762 Inexact Rounded
xcom479 compare -3.12001326E+318884762 9567.21595 -> -1
xdiv479 divide -3.12001326E+318884762 9567.21595 -> -3.26115066E+318884758 Inexact Rounded
xdvi479 divideint -3.12001326E+318884762 9567.21595 -> NaN Division_impossible
xmul479 multiply -3.12001326E+318884762 9567.21595 -> -2.98498406E+318884766 Inexact Rounded
xpow479 power -3.12001326E+318884762 9567 -> -Infinity Overflow Inexact Rounded
xrem479 remainder -3.12001326E+318884762 9567.21595 -> NaN Division_impossible
xsub479 subtract -3.12001326E+318884762 9567.21595 -> -3.12001326E+318884762 Inexact Rounded
xadd480 add 49436.6528 751.919517 -> 50188.5723 Inexact Rounded
xcom480 compare 49436.6528 751.919517 -> 1
xdiv480 divide 49436.6528 751.919517 -> 65.7472664 Inexact Rounded
xdvi480 divideint 49436.6528 751.919517 -> 65
xmul480 multiply 49436.6528 751.919517 -> 37172384.1 Inexact Rounded
xpow480 power 49436.6528 752 -> 8.41185718E+3529 Inexact Rounded
xrem480 remainder 49436.6528 751.919517 -> 561.884195
xsub480 subtract 49436.6528 751.919517 -> 48684.7333 Inexact Rounded
xadd481 add 552.669453 8.3725760E+16223526 -> 8.37257600E+16223526 Inexact Rounded
xcom481 compare 552.669453 8.3725760E+16223526 -> -1
xdiv481 divide 552.669453 8.3725760E+16223526 -> 6.60094878E-16223525 Inexact Rounded
xdvi481 divideint 552.669453 8.3725760E+16223526 -> 0
xmul481 multiply 552.669453 8.3725760E+16223526 -> 4.62726700E+16223529 Inexact Rounded
xpow481 power 552.669453 8 -> 8.70409632E+21 Inexact Rounded
xrem481 remainder 552.669453 8.3725760E+16223526 -> 552.669453
xsub481 subtract 552.669453 8.3725760E+16223526 -> -8.37257600E+16223526 Inexact Rounded
xadd482 add -3266303 453741.520 -> -2812561.48 Rounded
xcom482 compare -3266303 453741.520 -> -1
xdiv482 divide -3266303 453741.520 -> -7.19859844 Inexact Rounded
xdvi482 divideint -3266303 453741.520 -> -7
xmul482 multiply -3266303 453741.520 -> -1.48205729E+12 Inexact Rounded
xpow482 power -3266303 453742 -> 1.02497315E+2955701 Inexact Rounded
xrem482 remainder -3266303 453741.520 -> -90112.360
xsub482 subtract -3266303 453741.520 -> -3720044.52 Rounded
xadd483 add 12302757.4 542922.487E+414443353 -> 5.42922487E+414443358 Inexact Rounded
xcom483 compare 12302757.4 542922.487E+414443353 -> -1
xdiv483 divide 12302757.4 542922.487E+414443353 -> 2.26602465E-414443352 Inexact Rounded
xdvi483 divideint 12302757.4 542922.487E+414443353 -> 0
xmul483 multiply 12302757.4 542922.487E+414443353 -> 6.67944364E+414443365 Inexact Rounded
xpow483 power 12302757.4 5 -> 2.81846276E+35 Inexact Rounded
xrem483 remainder 12302757.4 542922.487E+414443353 -> 12302757.4
xsub483 subtract 12302757.4 542922.487E+414443353 -> -5.42922487E+414443358 Inexact Rounded
xadd484 add -5670757.79E-784754984 128144.503 -> 128144.503 Inexact Rounded
xcom484 compare -5670757.79E-784754984 128144.503 -> -1
xdiv484 divide -5670757.79E-784754984 128144.503 -> -4.42528369E-784754983 Inexact Rounded
xdvi484 divideint -5670757.79E-784754984 128144.503 -> -0
xmul484 multiply -5670757.79E-784754984 128144.503 -> -7.26676439E-784754973 Inexact Rounded
xpow484 power -5670757.79E-784754984 128145 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem484 remainder -5670757.79E-784754984 128144.503 -> -5.67075779E-784754978
xsub484 subtract -5670757.79E-784754984 128144.503 -> -128144.503 Inexact Rounded
xadd485 add 22.7721968E+842530698 5223.70462 -> 2.27721968E+842530699 Inexact Rounded
xcom485 compare 22.7721968E+842530698 5223.70462 -> 1
xdiv485 divide 22.7721968E+842530698 5223.70462 -> 4.35939596E+842530695 Inexact Rounded
xdvi485 divideint 22.7721968E+842530698 5223.70462 -> NaN Division_impossible
xmul485 multiply 22.7721968E+842530698 5223.70462 -> 1.18955230E+842530703 Inexact Rounded
xpow485 power 22.7721968E+842530698 5224 -> Infinity Overflow Inexact Rounded
xrem485 remainder 22.7721968E+842530698 5223.70462 -> NaN Division_impossible
xsub485 subtract 22.7721968E+842530698 5223.70462 -> 2.27721968E+842530699 Inexact Rounded
xadd486 add 88.5158199E-980164357 325846116 -> 325846116 Inexact Rounded
xcom486 compare 88.5158199E-980164357 325846116 -> -1
xdiv486 divide 88.5158199E-980164357 325846116 -> 2.71649148E-980164364 Inexact Rounded
xdvi486 divideint 88.5158199E-980164357 325846116 -> 0
xmul486 multiply 88.5158199E-980164357 325846116 -> 2.88425361E-980164347 Inexact Rounded
xpow486 power 88.5158199E-980164357 325846116 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem486 remainder 88.5158199E-980164357 325846116 -> 8.85158199E-980164356
xsub486 subtract 88.5158199E-980164357 325846116 -> -325846116 Inexact Rounded
xadd487 add -22881.0408 5.63661562 -> -22875.4042 Inexact Rounded
xcom487 compare -22881.0408 5.63661562 -> -1
xdiv487 divide -22881.0408 5.63661562 -> -4059.35802 Inexact Rounded
xdvi487 divideint -22881.0408 5.63661562 -> -4059
xmul487 multiply -22881.0408 5.63661562 -> -128971.632 Inexact Rounded
xpow487 power -22881.0408 6 -> 1.43500909E+26 Inexact Rounded
xrem487 remainder -22881.0408 5.63661562 -> -2.01799842
xsub487 subtract -22881.0408 5.63661562 -> -22886.6774 Inexact Rounded
xadd488 add -7157.57449 -76.4455519E-85647047 -> -7157.57449 Inexact Rounded
xcom488 compare -7157.57449 -76.4455519E-85647047 -> -1
xdiv488 divide -7157.57449 -76.4455519E-85647047 -> 9.36297052E+85647048 Inexact Rounded
xdvi488 divideint -7157.57449 -76.4455519E-85647047 -> NaN Division_impossible
xmul488 multiply -7157.57449 -76.4455519E-85647047 -> 5.47164732E-85647042 Inexact Rounded
xpow488 power -7157.57449 -8 -> 1.45168700E-31 Inexact Rounded
xrem488 remainder -7157.57449 -76.4455519E-85647047 -> NaN Division_impossible
xsub488 subtract -7157.57449 -76.4455519E-85647047 -> -7157.57449 Inexact Rounded
xadd489 add -503113.801 -9715149.82E-612184422 -> -503113.801 Inexact Rounded
xcom489 compare -503113.801 -9715149.82E-612184422 -> -1
xdiv489 divide -503113.801 -9715149.82E-612184422 -> 5.17865201E+612184420 Inexact Rounded
xdvi489 divideint -503113.801 -9715149.82E-612184422 -> NaN Division_impossible
xmul489 multiply -503113.801 -9715149.82E-612184422 -> 4.88782595E-612184410 Inexact Rounded
xpow489 power -503113.801 -10 -> 9.62360287E-58 Inexact Rounded
xrem489 remainder -503113.801 -9715149.82E-612184422 -> NaN Division_impossible
xsub489 subtract -503113.801 -9715149.82E-612184422 -> -503113.801 Inexact Rounded
xadd490 add -3066962.41 -55.3096879 -> -3067017.72 Inexact Rounded
xcom490 compare -3066962.41 -55.3096879 -> -1
xdiv490 divide -3066962.41 -55.3096879 -> 55450.7271 Inexact Rounded
xdvi490 divideint -3066962.41 -55.3096879 -> 55450
xmul490 multiply -3066962.41 -55.3096879 -> 169632734 Inexact Rounded
xpow490 power -3066962.41 -55 -> -1.70229600E-357 Inexact Rounded
xrem490 remainder -3066962.41 -55.3096879 -> -40.2159450
xsub490 subtract -3066962.41 -55.3096879 -> -3066907.10 Inexact Rounded
xadd491 add -53311.5738E+156608936 -7.45890666 -> -5.33115738E+156608940 Inexact Rounded
xcom491 compare -53311.5738E+156608936 -7.45890666 -> -1
xdiv491 divide -53311.5738E+156608936 -7.45890666 -> 7.14737109E+156608939 Inexact Rounded
xdvi491 divideint -53311.5738E+156608936 -7.45890666 -> NaN Division_impossible
xmul491 multiply -53311.5738E+156608936 -7.45890666 -> 3.97646053E+156608941 Inexact Rounded
xpow491 power -53311.5738E+156608936 -7 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem491 remainder -53311.5738E+156608936 -7.45890666 -> NaN Division_impossible
xsub491 subtract -53311.5738E+156608936 -7.45890666 -> -5.33115738E+156608940 Inexact Rounded
xadd492 add 998890068. -92.057879 -> 998889976 Inexact Rounded
xcom492 compare 998890068. -92.057879 -> 1
xdiv492 divide 998890068. -92.057879 -> -10850674.4 Inexact Rounded
xdvi492 divideint 998890068. -92.057879 -> -10850674
xmul492 multiply 998890068. -92.057879 -> -9.19557010E+10 Inexact Rounded
xpow492 power 998890068. -92 -> 1.10757225E-828 Inexact Rounded
xrem492 remainder 998890068. -92.057879 -> 33.839554
xsub492 subtract 998890068. -92.057879 -> 998890160 Inexact Rounded
xadd493 add 122.495591 -407836028. -> -407835906 Inexact Rounded
xcom493 compare 122.495591 -407836028. -> 1
xdiv493 divide 122.495591 -407836028. -> -3.00355002E-7 Inexact Rounded
xdvi493 divideint 122.495591 -407836028. -> -0
xmul493 multiply 122.495591 -407836028. -> -4.99581153E+10 Inexact Rounded
xpow493 power 122.495591 -407836028 -> 4.82463773E-851610754 Inexact Rounded
xrem493 remainder 122.495591 -407836028. -> 122.495591
xsub493 subtract 122.495591 -407836028. -> 407836150 Inexact Rounded
xadd494 add 187098.488 6220.05584E-236541249 -> 187098.488 Inexact Rounded
xcom494 compare 187098.488 6220.05584E-236541249 -> 1
xdiv494 divide 187098.488 6220.05584E-236541249 -> 3.00798727E+236541250 Inexact Rounded
xdvi494 divideint 187098.488 6220.05584E-236541249 -> NaN Division_impossible
xmul494 multiply 187098.488 6220.05584E-236541249 -> 1.16376304E-236541240 Inexact Rounded
xpow494 power 187098.488 6 -> 4.28964811E+31 Inexact Rounded
xrem494 remainder 187098.488 6220.05584E-236541249 -> NaN Division_impossible
xsub494 subtract 187098.488 6220.05584E-236541249 -> 187098.488 Inexact Rounded
xadd495 add 4819899.21E+432982550 -727441917 -> 4.81989921E+432982556 Inexact Rounded
xcom495 compare 4819899.21E+432982550 -727441917 -> 1
xdiv495 divide 4819899.21E+432982550 -727441917 -> -6.62582001E+432982547 Inexact Rounded
xdvi495 divideint 4819899.21E+432982550 -727441917 -> NaN Division_impossible
xmul495 multiply 4819899.21E+432982550 -727441917 -> -3.50619672E+432982565 Inexact Rounded
xpow495 power 4819899.21E+432982550 -727441917 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem495 remainder 4819899.21E+432982550 -727441917 -> NaN Division_impossible
xsub495 subtract 4819899.21E+432982550 -727441917 -> 4.81989921E+432982556 Inexact Rounded
xadd496 add 5770.01020E+507459752 -4208339.33E-129766680 -> 5.77001020E+507459755 Inexact Rounded
xcom496 compare 5770.01020E+507459752 -4208339.33E-129766680 -> 1
xdiv496 divide 5770.01020E+507459752 -4208339.33E-129766680 -> -1.37108958E+637226429 Inexact Rounded
xdvi496 divideint 5770.01020E+507459752 -4208339.33E-129766680 -> NaN Division_impossible
xmul496 multiply 5770.01020E+507459752 -4208339.33E-129766680 -> -2.42821609E+377693082 Inexact Rounded
xpow496 power 5770.01020E+507459752 -4 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
xrem496 remainder 5770.01020E+507459752 -4208339.33E-129766680 -> NaN Division_impossible
xsub496 subtract 5770.01020E+507459752 -4208339.33E-129766680 -> 5.77001020E+507459755 Inexact Rounded
xadd497 add -286.371320 710319152 -> 710318866 Inexact Rounded
xcom497 compare -286.371320 710319152 -> -1
xdiv497 divide -286.371320 710319152 -> -4.03158664E-7 Inexact Rounded
xdvi497 divideint -286.371320 710319152 -> -0
xmul497 multiply -286.371320 710319152 -> -2.03415033E+11 Inexact Rounded
xpow497 power -286.371320 710319152 -> Infinity Overflow Inexact Rounded
xrem497 remainder -286.371320 710319152 -> -286.371320
xsub497 subtract -286.371320 710319152 -> -710319438 Inexact Rounded
xadd498 add -7.27403536 -481469656E-835183700 -> -7.27403536 Inexact Rounded
xcom498 compare -7.27403536 -481469656E-835183700 -> -1
xdiv498 divide -7.27403536 -481469656E-835183700 -> 1.51079830E+835183692 Inexact Rounded
xdvi498 divideint -7.27403536 -481469656E-835183700 -> NaN Division_impossible
xmul498 multiply -7.27403536 -481469656E-835183700 -> 3.50222730E-835183691 Inexact Rounded
xpow498 power -7.27403536 -5 -> -0.0000491046885 Inexact Rounded
xrem498 remainder -7.27403536 -481469656E-835183700 -> NaN Division_impossible
xsub498 subtract -7.27403536 -481469656E-835183700 -> -7.27403536 Inexact Rounded
xadd499 add -6157.74292 -94075286.2E+92555877 -> -9.40752862E+92555884 Inexact Rounded
xcom499 compare -6157.74292 -94075286.2E+92555877 -> 1
xdiv499 divide -6157.74292 -94075286.2E+92555877 -> 6.54554790E-92555882 Inexact Rounded
xdvi499 divideint -6157.74292 -94075286.2E+92555877 -> 0
xmul499 multiply -6157.74292 -94075286.2E+92555877 -> 5.79291428E+92555888 Inexact Rounded
xpow499 power -6157.74292 -9 -> -7.85608218E-35 Inexact Rounded
xrem499 remainder -6157.74292 -94075286.2E+92555877 -> -6157.74292
xsub499 subtract -6157.74292 -94075286.2E+92555877 -> 9.40752862E+92555884 Inexact Rounded
xadd500 add -525445087.E+231529167 188227460 -> -5.25445087E+231529175 Inexact Rounded
xcom500 compare -525445087.E+231529167 188227460 -> -1
xdiv500 divide -525445087.E+231529167 188227460 -> -2.79154321E+231529167 Inexact Rounded
xdvi500 divideint -525445087.E+231529167 188227460 -> NaN Division_impossible
xmul500 multiply -525445087.E+231529167 188227460 -> -9.89031941E+231529183 Inexact Rounded
xpow500 power -525445087.E+231529167 188227460 -> Infinity Overflow Inexact Rounded
xrem500 remainder -525445087.E+231529167 188227460 -> NaN Division_impossible
xsub500 subtract -525445087.E+231529167 188227460 -> -5.25445087E+231529175 Inexact Rounded

