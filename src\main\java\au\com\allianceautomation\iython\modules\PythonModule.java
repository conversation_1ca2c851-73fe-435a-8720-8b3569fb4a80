package au.com.allianceautomation.iython.modules;

import java.util.HashMap;
import java.util.Map;

/**
 * Represents a Python module in the iython interpreter.
 * Contains the module's name, file path, source code, and namespace.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonModule {
    private final String name;
    private final String filePath;
    private final String sourceCode;
    private final Map<String, Object> namespace;
    private boolean executed = false;
    
    /**
     * Create a new Python module.
     * 
     * @param name The module name
     * @param filePath The path to the module file
     * @param sourceCode The source code of the module
     */
    public PythonModule(String name, String filePath, String sourceCode) {
        this.name = name;
        this.filePath = filePath;
        this.sourceCode = sourceCode;
        this.namespace = new HashMap<>();
        this.executed = false;
        
        // Set standard module attributes
        this.namespace.put("__name__", name);
        this.namespace.put("__file__", filePath);
    }
    
    /**
     * Get the module name.
     * 
     * @return The module name
     */
    public String getName() {
        return name;
    }
    
    /**
     * Get the module file path.
     * 
     * @return The file path
     */
    public String getFilePath() {
        return filePath;
    }
    
    /**
     * Get the module source code.
     * 
     * @return The source code
     */
    public String getSourceCode() {
        return sourceCode;
    }
    
    /**
     * Get the module namespace.
     * 
     * @return The namespace map
     */
    public Map<String, Object> getNamespace() {
        return namespace;
    }
    
    /**
     * Check if the module has been executed.
     * 
     * @return true if executed, false otherwise
     */
    public boolean isExecuted() {
        return executed;
    }
    
    /**
     * Mark the module as executed.
     */
    public void markExecuted() {
        this.executed = true;
    }
    
    /**
     * Get an attribute from the module namespace.
     * 
     * @param name The attribute name
     * @return The attribute value or null if not found
     */
    public Object getAttribute(String name) {
        return namespace.get(name);
    }
    
    /**
     * Set an attribute in the module namespace.
     * 
     * @param name The attribute name
     * @param value The attribute value
     */
    public void setAttribute(String name, Object value) {
        namespace.put(name, value);
    }
    
    /**
     * Check if the module has an attribute.
     * 
     * @param name The attribute name
     * @return true if the attribute exists, false otherwise
     */
    public boolean hasAttribute(String name) {
        return namespace.containsKey(name);
    }
    
    @Override
    public String toString() {
        return "<module '" + name + "' from '" + filePath + "'>";
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        PythonModule that = (PythonModule) obj;
        return name.equals(that.name);
    }
    
    @Override
    public int hashCode() {
        return name.hashCode();
    }
}
