------------------------------------------------------------------------
-- remainderNear.decTest -- decimal remainder-near (IEEE remainder)   --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

extended:    1
precision:   9
rounding:    half_up
maxExponent: 384
minexponent: -383

rmnx001 remaindernear  1     1    ->  0
rmnx002 remaindernear  2     1    ->  0
rmnx003 remaindernear  1     2    ->  1
rmnx004 remaindernear  2     2    ->  0
rmnx005 remaindernear  0     1    ->  0
rmnx006 remaindernear  0     2    ->  0
rmnx007 remaindernear  1     3    ->  1
rmnx008 remaindernear  2     3    -> -1
rmnx009 remaindernear  3     3    ->  0

rmnx010 remaindernear  2.4   1    ->  0.4
rmnx011 remaindernear  2.4   -1   ->  0.4
rmnx012 remaindernear  -2.4  1    ->  -0.4
rmnx013 remaindernear  -2.4  -1   ->  -0.4
rmnx014 remaindernear  2.40  1    ->  0.40
rmnx015 remaindernear  2.400 1    ->  0.400
rmnx016 remaindernear  2.4   2    ->  0.4
rmnx017 remaindernear  2.400 2    ->  0.400
rmnx018 remaindernear  2.    2    ->  0
rmnx019 remaindernear  20    20   ->  0

rmnx020 remaindernear  187   187    ->  0
rmnx021 remaindernear  5     2      ->  1
rmnx022 remaindernear  5     2.0    ->  1.0
rmnx023 remaindernear  5     2.000  ->  1.000
rmnx024 remaindernear  5     0.200  ->  0.000
rmnx025 remaindernear  5     0.200  ->  0.000

rmnx030 remaindernear  1     2      ->  1
rmnx031 remaindernear  1     4      ->  1
rmnx032 remaindernear  1     8      ->  1
rmnx033 remaindernear  1     16     ->  1
rmnx034 remaindernear  1     32     ->  1
rmnx035 remaindernear  1     64     ->  1
rmnx040 remaindernear  1    -2      ->  1
rmnx041 remaindernear  1    -4      ->  1
rmnx042 remaindernear  1    -8      ->  1
rmnx043 remaindernear  1    -16     ->  1
rmnx044 remaindernear  1    -32     ->  1
rmnx045 remaindernear  1    -64     ->  1
rmnx050 remaindernear -1     2      ->  -1
rmnx051 remaindernear -1     4      ->  -1
rmnx052 remaindernear -1     8      ->  -1
rmnx053 remaindernear -1     16     ->  -1
rmnx054 remaindernear -1     32     ->  -1
rmnx055 remaindernear -1     64     ->  -1
rmnx060 remaindernear -1    -2      ->  -1
rmnx061 remaindernear -1    -4      ->  -1
rmnx062 remaindernear -1    -8      ->  -1
rmnx063 remaindernear -1    -16     ->  -1
rmnx064 remaindernear -1    -32     ->  -1
rmnx065 remaindernear -1    -64     ->  -1

rmnx066 remaindernear  999999997     1  -> 0
rmnx067 remaindernear  999999997.4   1  -> 0.4
rmnx068 remaindernear  999999997.5   1  -> -0.5
rmnx069 remaindernear  999999997.9   1  -> -0.1
rmnx070 remaindernear  999999997.999 1  -> -0.001

rmnx071 remaindernear  999999998     1  -> 0
rmnx072 remaindernear  999999998.4   1  -> 0.4
rmnx073 remaindernear  999999998.5   1  -> 0.5
rmnx074 remaindernear  999999998.9   1  -> -0.1
rmnx075 remaindernear  999999998.999 1  -> -0.001

rmnx076 remaindernear  999999999     1  -> 0
rmnx077 remaindernear  999999999.4   1  -> 0.4
rmnx078 remaindernear  999999999.5   1  -> NaN Division_impossible
rmnx079 remaindernear  999999999.9   1  -> NaN Division_impossible
rmnx080 remaindernear  999999999.999 1  -> NaN Division_impossible

precision: 6
rmnx081 remaindernear  999999999     1  -> NaN Division_impossible
rmnx082 remaindernear  99999999      1  -> NaN Division_impossible
rmnx083 remaindernear  9999999       1  -> NaN Division_impossible
rmnx084 remaindernear  999999        1  -> 0
rmnx085 remaindernear  99999         1  -> 0
rmnx086 remaindernear  9999          1  -> 0
rmnx087 remaindernear  999           1  -> 0
rmnx088 remaindernear  99            1  -> 0
rmnx089 remaindernear  9             1  -> 0

precision: 9
rmnx090 remaindernear  0.            1  -> 0
rmnx091 remaindernear  .0            1  -> 0.0
rmnx092 remaindernear  0.00          1  -> 0.00
rmnx093 remaindernear  0.00E+9       1  -> 0
rmnx094 remaindernear  0.0000E-50    1  -> 0E-54


-- Various flavours of remaindernear by 0
precision: 9
maxexponent: 999999999
minexponent: -999999999
rmnx101 remaindernear  0       0   -> NaN Division_undefined
rmnx102 remaindernear  0      -0   -> NaN Division_undefined
rmnx103 remaindernear -0       0   -> NaN Division_undefined
rmnx104 remaindernear -0      -0   -> NaN Division_undefined
rmnx105 remaindernear  0.0E5   0   -> NaN Division_undefined
rmnx106 remaindernear  0.000   0   -> NaN Division_undefined
-- [Some think this next group should be Division_by_zero exception,
-- but IEEE 854 is explicit that it is Invalid operation .. for
-- remaindernear-near, anyway]
rmnx107 remaindernear  0.0001  0   -> NaN Invalid_operation
rmnx108 remaindernear  0.01    0   -> NaN Invalid_operation
rmnx109 remaindernear  0.1     0   -> NaN Invalid_operation
rmnx110 remaindernear  1       0   -> NaN Invalid_operation
rmnx111 remaindernear  1       0.0 -> NaN Invalid_operation
rmnx112 remaindernear 10       0.0 -> NaN Invalid_operation
rmnx113 remaindernear 1E+100   0.0 -> NaN Invalid_operation
rmnx114 remaindernear 1E+1000  0   -> NaN Invalid_operation
rmnx115 remaindernear  0.0001 -0   -> NaN Invalid_operation
rmnx116 remaindernear  0.01   -0   -> NaN Invalid_operation
rmnx119 remaindernear  0.1    -0   -> NaN Invalid_operation
rmnx120 remaindernear  1      -0   -> NaN Invalid_operation
rmnx121 remaindernear  1      -0.0 -> NaN Invalid_operation
rmnx122 remaindernear 10      -0.0 -> NaN Invalid_operation
rmnx123 remaindernear 1E+100  -0.0 -> NaN Invalid_operation
rmnx124 remaindernear 1E+1000 -0   -> NaN Invalid_operation
-- and zeros on left
rmnx130 remaindernear  0      1   ->  0
rmnx131 remaindernear  0     -1   ->  0
rmnx132 remaindernear  0.0    1   ->  0.0
rmnx133 remaindernear  0.0   -1   ->  0.0
rmnx134 remaindernear -0      1   -> -0
rmnx135 remaindernear -0     -1   -> -0
rmnx136 remaindernear -0.0    1   -> -0.0
rmnx137 remaindernear -0.0   -1   -> -0.0

-- 0.5ers
rmmx143 remaindernear   0.5  2     ->  0.5
rmmx144 remaindernear   0.5  2.1   ->  0.5
rmmx145 remaindernear   0.5  2.01  ->  0.50
rmmx146 remaindernear   0.5  2.001 ->  0.500
rmmx147 remaindernear   0.50 2     ->  0.50
rmmx148 remaindernear   0.50 2.01  ->  0.50
rmmx149 remaindernear   0.50 2.001 ->  0.500

-- some differences from remainder
rmnx150 remaindernear   0.4  1.020 ->  0.400
rmnx151 remaindernear   0.50 1.020 ->  0.500
rmnx152 remaindernear   0.51 1.020 ->  0.510
rmnx153 remaindernear   0.52 1.020 -> -0.500
rmnx154 remaindernear   0.6  1.020 -> -0.420
rmnx155 remaindernear   0.49 1     ->  0.49
rmnx156 remaindernear   0.50 1     ->  0.50
rmnx157 remaindernear   1.50 1     -> -0.50
rmnx158 remaindernear   2.50 1     ->  0.50
rmnx159 remaindernear   9.50 1     -> -0.50
rmnx160 remaindernear   0.51 1     -> -0.49

-- the nasty division-by-1 cases
rmnx161 remaindernear   0.4         1   ->  0.4
rmnx162 remaindernear   0.45        1   ->  0.45
rmnx163 remaindernear   0.455       1   ->  0.455
rmnx164 remaindernear   0.4555      1   ->  0.4555
rmnx165 remaindernear   0.45555     1   ->  0.45555
rmnx166 remaindernear   0.455555    1   ->  0.455555
rmnx167 remaindernear   0.4555555   1   ->  0.4555555
rmnx168 remaindernear   0.45555555  1   ->  0.45555555
rmnx169 remaindernear   0.455555555 1   ->  0.455555555
-- with spill...
rmnx171 remaindernear   0.5         1   ->  0.5
rmnx172 remaindernear   0.55        1   -> -0.45
rmnx173 remaindernear   0.555       1   -> -0.445
rmnx174 remaindernear   0.5555      1   -> -0.4445
rmnx175 remaindernear   0.55555     1   -> -0.44445
rmnx176 remaindernear   0.555555    1   -> -0.444445
rmnx177 remaindernear   0.5555555   1   -> -0.4444445
rmnx178 remaindernear   0.55555555  1   -> -0.44444445
rmnx179 remaindernear   0.555555555 1   -> -0.444444445

-- progression
rmnx180 remaindernear  1  1   -> 0
rmnx181 remaindernear  1  2   -> 1
rmnx182 remaindernear  1  3   -> 1
rmnx183 remaindernear  1  4   -> 1
rmnx184 remaindernear  1  5   -> 1
rmnx185 remaindernear  1  6   -> 1
rmnx186 remaindernear  1  7   -> 1
rmnx187 remaindernear  1  8   -> 1
rmnx188 remaindernear  1  9   -> 1
rmnx189 remaindernear  1  10  -> 1
rmnx190 remaindernear  1  1   -> 0
rmnx191 remaindernear  2  1   -> 0
rmnx192 remaindernear  3  1   -> 0
rmnx193 remaindernear  4  1   -> 0
rmnx194 remaindernear  5  1   -> 0
rmnx195 remaindernear  6  1   -> 0
rmnx196 remaindernear  7  1   -> 0
rmnx197 remaindernear  8  1   -> 0
rmnx198 remaindernear  9  1   -> 0
rmnx199 remaindernear  10 1   -> 0


-- Various flavours of remaindernear by 0
maxexponent: 999999999
minexponent: -999999999
rmnx201 remaindernear  0      0   -> NaN Division_undefined
rmnx202 remaindernear  0.0E5  0   -> NaN Division_undefined
rmnx203 remaindernear  0.000  0   -> NaN Division_undefined
rmnx204 remaindernear  0.0001 0   -> NaN Invalid_operation
rmnx205 remaindernear  0.01   0   -> NaN Invalid_operation
rmnx206 remaindernear  0.1    0   -> NaN Invalid_operation
rmnx207 remaindernear  1      0   -> NaN Invalid_operation
rmnx208 remaindernear  1      0.0 -> NaN Invalid_operation
rmnx209 remaindernear 10      0.0 -> NaN Invalid_operation
rmnx210 remaindernear 1E+100  0.0 -> NaN Invalid_operation
rmnx211 remaindernear 1E+1000 0   -> NaN Invalid_operation

-- tests from the extended specification
rmnx221 remaindernear 2.1     3   -> -0.9
rmnx222 remaindernear  10     6   -> -2
rmnx223 remaindernear  10     3   ->  1
rmnx224 remaindernear -10     3   -> -1
rmnx225 remaindernear  10.2   1   -> 0.2
rmnx226 remaindernear  10     0.3 -> 0.1
rmnx227 remaindernear   3.6   1.3 -> -0.3

-- some differences from remainder
rmnx231 remaindernear   0.4  1.020 ->  0.400
rmnx232 remaindernear   0.50 1.020 ->  0.500
rmnx233 remaindernear   0.51 1.020 ->  0.510
rmnx234 remaindernear   0.52 1.020 -> -0.500
rmnx235 remaindernear   0.6  1.020 -> -0.420

-- test some cases that are close to exponent overflow
maxexponent: 999999999
minexponent: -999999999
rmnx270 remaindernear 1 1e999999999    -> 1
rmnx271 remaindernear 1 0.9e999999999  -> 1
rmnx272 remaindernear 1 0.99e999999999 -> 1
rmnx273 remaindernear 1 0.999999999e999999999 -> 1
rmnx274 remaindernear 9e999999999          1 -> NaN Division_impossible
rmnx275 remaindernear 9.9e999999999        1 -> NaN Division_impossible
rmnx276 remaindernear 9.99e999999999       1 -> NaN Division_impossible
rmnx277 remaindernear 9.99999999e999999999 1 -> NaN Division_impossible

rmnx280 remaindernear 0.1 9e-999999999       -> NaN Division_impossible
rmnx281 remaindernear 0.1 99e-999999999      -> NaN Division_impossible
rmnx282 remaindernear 0.1 999e-999999999     -> NaN Division_impossible

rmnx283 remaindernear 0.1 9e-999999998       -> NaN Division_impossible
rmnx284 remaindernear 0.1 99e-999999998      -> NaN Division_impossible
rmnx285 remaindernear 0.1 999e-999999998     -> NaN Division_impossible
rmnx286 remaindernear 0.1 999e-999999997     -> NaN Division_impossible
rmnx287 remaindernear 0.1 9999e-999999997    -> NaN Division_impossible
rmnx288 remaindernear 0.1 99999e-999999997   -> NaN Division_impossible

-- rmnx3xx are from DiagBigDecimal
rmnx301 remaindernear   1    3     ->  1
rmnx302 remaindernear   5    5     ->  0
rmnx303 remaindernear   13   10    ->  3
rmnx304 remaindernear   13   50    ->  13
rmnx305 remaindernear   13   100   ->  13
rmnx306 remaindernear   13   1000  ->  13
rmnx307 remaindernear   .13    1   ->  0.13
rmnx308 remaindernear   0.133  1   ->  0.133
rmnx309 remaindernear   0.1033 1   ->  0.1033
rmnx310 remaindernear   1.033  1   ->  0.033
rmnx311 remaindernear   10.33  1   ->  0.33
rmnx312 remaindernear   10.33 10   ->  0.33
rmnx313 remaindernear   103.3  1   ->  0.3
rmnx314 remaindernear   133   10   ->  3
rmnx315 remaindernear   1033  10   ->  3
rmnx316 remaindernear   1033  50   -> -17
rmnx317 remaindernear   101.0  3   -> -1.0
rmnx318 remaindernear   102.0  3   ->  0.0
rmnx319 remaindernear   103.0  3   ->  1.0
rmnx320 remaindernear   2.40   1   ->  0.40
rmnx321 remaindernear   2.400  1   ->  0.400
rmnx322 remaindernear   2.4    1   ->  0.4
rmnx323 remaindernear   2.4    2   ->  0.4
rmnx324 remaindernear   2.400  2   ->  0.400
rmnx325 remaindernear   1   0.3    ->  0.1
rmnx326 remaindernear   1   0.30   ->  0.10
rmnx327 remaindernear   1   0.300  ->  0.100
rmnx328 remaindernear   1   0.3000 ->  0.1000
rmnx329 remaindernear   1.0    0.3 ->  0.1
rmnx330 remaindernear   1.00   0.3 ->  0.10
rmnx331 remaindernear   1.000  0.3 ->  0.100
rmnx332 remaindernear   1.0000 0.3 ->  0.1000
rmnx333 remaindernear   0.5  2     ->  0.5
rmnx334 remaindernear   0.5  2.1   ->  0.5
rmnx335 remaindernear   0.5  2.01  ->  0.50
rmnx336 remaindernear   0.5  2.001 ->  0.500
rmnx337 remaindernear   0.50 2     ->  0.50
rmnx338 remaindernear   0.50 2.01  ->  0.50
rmnx339 remaindernear   0.50 2.001 ->  0.500

rmnx340 remaindernear   0.5   0.5000001    ->  -1E-7
rmnx341 remaindernear   0.5   0.50000001    ->  -1E-8
rmnx342 remaindernear   0.5   0.500000001    ->  -1E-9
rmnx343 remaindernear   0.5   0.5000000001    ->  -1E-10
rmnx344 remaindernear   0.5   0.50000000001    ->  -1E-11
rmnx345 remaindernear   0.5   0.4999999    ->  1E-7
rmnx346 remaindernear   0.5   0.49999999    ->  1E-8
rmnx347 remaindernear   0.5   0.499999999    ->  1E-9
rmnx348 remaindernear   0.5   0.4999999999    ->  1E-10
rmnx349 remaindernear   0.5   0.49999999999    ->  1E-11

rmnx350 remaindernear   0.03  7  ->  0.03
rmnx351 remaindernear   5   2    ->  1
rmnx352 remaindernear   4.1   2    ->  0.1
rmnx353 remaindernear   4.01   2    ->  0.01
rmnx354 remaindernear   4.001   2    ->  0.001
rmnx355 remaindernear   4.0001   2    ->  0.0001
rmnx356 remaindernear   4.00001   2    ->  0.00001
rmnx357 remaindernear   4.000001   2    ->  0.000001
rmnx358 remaindernear   4.0000001   2    ->  1E-7

rmnx360 remaindernear   1.2   0.7345 -> -0.2690
rmnx361 remaindernear   0.8   12     ->  0.8
rmnx362 remaindernear   0.8   0.2    ->  0.0
rmnx363 remaindernear   0.8   0.3    -> -0.1
rmnx364 remaindernear   0.800   12   ->  0.800
rmnx365 remaindernear   0.800   1.7  ->  0.800
rmnx366 remaindernear   2.400   2    ->  0.400

precision: 6
rmnx371 remaindernear   2.400  2        ->  0.400
precision: 3
rmnx372 remaindernear   12345678900000 12e+12 -> 3.46E+11 Inexact Rounded

precision: 5
rmnx381 remaindernear 12345  1         ->  0
rmnx382 remaindernear 12345  1.0001    -> -0.2344
rmnx383 remaindernear 12345  1.001     -> -0.333
rmnx384 remaindernear 12345  1.01      -> -0.23
rmnx385 remaindernear 12345  1.1       -> -0.3
rmnx386 remaindernear 12355  4         -> -1
rmnx387 remaindernear 12345  4         ->  1
rmnx388 remaindernear 12355  4.0001    -> -1.3089
rmnx389 remaindernear 12345  4.0001    ->  0.6914
rmnx390 remaindernear 12345  4.9       ->  1.9
rmnx391 remaindernear 12345  4.99      -> -0.26
rmnx392 remaindernear 12345  4.999     ->  2.469
rmnx393 remaindernear 12345  4.9999    ->  0.2469
rmnx394 remaindernear 12345  5         ->  0
rmnx395 remaindernear 12345  5.0001    -> -0.2469
rmnx396 remaindernear 12345  5.001     -> -2.469
rmnx397 remaindernear 12345  5.01      ->  0.36
rmnx398 remaindernear 12345  5.1       -> -2.1

precision: 9
-- some nasty division-by-1 cases [some similar above]
rmnx401 remaindernear   0.4         1   ->  0.4
rmnx402 remaindernear   0.45        1   ->  0.45
rmnx403 remaindernear   0.455       1   ->  0.455
rmnx404 remaindernear   0.4555      1   ->  0.4555
rmnx405 remaindernear   0.45555     1   ->  0.45555
rmnx406 remaindernear   0.455555    1   ->  0.455555
rmnx407 remaindernear   0.4555555   1   ->  0.4555555
rmnx408 remaindernear   0.45555555  1   ->  0.45555555
rmnx409 remaindernear   0.455555555 1   ->  0.455555555

-- some tricky LHSs
rmnx420 remaindernear   99999999.999999999   1E+8   -> -1E-9
rmnx421 remaindernear  999999999.999999999   1E+9   -> -1E-9
precision: 9
rmnx430 remaindernear   0.455555555 1   ->  0.455555555
precision: 8
rmnx431 remaindernear   0.455555555 1   ->  0.45555556 Inexact Rounded
precision: 7
rmnx432 remaindernear   0.455555555 1   ->  0.4555556  Inexact Rounded
precision: 6
rmnx433 remaindernear   0.455555555 1   ->  0.455556   Inexact Rounded
precision: 5
rmnx434 remaindernear   0.455555555 1   ->  0.45556    Inexact Rounded
precision: 4
rmnx435 remaindernear   0.455555555 1   ->  0.4556     Inexact Rounded
precision: 3
rmnx436 remaindernear   0.455555555 1   ->  0.456      Inexact Rounded
precision: 2
rmnx437 remaindernear   0.455555555 1   ->  0.46       Inexact Rounded
precision: 1
rmnx438 remaindernear   0.455555555 1   ->  0.5        Inexact Rounded

-- early tests; from text descriptions
precision: 9
rmnx601 remaindernear  10   6  -> -2
rmnx602 remaindernear -10   6  -> 2
rmnx603 remaindernear  11   3  -> -1
rmnx604 remaindernear  11   5  -> 1
rmnx605 remaindernear   7.7 8  -> -0.3
rmnx606 remaindernear  31.5 3  -> 1.5    -- i=10
rmnx607 remaindernear  34.5 3  -> -1.5   -- i=11

-- zero signs
rmnx650 remaindernear  1  1 ->  0
rmnx651 remaindernear -1  1 -> -0
rmnx652 remaindernear  1 -1 ->  0
rmnx653 remaindernear -1 -1 -> -0
rmnx654 remaindernear  0  1 ->  0
rmnx655 remaindernear -0  1 -> -0
rmnx656 remaindernear  0 -1 ->  0
rmnx657 remaindernear -0 -1 -> -0
rmnx658 remaindernear  0.00  1  ->  0.00
rmnx659 remaindernear -0.00  1  -> -0.00

-- Specials
rmnx680 remaindernear  Inf  -Inf   ->  NaN Invalid_operation
rmnx681 remaindernear  Inf  -1000  ->  NaN Invalid_operation
rmnx682 remaindernear  Inf  -1     ->  NaN Invalid_operation
rmnx683 remaindernear  Inf   0     ->  NaN Invalid_operation
rmnx684 remaindernear  Inf  -0     ->  NaN Invalid_operation
rmnx685 remaindernear  Inf   1     ->  NaN Invalid_operation
rmnx686 remaindernear  Inf   1000  ->  NaN Invalid_operation
rmnx687 remaindernear  Inf   Inf   ->  NaN Invalid_operation
rmnx688 remaindernear -1000  Inf   -> -1000
rmnx689 remaindernear -Inf   Inf   ->  NaN Invalid_operation
rmnx691 remaindernear -1     Inf   -> -1
rmnx692 remaindernear  0     Inf   ->  0
rmnx693 remaindernear -0     Inf   -> -0
rmnx694 remaindernear  1     Inf   ->  1
rmnx695 remaindernear  1000  Inf   ->  1000
rmnx696 remaindernear  Inf   Inf   ->  NaN Invalid_operation

rmnx700 remaindernear -Inf  -Inf   ->  NaN Invalid_operation
rmnx701 remaindernear -Inf  -1000  ->  NaN Invalid_operation
rmnx702 remaindernear -Inf  -1     ->  NaN Invalid_operation
rmnx703 remaindernear -Inf  -0     ->  NaN Invalid_operation
rmnx704 remaindernear -Inf   0     ->  NaN Invalid_operation
rmnx705 remaindernear -Inf   1     ->  NaN Invalid_operation
rmnx706 remaindernear -Inf   1000  ->  NaN Invalid_operation
rmnx707 remaindernear -Inf   Inf   ->  NaN Invalid_operation
rmnx708 remaindernear -Inf  -Inf   ->  NaN Invalid_operation
rmnx709 remaindernear -1000  Inf   -> -1000
rmnx710 remaindernear -1    -Inf   -> -1
rmnx711 remaindernear -0    -Inf   -> -0
rmnx712 remaindernear  0    -Inf   ->  0
rmnx713 remaindernear  1    -Inf   ->  1
rmnx714 remaindernear  1000 -Inf   ->  1000
rmnx715 remaindernear  Inf  -Inf   ->  NaN Invalid_operation

rmnx721 remaindernear  NaN -Inf    ->  NaN
rmnx722 remaindernear  NaN -1000   ->  NaN
rmnx723 remaindernear  NaN -1      ->  NaN
rmnx724 remaindernear  NaN -0      ->  NaN
rmnx725 remaindernear  NaN  0      ->  NaN
rmnx726 remaindernear  NaN  1      ->  NaN
rmnx727 remaindernear  NaN  1000   ->  NaN
rmnx728 remaindernear  NaN  Inf    ->  NaN
rmnx729 remaindernear  NaN  NaN    ->  NaN
rmnx730 remaindernear -Inf  NaN    ->  NaN
rmnx731 remaindernear -1000 NaN    ->  NaN
rmnx732 remaindernear -1   -NaN    -> -NaN
rmnx733 remaindernear -0    NaN    ->  NaN
rmnx734 remaindernear  0    NaN    ->  NaN
rmnx735 remaindernear  1    NaN    ->  NaN
rmnx736 remaindernear  1000 NaN    ->  NaN
rmnx737 remaindernear  Inf  NaN    ->  NaN

rmnx741 remaindernear  sNaN -Inf   ->  NaN  Invalid_operation
rmnx742 remaindernear  sNaN -1000  ->  NaN  Invalid_operation
rmnx743 remaindernear -sNaN -1     -> -NaN  Invalid_operation
rmnx744 remaindernear  sNaN -0     ->  NaN  Invalid_operation
rmnx745 remaindernear  sNaN  0     ->  NaN  Invalid_operation
rmnx746 remaindernear  sNaN  1     ->  NaN  Invalid_operation
rmnx747 remaindernear  sNaN  1000  ->  NaN  Invalid_operation
rmnx749 remaindernear  sNaN  NaN   ->  NaN  Invalid_operation
rmnx750 remaindernear  sNaN sNaN   ->  NaN  Invalid_operation
rmnx751 remaindernear  NaN  sNaN   ->  NaN  Invalid_operation
rmnx752 remaindernear -Inf  sNaN   ->  NaN  Invalid_operation
rmnx753 remaindernear -1000 sNaN   ->  NaN  Invalid_operation
rmnx754 remaindernear -1    sNaN   ->  NaN  Invalid_operation
rmnx755 remaindernear -0   -sNaN   -> -NaN  Invalid_operation
rmnx756 remaindernear  0    sNaN   ->  NaN  Invalid_operation
rmnx757 remaindernear  1    sNaN   ->  NaN  Invalid_operation
rmnx758 remaindernear  1000 sNaN   ->  NaN  Invalid_operation
rmnx759 remaindernear  Inf  sNaN   ->  NaN  Invalid_operation
rmnx760 remaindernear  NaN  sNaN   ->  NaN  Invalid_operation

-- propagating NaNs
rmnx761 remaindernear  NaN1   NaN7   ->  NaN1
rmnx762 remaindernear sNaN2   NaN8   ->  NaN2 Invalid_operation
rmnx763 remaindernear  NaN3 -sNaN9   -> -NaN9 Invalid_operation
rmnx764 remaindernear sNaN4  sNaN10  ->  NaN4 Invalid_operation
rmnx765 remaindernear    15   NaN11  ->  NaN11
rmnx766 remaindernear  NaN6   NaN12  ->  NaN6
rmnx767 remaindernear  Inf   -NaN13  -> -NaN13
rmnx768 remaindernear  NaN14  -Inf   ->  NaN14
rmnx769 remaindernear    0    NaN15  ->  NaN15
rmnx770 remaindernear -NaN16   -0    -> -NaN16

-- test some cases that are close to exponent overflow
maxexponent: 999999999
minexponent: -999999999
rmnx780 remaindernear 1 1e999999999    -> 1
rmnx781 remaindernear 1 0.9e999999999  -> 1
rmnx782 remaindernear 1 0.99e999999999 -> 1
rmnx783 remaindernear 1 0.999999999e999999999 -> 1
rmnx784 remaindernear 9e999999999          1 -> NaN Division_impossible
rmnx785 remaindernear 9.9e999999999        1 -> NaN Division_impossible
rmnx786 remaindernear 9.99e999999999       1 -> NaN Division_impossible
rmnx787 remaindernear 9.99999999e999999999 1 -> NaN Division_impossible


-- overflow and underflow tests [from divide]
precision: 9
maxexponent: 999999999
minexponent: -999999999
rmnx790 remaindernear +1.23456789012345E-0 9E+999999999 -> 1.23456789 Inexact Rounded
rmnx791 remaindernear 9E+999999999 +0.23456789012345E-0 -> NaN Division_impossible
rmnx792 remaindernear +0.100 9E+999999999               -> 0.100
rmnx793 remaindernear 9E-999999999 +9.100               -> 9E-999999999
rmnx795 remaindernear -1.23456789012345E-0 9E+999999999 -> -1.23456789 Inexact Rounded
rmnx796 remaindernear 9E+999999999 -0.83456789012345E-0 -> NaN Division_impossible
rmnx797 remaindernear -0.100 9E+999999999               -> -0.100
rmnx798 remaindernear 9E-999999999 -9.100               -> 9E-999999999

-- long operands checks
maxexponent: 999
minexponent: -999
precision: 9
rmnx801 remaindernear 12345678000 100 -> 0
rmnx802 remaindernear 1 12345678000   -> 1
rmnx803 remaindernear 1234567800  10  -> 0
rmnx804 remaindernear 1 1234567800    -> 1
rmnx805 remaindernear 1234567890  10  -> 0
rmnx806 remaindernear 1 1234567890    -> 1
rmnx807 remaindernear 1234567891  10  -> 1
rmnx808 remaindernear 1 1234567891    -> 1
rmnx809 remaindernear 12345678901 100 -> 1
rmnx810 remaindernear 1 12345678901   -> 1
rmnx811 remaindernear 1234567896  10  -> -4
rmnx812 remaindernear 1 1234567896    -> 1

precision: 15
rmnx841 remaindernear 12345678000 100 -> 0
rmnx842 remaindernear 1 12345678000   -> 1
rmnx843 remaindernear 1234567800  10  -> 0
rmnx844 remaindernear 1 1234567800    -> 1
rmnx845 remaindernear 1234567890  10  -> 0
rmnx846 remaindernear 1 1234567890    -> 1
rmnx847 remaindernear 1234567891  10  -> 1
rmnx848 remaindernear 1 1234567891    -> 1
rmnx849 remaindernear 12345678901 100 -> 1
rmnx850 remaindernear 1 12345678901   -> 1
rmnx851 remaindernear 1234567896  10  -> -4
rmnx852 remaindernear 1 1234567896    -> 1

-- Null tests
rmnx900 remaindernear 10  # -> NaN Invalid_operation
rmnx901 remaindernear  # 10 -> NaN Invalid_operation
