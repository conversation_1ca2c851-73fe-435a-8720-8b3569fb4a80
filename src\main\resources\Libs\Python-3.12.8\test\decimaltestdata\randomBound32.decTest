------------------------------------------------------------------------
-- randomBound32.decTest -- decimal testcases -- boundaries near 32   --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- These testcases test calculations at precisions 31, 32, and 33, to
-- exercise the boundaries around 2**5

-- randomly generated testcases [26 Sep 2001]
extended:    1
precision:   31
rounding:    half_up
maxExponent: 9999
minexponent: -9999

addx3001 add 4953734675913.065314738743322579 0218.932010396534371704930714860E+797 -> 2.189320103965343717049307148600E+799 Inexact Rounded
comx3001 compare 4953734675913.065314738743322579 0218.932010396534371704930714860E+797 -> -1
divx3001 divide 4953734675913.065314738743322579 0218.932010396534371704930714860E+797 -> 2.262681764507965005284080800438E-787 Inexact Rounded
dvix3001 divideint 4953734675913.065314738743322579 0218.932010396534371704930714860E+797 -> 0
mulx3001 multiply 4953734675913.065314738743322579 0218.932010396534371704930714860E+797 -> 1.084531091568672041923151632066E+812 Inexact Rounded
powx3001 power 4953734675913.065314738743322579 2 -> 24539487239343522246155890.99495 Inexact Rounded
remx3001 remainder 4953734675913.065314738743322579 0218.932010396534371704930714860E+797 -> 4953734675913.065314738743322579
subx3001 subtract 4953734675913.065314738743322579 0218.932010396534371704930714860E+797 -> -2.189320103965343717049307148600E+799 Inexact Rounded
addx3002 add 9641.684323386955881595490347910E-844 -78864532047.12287484430980636798E+934 -> -7.886453204712287484430980636798E+944 Inexact Rounded
comx3002 compare 9641.684323386955881595490347910E-844 -78864532047.12287484430980636798E+934 -> 1
divx3002 divide 9641.684323386955881595490347910E-844 -78864532047.12287484430980636798E+934 -> -1.222562801441069667849402782716E-1785 Inexact Rounded
dvix3002 divideint 9641.684323386955881595490347910E-844 -78864532047.12287484430980636798E+934 -> -0
mulx3002 multiply 9641.684323386955881595490347910E-844 -78864532047.12287484430980636798E+934 -> -7.603869223099928141659831589905E+104 Inexact Rounded
powx3002 power 9641.684323386955881595490347910E-844 -8 -> 1.338988152067180337738955757587E+6720 Inexact Rounded
remx3002 remainder 9641.684323386955881595490347910E-844 -78864532047.12287484430980636798E+934 -> 9.641684323386955881595490347910E-841
subx3002 subtract 9641.684323386955881595490347910E-844 -78864532047.12287484430980636798E+934 -> 7.886453204712287484430980636798E+944 Inexact Rounded
addx3003 add -1.028048571628326871054964307774E+529 49200008645699.35577937582714739 -> -1.028048571628326871054964307774E+529 Inexact Rounded
comx3003 compare -1.028048571628326871054964307774E+529 49200008645699.35577937582714739 -> -1
divx3003 divide -1.028048571628326871054964307774E+529 49200008645699.35577937582714739 -> -2.089529249946971482861843692465E+515 Inexact Rounded
dvix3003 divideint -1.028048571628326871054964307774E+529 49200008645699.35577937582714739 -> NaN Division_impossible
mulx3003 multiply -1.028048571628326871054964307774E+529 49200008645699.35577937582714739 -> -5.057999861231255549283737861207E+542 Inexact Rounded
powx3003 power -1.028048571628326871054964307774E+529 5 -> -1.148333858253704284232780819739E+2645 Inexact Rounded
remx3003 remainder -1.028048571628326871054964307774E+529 49200008645699.35577937582714739 -> NaN Division_impossible
subx3003 subtract -1.028048571628326871054964307774E+529 49200008645699.35577937582714739 -> -1.028048571628326871054964307774E+529 Inexact Rounded
addx3004 add 479084.8561808930525417735205519 084157571054.2691784660983989931 -> 84158050139.12535935915094076662 Inexact Rounded
comx3004 compare 479084.8561808930525417735205519 084157571054.2691784660983989931 -> -1
divx3004 divide 479084.8561808930525417735205519 084157571054.2691784660983989931 -> 0.000005692712493709617905493710207969 Inexact Rounded
dvix3004 divideint 479084.8561808930525417735205519 084157571054.2691784660983989931 -> 0
mulx3004 multiply 479084.8561808930525417735205519 084157571054.2691784660983989931 -> 40318617825067837.47317700523687 Inexact Rounded
powx3004 power 479084.8561808930525417735205519 8 -> 2.775233598021235973545933045837E+45 Inexact Rounded
remx3004 remainder 479084.8561808930525417735205519 084157571054.2691784660983989931 -> 479084.8561808930525417735205519
subx3004 subtract 479084.8561808930525417735205519 084157571054.2691784660983989931 -> -84157091969.41299757304585721958 Inexact Rounded
addx3005 add -0363750788.573782205664349562931 -3172.080934464133691909905980096 -> -363753960.6547166697980414728370 Inexact Rounded
comx3005 compare -0363750788.573782205664349562931 -3172.080934464133691909905980096 -> -1
divx3005 divide -0363750788.573782205664349562931 -3172.080934464133691909905980096 -> 114672.6064337420167096295290890 Inexact Rounded
dvix3005 divideint -0363750788.573782205664349562931 -3172.080934464133691909905980096 -> 114672
mulx3005 multiply -0363750788.573782205664349562931 -3172.080934464133691909905980096 -> 1153846941331.188583292239230818 Inexact Rounded
powx3005 power -0363750788.573782205664349562931 -3172 -> 0E-10029 Underflow Subnormal Inexact Rounded Clamped
remx3005 remainder -0363750788.573782205664349562931 -3172.080934464133691909905980096 -> -1923.656911066945656824381431488
subx3005 subtract -0363750788.573782205664349562931 -3172.080934464133691909905980096 -> -363747616.4928477415306576530250 Inexact Rounded
addx3006 add 1381026551423669919010191878449 -82.66614775445371254999357800739 -> 1381026551423669919010191878366 Inexact Rounded
comx3006 compare 1381026551423669919010191878449 -82.66614775445371254999357800739 -> 1
divx3006 divide 1381026551423669919010191878449 -82.66614775445371254999357800739 -> -16706071214613552377376639557.90 Inexact Rounded
dvix3006 divideint 1381026551423669919010191878449 -82.66614775445371254999357800739 -> -16706071214613552377376639557
mulx3006 multiply 1381026551423669919010191878449 -82.66614775445371254999357800739 -> -1.141641449528127656560770057228E+32 Inexact Rounded
powx3006 power 1381026551423669919010191878449 -83 -> 2.307977908106564299925193011052E-2502 Inexact Rounded
remx3006 remainder 1381026551423669919010191878449 -82.66614775445371254999357800739 -> 74.22115953553602036042168767377
subx3006 subtract 1381026551423669919010191878449 -82.66614775445371254999357800739 -> 1381026551423669919010191878532 Inexact Rounded
addx3007 add 4627.026960423072127953556635585 -4410583132901.830017479741231131 -> -4410583128274.803057056669103177 Inexact Rounded
comx3007 compare 4627.026960423072127953556635585 -4410583132901.830017479741231131 -> 1
divx3007 divide 4627.026960423072127953556635585 -4410583132901.830017479741231131 -> -1.049073743992404570569003129346E-9 Inexact Rounded
dvix3007 divideint 4627.026960423072127953556635585 -4410583132901.830017479741231131 -> -0
mulx3007 multiply 4627.026960423072127953556635585 -4410583132901.830017479741231131 -> -20407887067124025.31576887565113 Inexact Rounded
powx3007 power 4627.026960423072127953556635585 -4 -> 2.181684167222334934221407781701E-15 Inexact Rounded
remx3007 remainder 4627.026960423072127953556635585 -4410583132901.830017479741231131 -> 4627.026960423072127953556635585
subx3007 subtract 4627.026960423072127953556635585 -4410583132901.830017479741231131 -> 4410583137528.856977902813359085 Inexact Rounded
addx3008 add 75353574493.84484153484918212042 -8684111695095849922263044191221 -> -8684111695095849922187690616727 Inexact Rounded
comx3008 compare 75353574493.84484153484918212042 -8684111695095849922263044191221 -> 1
divx3008 divide 75353574493.84484153484918212042 -8684111695095849922263044191221 -> -8.677177026223536475531592432118E-21 Inexact Rounded
dvix3008 divideint 75353574493.84484153484918212042 -8684111695095849922263044191221 -> -0
mulx3008 multiply 75353574493.84484153484918212042 -8684111695095849922263044191221 -> -6.543788575292743281456830701127E+41 Inexact Rounded
powx3008 power 75353574493.84484153484918212042 -9 -> 1.276630670287906925570645490707E-98 Inexact Rounded
remx3008 remainder 75353574493.84484153484918212042 -8684111695095849922263044191221 -> 75353574493.84484153484918212042
subx3008 subtract 75353574493.84484153484918212042 -8684111695095849922263044191221 -> 8684111695095849922338397765715 Inexact Rounded
addx3009 add 6907058.216435355874729592373011 2.857005446917670515662398741545 -> 6907061.073440802792400108035410 Inexact Rounded
comx3009 compare 6907058.216435355874729592373011 2.857005446917670515662398741545 -> 1
divx3009 divide 6907058.216435355874729592373011 2.857005446917670515662398741545 -> 2417586.646146283856436864121104 Inexact Rounded
dvix3009 divideint 6907058.216435355874729592373011 2.857005446917670515662398741545 -> 2417586
mulx3009 multiply 6907058.216435355874729592373011 2.857005446917670515662398741545 -> 19733502.94653326211623698034717 Inexact Rounded
powx3009 power 6907058.216435355874729592373011 3 -> 329518156646369505494.8971353240 Inexact Rounded
remx3009 remainder 6907058.216435355874729592373011 2.857005446917670515662398741545 -> 1.846043452483451396449034189630
subx3009 subtract 6907058.216435355874729592373011 2.857005446917670515662398741545 -> 6907055.359429908957059076710612 Inexact Rounded
addx3010 add -38949530427253.24030680468677190 712168021.1265384466442576619064E-992 -> -38949530427253.24030680468677190 Inexact Rounded
comx3010 compare -38949530427253.24030680468677190 712168021.1265384466442576619064E-992 -> -1
divx3010 divide -38949530427253.24030680468677190 712168021.1265384466442576619064E-992 -> -5.469149031100999700489221122509E+996 Inexact Rounded
dvix3010 divideint -38949530427253.24030680468677190 712168021.1265384466442576619064E-992 -> NaN Division_impossible
mulx3010 multiply -38949530427253.24030680468677190 712168021.1265384466442576619064E-992 -> -2.773861000818483769292240109417E-970 Inexact Rounded
powx3010 power -38949530427253.24030680468677190 7 -> -1.359926959823071332599817363877E+95 Inexact Rounded
remx3010 remainder -38949530427253.24030680468677190 712168021.1265384466442576619064E-992 -> NaN Division_impossible
subx3010 subtract -38949530427253.24030680468677190 712168021.1265384466442576619064E-992 -> -38949530427253.24030680468677190 Inexact Rounded
addx3011 add -0708069.025667471996378081482549 -562842.4701520787831018732202804 -> -1270911.495819550779479954702829 Inexact Rounded
comx3011 compare -0708069.025667471996378081482549 -562842.4701520787831018732202804 -> -1
divx3011 divide -0708069.025667471996378081482549 -562842.4701520787831018732202804 -> 1.258023449218665608349145394069 Inexact Rounded
dvix3011 divideint -0708069.025667471996378081482549 -562842.4701520787831018732202804 -> 1
mulx3011 multiply -0708069.025667471996378081482549 -562842.4701520787831018732202804 -> 398531319444.8556128729086112205 Inexact Rounded
powx3011 power -0708069.025667471996378081482549 -562842 -> 0E-10029 Underflow Subnormal Inexact Rounded Clamped
remx3011 remainder -0708069.025667471996378081482549 -562842.4701520787831018732202804 -> -145226.5555153932132762082622686
subx3011 subtract -0708069.025667471996378081482549 -562842.4701520787831018732202804 -> -145226.5555153932132762082622686
addx3012 add 4055087.246994644709729942673976 -43183146921897.67383476104084575E+211 -> -4.318314692189767383476104084575E+224 Inexact Rounded
comx3012 compare 4055087.246994644709729942673976 -43183146921897.67383476104084575E+211 -> 1
divx3012 divide 4055087.246994644709729942673976 -43183146921897.67383476104084575E+211 -> -9.390439409913307906923909630247E-219 Inexact Rounded
dvix3012 divideint 4055087.246994644709729942673976 -43183146921897.67383476104084575E+211 -> -0
mulx3012 multiply 4055087.246994644709729942673976 -43183146921897.67383476104084575E+211 -> -1.751114283680833039197637874453E+231 Inexact Rounded
powx3012 power 4055087.246994644709729942673976 -4 -> 3.698274893849241116195795515302E-27 Inexact Rounded
remx3012 remainder 4055087.246994644709729942673976 -43183146921897.67383476104084575E+211 -> 4055087.246994644709729942673976
subx3012 subtract 4055087.246994644709729942673976 -43183146921897.67383476104084575E+211 -> 4.318314692189767383476104084575E+224 Inexact Rounded
addx3013 add 4502895892520.396581348110906909E-512 -815.9047305921862348263521876034 -> -815.9047305921862348263521876034 Inexact Rounded
comx3013 compare 4502895892520.396581348110906909E-512 -815.9047305921862348263521876034 -> 1
divx3013 divide 4502895892520.396581348110906909E-512 -815.9047305921862348263521876034 -> -5.518899111238367862234798433551E-503 Inexact Rounded
dvix3013 divideint 4502895892520.396581348110906909E-512 -815.9047305921862348263521876034 -> -0
mulx3013 multiply 4502895892520.396581348110906909E-512 -815.9047305921862348263521876034 -> -3.673934060071516156604453756541E-497 Inexact Rounded
powx3013 power 4502895892520.396581348110906909E-512 -816 -> Infinity Overflow Inexact Rounded
remx3013 remainder 4502895892520.396581348110906909E-512 -815.9047305921862348263521876034 -> 4.502895892520396581348110906909E-500
subx3013 subtract 4502895892520.396581348110906909E-512 -815.9047305921862348263521876034 -> 815.9047305921862348263521876034 Inexact Rounded
addx3014 add 467.6721295072628100260239179865 -02.07155073395573569852316073025 -> 465.6005787733070743275007572563 Inexact Rounded
comx3014 compare 467.6721295072628100260239179865 -02.07155073395573569852316073025 -> 1
divx3014 divide 467.6721295072628100260239179865 -02.07155073395573569852316073025 -> -225.7594380101027705997496045999 Inexact Rounded
dvix3014 divideint 467.6721295072628100260239179865 -02.07155073395573569852316073025 -> -225
mulx3014 multiply 467.6721295072628100260239179865 -02.07155073395573569852316073025 -> -968.8065431314121523074875069807 Inexact Rounded
powx3014 power 467.6721295072628100260239179865 -2 -> 0.000004572113694193221810609836080931 Inexact Rounded
remx3014 remainder 467.6721295072628100260239179865 -02.07155073395573569852316073025 -> 1.57321436722227785831275368025
subx3014 subtract 467.6721295072628100260239179865 -02.07155073395573569852316073025 -> 469.7436802412185457245470787168 Inexact Rounded
addx3015 add 2.156795313311150143949997552501E-571 -8677147.586389401682712180146855 -> -8677147.586389401682712180146855 Inexact Rounded
comx3015 compare 2.156795313311150143949997552501E-571 -8677147.586389401682712180146855 -> 1
divx3015 divide 2.156795313311150143949997552501E-571 -8677147.586389401682712180146855 -> -2.485604044230163799604243529005E-578 Inexact Rounded
dvix3015 divideint 2.156795313311150143949997552501E-571 -8677147.586389401682712180146855 -> -0
mulx3015 multiply 2.156795313311150143949997552501E-571 -8677147.586389401682712180146855 -> -1.871483124723381986272837942577E-564 Inexact Rounded
powx3015 power 2.156795313311150143949997552501E-571 -8677148 -> Infinity Overflow Inexact Rounded
remx3015 remainder 2.156795313311150143949997552501E-571 -8677147.586389401682712180146855 -> 2.156795313311150143949997552501E-571
subx3015 subtract 2.156795313311150143949997552501E-571 -8677147.586389401682712180146855 -> 8677147.586389401682712180146855 Inexact Rounded
addx3016 add -974953.2801637208368002585822457 -693095793.3667578067802698191246 -> -694070746.6469215276170700777068 Inexact Rounded
comx3016 compare -974953.2801637208368002585822457 -693095793.3667578067802698191246 -> 1
divx3016 divide -974953.2801637208368002585822457 -693095793.3667578067802698191246 -> 0.001406664546942092941961075608769 Inexact Rounded
dvix3016 divideint -974953.2801637208368002585822457 -693095793.3667578067802698191246 -> 0
mulx3016 multiply -974953.2801637208368002585822457 -693095793.3667578067802698191246 -> 675736017210596.9899587749991363 Inexact Rounded
powx3016 power -974953.2801637208368002585822457 -693095793 -> -0E-10029 Underflow Subnormal Inexact Rounded Clamped
remx3016 remainder -974953.2801637208368002585822457 -693095793.3667578067802698191246 -> -974953.2801637208368002585822457
subx3016 subtract -974953.2801637208368002585822457 -693095793.3667578067802698191246 -> 692120840.0865940859434695605424 Inexact Rounded
addx3017 add -7634680140009571846155654339781 3009630949502.035852433434214413E-490 -> -7634680140009571846155654339781 Inexact Rounded
comx3017 compare -7634680140009571846155654339781 3009630949502.035852433434214413E-490 -> -1
divx3017 divide -7634680140009571846155654339781 3009630949502.035852433434214413E-490 -> -2.536749610869326753741024659948E+508 Inexact Rounded
dvix3017 divideint -7634680140009571846155654339781 3009630949502.035852433434214413E-490 -> NaN Division_impossible
mulx3017 multiply -7634680140009571846155654339781 3009630949502.035852433434214413E-490 -> -2.297756963892134373657544025107E-447 Inexact Rounded
powx3017 power -7634680140009571846155654339781 3 -> -4.450128382072157170207584847831E+92 Inexact Rounded
remx3017 remainder -7634680140009571846155654339781 3009630949502.035852433434214413E-490 -> NaN Division_impossible
subx3017 subtract -7634680140009571846155654339781 3009630949502.035852433434214413E-490 -> -7634680140009571846155654339781 Inexact Rounded
addx3018 add 262273.0222851186523650889896428E-624 74177.21073338090843145838835480 -> 74177.21073338090843145838835480 Inexact Rounded
comx3018 compare 262273.0222851186523650889896428E-624 74177.21073338090843145838835480 -> -1
divx3018 divide 262273.0222851186523650889896428E-624 74177.21073338090843145838835480 -> 3.535762799545274329358292065343E-624 Inexact Rounded
dvix3018 divideint 262273.0222851186523650889896428E-624 74177.21073338090843145838835480 -> 0
mulx3018 multiply 262273.0222851186523650889896428E-624 74177.21073338090843145838835480 -> 1.945468124372395349192665031675E-614 Inexact Rounded
powx3018 power 262273.0222851186523650889896428E-624 74177 -> 0E-10029 Underflow Subnormal Inexact Rounded Clamped
remx3018 remainder 262273.0222851186523650889896428E-624 74177.21073338090843145838835480 -> 2.622730222851186523650889896428E-619
subx3018 subtract 262273.0222851186523650889896428E-624 74177.21073338090843145838835480 -> -74177.21073338090843145838835480 Inexact Rounded
addx3019 add -8036052748815903177624716581732 -066677357.4438809548850966167573 -> -8036052748815903177624783259089 Inexact Rounded
comx3019 compare -8036052748815903177624716581732 -066677357.4438809548850966167573 -> -1
divx3019 divide -8036052748815903177624716581732 -066677357.4438809548850966167573 -> 120521464210387351732732.6271469 Inexact Rounded
dvix3019 divideint -8036052748815903177624716581732 -066677357.4438809548850966167573 -> 120521464210387351732732
mulx3019 multiply -8036052748815903177624716581732 -066677357.4438809548850966167573 -> 5.358227615706800711033262124598E+38 Inexact Rounded
powx3019 power -8036052748815903177624716581732 -66677357 -> -0E-10029 Underflow Subnormal Inexact Rounded Clamped
remx3019 remainder -8036052748815903177624716581732 -066677357.4438809548850966167573 -> -41816499.5048993028288978900564
subx3019 subtract -8036052748815903177624716581732 -066677357.4438809548850966167573 -> -8036052748815903177624649904375 Inexact Rounded
addx3020 add 883429.5928031498103637713570166E+765 -43978.97283712939198111043032726 -> 8.834295928031498103637713570166E+770 Inexact Rounded
comx3020 compare 883429.5928031498103637713570166E+765 -43978.97283712939198111043032726 -> 1
divx3020 divide 883429.5928031498103637713570166E+765 -43978.97283712939198111043032726 -> -2.008754492913739633208672455025E+766 Inexact Rounded
dvix3020 divideint 883429.5928031498103637713570166E+765 -43978.97283712939198111043032726 -> NaN Division_impossible
mulx3020 multiply 883429.5928031498103637713570166E+765 -43978.97283712939198111043032726 -> -3.885232606540600490321438191516E+775 Inexact Rounded
powx3020 power 883429.5928031498103637713570166E+765 -43979 -> 0E-10029 Underflow Subnormal Inexact Rounded Clamped
remx3020 remainder 883429.5928031498103637713570166E+765 -43978.97283712939198111043032726 -> NaN Division_impossible
subx3020 subtract 883429.5928031498103637713570166E+765 -43978.97283712939198111043032726 -> 8.834295928031498103637713570166E+770 Inexact Rounded
addx3021 add 24791301060.37938360567775506973 -5613327866480.322649080205877564 -> -5588536565419.943265474528122494 Inexact Rounded
comx3021 compare 24791301060.37938360567775506973 -5613327866480.322649080205877564 -> 1
divx3021 divide 24791301060.37938360567775506973 -5613327866480.322649080205877564 -> -0.004416506865458415275182120038399 Inexact Rounded
dvix3021 divideint 24791301060.37938360567775506973 -5613327866480.322649080205877564 -> -0
mulx3021 multiply 24791301060.37938360567775506973 -5613327866480.322649080205877564 -> -139161701088530765925120.8408852 Inexact Rounded
powx3021 power 24791301060.37938360567775506973 -6 -> 4.307289712375673028996126249656E-63 Inexact Rounded
remx3021 remainder 24791301060.37938360567775506973 -5613327866480.322649080205877564 -> 24791301060.37938360567775506973
subx3021 subtract 24791301060.37938360567775506973 -5613327866480.322649080205877564 -> 5638119167540.702032685883632634 Inexact Rounded
addx3022 add -930711443.9474781586162910776139 -740.3860979292775472622798348030 -> -930712184.3335760878938383398937 Inexact Rounded
comx3022 compare -930711443.9474781586162910776139 -740.3860979292775472622798348030 -> -1
divx3022 divide -930711443.9474781586162910776139 -740.3860979292775472622798348030 -> 1257062.290270583507131602958799 Inexact Rounded
dvix3022 divideint -930711443.9474781586162910776139 -740.3860979292775472622798348030 -> 1257062
mulx3022 multiply -930711443.9474781586162910776139 -740.3860979292775472622798348030 -> 689085814282.3968746911100154133 Inexact Rounded
powx3022 power -930711443.9474781586162910776139 -740 -> 1.193603394165051899997226995178E-6637 Inexact Rounded
remx3022 remainder -930711443.9474781586162910776139 -740.3860979292775472622798348030 -> -214.9123046664996750639167712140
subx3022 subtract -930711443.9474781586162910776139 -740.3860979292775472622798348030 -> -930710703.5613802293387438153341 Inexact Rounded
addx3023 add 2358276428765.064191082773385539 214.3589796082328665878602304469 -> 2358276428979.423170691006252127 Inexact Rounded
comx3023 compare 2358276428765.064191082773385539 214.3589796082328665878602304469 -> 1
divx3023 divide 2358276428765.064191082773385539 214.3589796082328665878602304469 -> 11001528525.07089502152736489473 Inexact Rounded
dvix3023 divideint 2358276428765.064191082773385539 214.3589796082328665878602304469 -> 11001528525
mulx3023 multiply 2358276428765.064191082773385539 214.3589796082328665878602304469 -> 505517728904226.6233443209659001 Inexact Rounded
powx3023 power 2358276428765.064191082773385539 214 -> 5.435856480782850080741276939256E+2647 Inexact Rounded
remx3023 remainder 2358276428765.064191082773385539 214.3589796082328665878602304469 -> 15.1969844739096415643561521775
subx3023 subtract 2358276428765.064191082773385539 214.3589796082328665878602304469 -> 2358276428550.705211474540518951 Inexact Rounded
addx3024 add -3.868744449795653651638308926987E+750 8270.472492965559872384018329418 -> -3.868744449795653651638308926987E+750 Inexact Rounded
comx3024 compare -3.868744449795653651638308926987E+750 8270.472492965559872384018329418 -> -1
divx3024 divide -3.868744449795653651638308926987E+750 8270.472492965559872384018329418 -> -4.677779235812959233092739433453E+746 Inexact Rounded
dvix3024 divideint -3.868744449795653651638308926987E+750 8270.472492965559872384018329418 -> NaN Division_impossible
mulx3024 multiply -3.868744449795653651638308926987E+750 8270.472492965559872384018329418 -> -3.199634455434813294426505526063E+754 Inexact Rounded
powx3024 power -3.868744449795653651638308926987E+750 8270 -> Infinity Overflow Inexact Rounded
remx3024 remainder -3.868744449795653651638308926987E+750 8270.472492965559872384018329418 -> NaN Division_impossible
subx3024 subtract -3.868744449795653651638308926987E+750 8270.472492965559872384018329418 -> -3.868744449795653651638308926987E+750 Inexact Rounded
addx3025 add 140422069.5863246490180206814374E-447 -567195652586.2454217069003186487 -> -567195652586.2454217069003186487 Inexact Rounded
comx3025 compare 140422069.5863246490180206814374E-447 -567195652586.2454217069003186487 -> 1
divx3025 divide 140422069.5863246490180206814374E-447 -567195652586.2454217069003186487 -> -2.475725421131866851190640203633E-451 Inexact Rounded
dvix3025 divideint 140422069.5863246490180206814374E-447 -567195652586.2454217069003186487 -> -0
mulx3025 multiply 140422069.5863246490180206814374E-447 -567195652586.2454217069003186487 -> -7.964678739652657498503799559950E-428 Inexact Rounded
powx3025 power 140422069.5863246490180206814374E-447 -6 -> 1.304330899731988395473578425854E+2633 Inexact Rounded
remx3025 remainder 140422069.5863246490180206814374E-447 -567195652586.2454217069003186487 -> 1.404220695863246490180206814374E-439
subx3025 subtract 140422069.5863246490180206814374E-447 -567195652586.2454217069003186487 -> 567195652586.2454217069003186487 Inexact Rounded
addx3026 add 75929096475.63450425339472559646E+153 -0945260193.503803519572604151290E+459 -> -9.452601935038035195726041512900E+467 Inexact Rounded
comx3026 compare 75929096475.63450425339472559646E+153 -0945260193.503803519572604151290E+459 -> 1
divx3026 divide 75929096475.63450425339472559646E+153 -0945260193.503803519572604151290E+459 -> -8.032613347885465805613265604973E-305 Inexact Rounded
dvix3026 divideint 75929096475.63450425339472559646E+153 -0945260193.503803519572604151290E+459 -> -0
mulx3026 multiply 75929096475.63450425339472559646E+153 -0945260193.503803519572604151290E+459 -> -7.177275242712723733041569606882E+631 Inexact Rounded
powx3026 power 75929096475.63450425339472559646E+153 -9 -> 1.192136299657177324051477375561E-1475 Inexact Rounded
remx3026 remainder 75929096475.63450425339472559646E+153 -0945260193.503803519572604151290E+459 -> 7.592909647563450425339472559646E+163
subx3026 subtract 75929096475.63450425339472559646E+153 -0945260193.503803519572604151290E+459 -> 9.452601935038035195726041512900E+467 Inexact Rounded
addx3027 add 6312318309.142044953357460463732 -5641317823.202274083982487558514E+628 -> -5.641317823202274083982487558514E+637 Inexact Rounded
comx3027 compare 6312318309.142044953357460463732 -5641317823.202274083982487558514E+628 -> 1
divx3027 divide 6312318309.142044953357460463732 -5641317823.202274083982487558514E+628 -> -1.118943925332481944765809682502E-628 Inexact Rounded
dvix3027 divideint 6312318309.142044953357460463732 -5641317823.202274083982487558514E+628 -> -0
mulx3027 multiply 6312318309.142044953357460463732 -5641317823.202274083982487558514E+628 -> -3.560979378308906043783023726787E+647 Inexact Rounded
powx3027 power 6312318309.142044953357460463732 -6 -> 1.580762611512787720076533747265E-59 Inexact Rounded
remx3027 remainder 6312318309.142044953357460463732 -5641317823.202274083982487558514E+628 -> 6312318309.142044953357460463732
subx3027 subtract 6312318309.142044953357460463732 -5641317823.202274083982487558514E+628 -> 5.641317823202274083982487558514E+637 Inexact Rounded
addx3028 add 93793652428100.52105928239469937 917.2571313109730433369594936416E-712 -> 93793652428100.52105928239469937 Inexact Rounded
comx3028 compare 93793652428100.52105928239469937 917.2571313109730433369594936416E-712 -> 1
divx3028 divide 93793652428100.52105928239469937 917.2571313109730433369594936416E-712 -> 1.022544815694674972559924997256E+723 Inexact Rounded
dvix3028 divideint 93793652428100.52105928239469937 917.2571313109730433369594936416E-712 -> NaN Division_impossible
mulx3028 multiply 93793652428100.52105928239469937 917.2571313109730433369594936416E-712 -> 8.603289656137796526769786965341E-696 Inexact Rounded
powx3028 power 93793652428100.52105928239469937 9 -> 5.617732206663136654187263964365E+125 Inexact Rounded
remx3028 remainder 93793652428100.52105928239469937 917.2571313109730433369594936416E-712 -> NaN Division_impossible
subx3028 subtract 93793652428100.52105928239469937 917.2571313109730433369594936416E-712 -> 93793652428100.52105928239469937 Inexact Rounded
addx3029 add 98471198160.56524417578665886060 -23994.14313393939743548945165462 -> 98471174166.42211023638922337115 Inexact Rounded
comx3029 compare 98471198160.56524417578665886060 -23994.14313393939743548945165462 -> 1
divx3029 divide 98471198160.56524417578665886060 -23994.14313393939743548945165462 -> -4103968.106336710126241266685434 Inexact Rounded
dvix3029 divideint 98471198160.56524417578665886060 -23994.14313393939743548945165462 -> -4103968
mulx3029 multiply 98471198160.56524417578665886060 -23994.14313393939743548945165462 -> -2362732023235112.375960528304974 Inexact Rounded
powx3029 power 98471198160.56524417578665886060 -23994 -> 0E-10029 Underflow Subnormal Inexact Rounded Clamped
remx3029 remainder 98471198160.56524417578665886060 -23994.14313393939743548945165462 -> 2551.45824316125588493249246784
subx3029 subtract 98471198160.56524417578665886060 -23994.14313393939743548945165462 -> 98471222154.70837811518409435005 Inexact Rounded
addx3030 add 329326552.0208398002250836592043 -02451.10065397010591546041034041 -> 329324100.9201858301191681987940 Inexact Rounded
comx3030 compare 329326552.0208398002250836592043 -02451.10065397010591546041034041 -> 1
divx3030 divide 329326552.0208398002250836592043 -02451.10065397010591546041034041 -> -134358.6406732917173739187421978 Inexact Rounded
dvix3030 divideint 329326552.0208398002250836592043 -02451.10065397010591546041034041 -> -134358
mulx3030 multiply 329326552.0208398002250836592043 -02451.10065397010591546041034041 -> -807212527028.0005401736893474430 Inexact Rounded
powx3030 power 329326552.0208398002250836592043 -2451 -> 0E-10029 Underflow Subnormal Inexact Rounded Clamped
remx3030 remainder 329326552.0208398002250836592043 -02451.10065397010591546041034041 -> 1570.35472430963565384668749322
subx3030 subtract 329326552.0208398002250836592043 -02451.10065397010591546041034041 -> 329329003.1214937703309991196146 Inexact Rounded
addx3031 add -92980.68431371090354435763218439 -2282178507046019721925800997065 -> -2282178507046019721925801090046 Inexact Rounded
comx3031 compare -92980.68431371090354435763218439 -2282178507046019721925800997065 -> 1
divx3031 divide -92980.68431371090354435763218439 -2282178507046019721925800997065 -> 4.074207342968196863070496994457E-26 Inexact Rounded
dvix3031 divideint -92980.68431371090354435763218439 -2282178507046019721925800997065 -> 0
mulx3031 multiply -92980.68431371090354435763218439 -2282178507046019721925800997065 -> 2.121985193111820147170707717938E+35 Inexact Rounded
powx3031 power -92980.68431371090354435763218439 -2 -> 1.156683455371909793870207184337E-10 Inexact Rounded
remx3031 remainder -92980.68431371090354435763218439 -2282178507046019721925800997065 -> -92980.68431371090354435763218439
subx3031 subtract -92980.68431371090354435763218439 -2282178507046019721925800997065 -> 2282178507046019721925800904084 Inexact Rounded
addx3032 add 12135817762.27858606259822256987E+738 98.35649167872356132249561021910E-902 -> 1.213581776227858606259822256987E+748 Inexact Rounded
comx3032 compare 12135817762.27858606259822256987E+738 98.35649167872356132249561021910E-902 -> 1
divx3032 divide 12135817762.27858606259822256987E+738 98.35649167872356132249561021910E-902 -> 1.233860374149945561886955398724E+1648 Inexact Rounded
dvix3032 divideint 12135817762.27858606259822256987E+738 98.35649167872356132249561021910E-902 -> NaN Division_impossible
mulx3032 multiply 12135817762.27858606259822256987E+738 98.35649167872356132249561021910E-902 -> 1.193636458750059340733188876015E-152 Inexact Rounded
powx3032 power 12135817762.27858606259822256987E+738 10 -> 6.929317520577437720457517499936E+7480 Inexact Rounded
remx3032 remainder 12135817762.27858606259822256987E+738 98.35649167872356132249561021910E-902 -> NaN Division_impossible
subx3032 subtract 12135817762.27858606259822256987E+738 98.35649167872356132249561021910E-902 -> 1.213581776227858606259822256987E+748 Inexact Rounded
addx3033 add 37.27457578793521166809739140081 -392550.4790095035979998355569916 -> -392513.2044337156627881674596002 Inexact Rounded
comx3033 compare 37.27457578793521166809739140081 -392550.4790095035979998355569916 -> 1
divx3033 divide 37.27457578793521166809739140081 -392550.4790095035979998355569916 -> -0.00009495486002714264641177211062199 Inexact Rounded
dvix3033 divideint 37.27457578793521166809739140081 -392550.4790095035979998355569916 -> -0
mulx3033 multiply 37.27457578793521166809739140081 -392550.4790095035979998355569916 -> -14632152.58043001234518095997140 Inexact Rounded
powx3033 power 37.27457578793521166809739140081 -392550 -> 0E-10029 Underflow Subnormal Inexact Rounded Clamped
remx3033 remainder 37.27457578793521166809739140081 -392550.4790095035979998355569916 -> 37.27457578793521166809739140081
subx3033 subtract 37.27457578793521166809739140081 -392550.4790095035979998355569916 -> 392587.7535852915332115036543830 Inexact Rounded
addx3034 add -2787.980590304199878755265273703 7117631179305319208210387565324 -> 7117631179305319208210387562536 Inexact Rounded
comx3034 compare -2787.980590304199878755265273703 7117631179305319208210387565324 -> -1
divx3034 divide -2787.980590304199878755265273703 7117631179305319208210387565324 -> -3.917006262435063093475140250870E-28 Inexact Rounded
dvix3034 divideint -2787.980590304199878755265273703 7117631179305319208210387565324 -> -0
mulx3034 multiply -2787.980590304199878755265273703 7117631179305319208210387565324 -> -1.984381757684722217801410305714E+34 Inexact Rounded
powx3034 power -2787.980590304199878755265273703 7 -> -1309266999233099220127139.440082 Inexact Rounded
remx3034 remainder -2787.980590304199878755265273703 7117631179305319208210387565324 -> -2787.980590304199878755265273703
subx3034 subtract -2787.980590304199878755265273703 7117631179305319208210387565324 -> -7117631179305319208210387568112 Inexact Rounded
addx3035 add -9890633.854609434943559831911276E+971 -1939985729.436827777055699361237 -> -9.890633854609434943559831911276E+977 Inexact Rounded
comx3035 compare -9890633.854609434943559831911276E+971 -1939985729.436827777055699361237 -> -1
divx3035 divide -9890633.854609434943559831911276E+971 -1939985729.436827777055699361237 -> 5.098302376420396260404821158158E+968 Inexact Rounded
dvix3035 divideint -9890633.854609434943559831911276E+971 -1939985729.436827777055699361237 -> NaN Division_impossible
mulx3035 multiply -9890633.854609434943559831911276E+971 -1939985729.436827777055699361237 -> 1.918768853302706825964087702307E+987 Inexact Rounded
powx3035 power -9890633.854609434943559831911276E+971 -2 -> 1.022237362667592867768511487814E-1956 Inexact Rounded
remx3035 remainder -9890633.854609434943559831911276E+971 -1939985729.436827777055699361237 -> NaN Division_impossible
subx3035 subtract -9890633.854609434943559831911276E+971 -1939985729.436827777055699361237 -> -9.890633854609434943559831911276E+977 Inexact Rounded
addx3036 add 3944570323.331121750661920475191 -17360722.28878145641394962484366 -> 3927209601.042340294247970850347 Inexact Rounded
comx3036 compare 3944570323.331121750661920475191 -17360722.28878145641394962484366 -> 1
divx3036 divide 3944570323.331121750661920475191 -17360722.28878145641394962484366 -> -227.2123393091837706827708196101 Inexact Rounded
dvix3036 divideint 3944570323.331121750661920475191 -17360722.28878145641394962484366 -> -227
mulx3036 multiply 3944570323.331121750661920475191 -17360722.28878145641394962484366 -> -68480589931920481.56020043213767 Inexact Rounded
powx3036 power 3944570323.331121750661920475191 -17360722 -> 0E-10029 Underflow Subnormal Inexact Rounded Clamped
remx3036 remainder 3944570323.331121750661920475191 -17360722.28878145641394962484366 -> 3686363.77773114469535563568018
subx3036 subtract 3944570323.331121750661920475191 -17360722.28878145641394962484366 -> 3961931045.619903207075870100035 Inexact Rounded
addx3037 add 19544.14018503427029002552872707 1786697762.885178994182133839546 -> 1786717307.025364028452423865075 Inexact Rounded
comx3037 compare 19544.14018503427029002552872707 1786697762.885178994182133839546 -> -1
divx3037 divide 19544.14018503427029002552872707 1786697762.885178994182133839546 -> 0.00001093869404832867759234359871991 Inexact Rounded
dvix3037 divideint 19544.14018503427029002552872707 1786697762.885178994182133839546 -> 0
mulx3037 multiply 19544.14018503427029002552872707 1786697762.885178994182133839546 -> 34919471546115.05897163496162290 Inexact Rounded
powx3037 power 19544.14018503427029002552872707 2 -> 381973415.5722714009298802557940 Inexact Rounded
remx3037 remainder 19544.14018503427029002552872707 1786697762.885178994182133839546 -> 19544.14018503427029002552872707
subx3037 subtract 19544.14018503427029002552872707 1786697762.885178994182133839546 -> -1786678218.744993959911843814017 Inexact Rounded
addx3038 add -05.75485957937617757983513662981 5564476875.989640431173694372083 -> 5564476870.234780851797516792248 Inexact Rounded
comx3038 compare -05.75485957937617757983513662981 5564476875.989640431173694372083 -> -1
divx3038 divide -05.75485957937617757983513662981 5564476875.989640431173694372083 -> -1.034213944568271324841608825136E-9 Inexact Rounded
dvix3038 divideint -05.75485957937617757983513662981 5564476875.989640431173694372083 -> -0
mulx3038 multiply -05.75485957937617757983513662981 5564476875.989640431173694372083 -> -32022783054.00620878436398990135 Inexact Rounded
powx3038 power -05.75485957937617757983513662981 6 -> 36325.23118223611421303238908472 Inexact Rounded
remx3038 remainder -05.75485957937617757983513662981 5564476875.989640431173694372083 -> -5.75485957937617757983513662981
subx3038 subtract -05.75485957937617757983513662981 5564476875.989640431173694372083 -> -5564476881.744500010549871951918 Inexact Rounded
addx3039 add -4208820.898718069194008526302746 626887.7553774705678201112845462E+206 -> 6.268877553774705678201112845462E+211 Inexact Rounded
comx3039 compare -4208820.898718069194008526302746 626887.7553774705678201112845462E+206 -> -1
divx3039 divide -4208820.898718069194008526302746 626887.7553774705678201112845462E+206 -> -6.713834913211527184907421856434E-206 Inexact Rounded
dvix3039 divideint -4208820.898718069194008526302746 626887.7553774705678201112845462E+206 -> -0
mulx3039 multiply -4208820.898718069194008526302746 626887.7553774705678201112845462E+206 -> -2.638458285983158789458925170267E+218 Inexact Rounded
powx3039 power -4208820.898718069194008526302746 6 -> 5.558564783291260359142223337994E+39 Inexact Rounded
remx3039 remainder -4208820.898718069194008526302746 626887.7553774705678201112845462E+206 -> -4208820.898718069194008526302746
subx3039 subtract -4208820.898718069194008526302746 626887.7553774705678201112845462E+206 -> -6.268877553774705678201112845462E+211 Inexact Rounded
addx3040 add -70077195478066.30896979085821269E+549 4607.163248554155483681430013073 -> -7.007719547806630896979085821269E+562 Inexact Rounded
comx3040 compare -70077195478066.30896979085821269E+549 4607.163248554155483681430013073 -> -1
divx3040 divide -70077195478066.30896979085821269E+549 4607.163248554155483681430013073 -> -1.521048673498997627360230078306E+559 Inexact Rounded
dvix3040 divideint -70077195478066.30896979085821269E+549 4607.163248554155483681430013073 -> NaN Division_impossible
mulx3040 multiply -70077195478066.30896979085821269E+549 4607.163248554155483681430013073 -> -3.228570795682925509478191397878E+566 Inexact Rounded
powx3040 power -70077195478066.30896979085821269E+549 4607 -> -Infinity Overflow Inexact Rounded
remx3040 remainder -70077195478066.30896979085821269E+549 4607.163248554155483681430013073 -> NaN Division_impossible
subx3040 subtract -70077195478066.30896979085821269E+549 4607.163248554155483681430013073 -> -7.007719547806630896979085821269E+562 Inexact Rounded
addx3041 add -442941.7541811527940918244383454 -068126768.0563559819156379151016 -> -68569709.81053713470972973953995 Inexact Rounded
comx3041 compare -442941.7541811527940918244383454 -068126768.0563559819156379151016 -> 1
divx3041 divide -442941.7541811527940918244383454 -068126768.0563559819156379151016 -> 0.006501728568934042143913111768557 Inexact Rounded
dvix3041 divideint -442941.7541811527940918244383454 -068126768.0563559819156379151016 -> 0
mulx3041 multiply -442941.7541811527940918244383454 -068126768.0563559819156379151016 -> 30176190149574.84386395947593970 Inexact Rounded
powx3041 power -442941.7541811527940918244383454 -68126768 -> 0E-10029 Underflow Subnormal Inexact Rounded Clamped
remx3041 remainder -442941.7541811527940918244383454 -068126768.0563559819156379151016 -> -442941.7541811527940918244383454
subx3041 subtract -442941.7541811527940918244383454 -068126768.0563559819156379151016 -> 67683826.30217482912154609066325 Inexact Rounded
addx3042 add -040726778711.8677615616711676159 299691.9430345259174614997064916 -> -40726479019.92472703575370611619 Inexact Rounded
comx3042 compare -040726778711.8677615616711676159 299691.9430345259174614997064916 -> -1
divx3042 divide -040726778711.8677615616711676159 299691.9430345259174614997064916 -> -135895.4741975690872548233111888 Inexact Rounded
dvix3042 divideint -040726778711.8677615616711676159 299691.9430345259174614997064916 -> -135895
mulx3042 multiply -040726778711.8677615616711676159 299691.9430345259174614997064916 -> -12205487445696816.02175665622242 Inexact Rounded
powx3042 power -040726778711.8677615616711676159 299692 -> Infinity Overflow Inexact Rounded
remx3042 remainder -040726778711.8677615616711676159 299691.9430345259174614997064916 -> -142113.1908620082406650022240180
subx3042 subtract -040726778711.8677615616711676159 299691.9430345259174614997064916 -> -40727078403.81079608758862911561 Inexact Rounded
addx3043 add -1934197520.738366912179143085955 3.810751422515178400293693371519 -> -1934197516.927615489663964685661 Inexact Rounded
comx3043 compare -1934197520.738366912179143085955 3.810751422515178400293693371519 -> -1
divx3043 divide -1934197520.738366912179143085955 3.810751422515178400293693371519 -> -507563287.7312566071537233697473 Inexact Rounded
dvix3043 divideint -1934197520.738366912179143085955 3.810751422515178400293693371519 -> -507563287
mulx3043 multiply -1934197520.738366912179143085955 3.810751422515178400293693371519 -> -7370745953.579062985130438309023 Inexact Rounded
powx3043 power -1934197520.738366912179143085955 4 -> 1.399597922275400947497855539475E+37 Inexact Rounded
remx3043 remainder -1934197520.738366912179143085955 3.810751422515178400293693371519 -> -2.786637155934674312936704177047
subx3043 subtract -1934197520.738366912179143085955 3.810751422515178400293693371519 -> -1934197524.549118334694321486249 Inexact Rounded
addx3044 add 813262.7723533833038829559646830 -303284822716.8282178131118185907 -> -303284009454.0558644298079356347 Inexact Rounded
comx3044 compare 813262.7723533833038829559646830 -303284822716.8282178131118185907 -> 1
divx3044 divide 813262.7723533833038829559646830 -303284822716.8282178131118185907 -> -0.000002681514904267770294213381485108 Inexact Rounded
dvix3044 divideint 813262.7723533833038829559646830 -303284822716.8282178131118185907 -> -0
mulx3044 multiply 813262.7723533833038829559646830 -303284822716.8282178131118185907 -> -246650255735392080.1357404280431 Inexact Rounded
powx3044 power 813262.7723533833038829559646830 -3 -> 1.859119568310997605545914895133E-18 Inexact Rounded
remx3044 remainder 813262.7723533833038829559646830 -303284822716.8282178131118185907 -> 813262.7723533833038829559646830
subx3044 subtract 813262.7723533833038829559646830 -303284822716.8282178131118185907 -> 303285635979.6005711964157015467 Inexact Rounded
addx3045 add 36105954884.94621434979365589311 745558205.7692397481313005659523E-952 -> 36105954884.94621434979365589311 Inexact Rounded
comx3045 compare 36105954884.94621434979365589311 745558205.7692397481313005659523E-952 -> 1
divx3045 divide 36105954884.94621434979365589311 745558205.7692397481313005659523E-952 -> 4.842808328786805821411674302686E+953 Inexact Rounded
dvix3045 divideint 36105954884.94621434979365589311 745558205.7692397481313005659523E-952 -> NaN Division_impossible
mulx3045 multiply 36105954884.94621434979365589311 745558205.7692397481313005659523E-952 -> 2.691909094160561673391352743869E-933 Inexact Rounded
powx3045 power 36105954884.94621434979365589311 7 -> 7.999297449713301719582732447386E+73 Inexact Rounded
remx3045 remainder 36105954884.94621434979365589311 745558205.7692397481313005659523E-952 -> NaN Division_impossible
subx3045 subtract 36105954884.94621434979365589311 745558205.7692397481313005659523E-952 -> 36105954884.94621434979365589311 Inexact Rounded
addx3046 add -075537177538.1814516621962185490 26980775255.51542856483122484898 -> -48556402282.66602309736499370002
comx3046 compare -075537177538.1814516621962185490 26980775255.51542856483122484898 -> -1
divx3046 divide -075537177538.1814516621962185490 26980775255.51542856483122484898 -> -2.799666682029089956269018541649 Inexact Rounded
dvix3046 divideint -075537177538.1814516621962185490 26980775255.51542856483122484898 -> -2
mulx3046 multiply -075537177538.1814516621962185490 26980775255.51542856483122484898 -> -2038051610593641947717.268652175 Inexact Rounded
powx3046 power -075537177538.1814516621962185490 3 -> -4.310049518987988084595264617727E+32 Inexact Rounded
remx3046 remainder -075537177538.1814516621962185490 26980775255.51542856483122484898 -> -21575627027.15059453253376885104
subx3046 subtract -075537177538.1814516621962185490 26980775255.51542856483122484898 -> -102517952793.6968802270274433980 Inexact Rounded
addx3047 add -4223765.415319564898840040697647 -2590590305497454185455459149918E-215 -> -4223765.415319564898840040697647 Inexact Rounded
comx3047 compare -4223765.415319564898840040697647 -2590590305497454185455459149918E-215 -> -1
divx3047 divide -4223765.415319564898840040697647 -2590590305497454185455459149918E-215 -> 1.630425855588347356570076909053E+191 Inexact Rounded
dvix3047 divideint -4223765.415319564898840040697647 -2590590305497454185455459149918E-215 -> NaN Division_impossible
mulx3047 multiply -4223765.415319564898840040697647 -2590590305497454185455459149918E-215 -> 1.094204573762229308798604845395E-178 Inexact Rounded
powx3047 power -4223765.415319564898840040697647 -3 -> -1.327090775863616939309569791138E-20 Inexact Rounded
remx3047 remainder -4223765.415319564898840040697647 -2590590305497454185455459149918E-215 -> NaN Division_impossible
subx3047 subtract -4223765.415319564898840040697647 -2590590305497454185455459149918E-215 -> -4223765.415319564898840040697647 Inexact Rounded
addx3048 add -6468.903738522951259063099946195 -7877.324314273694312164407794939E+267 -> -7.877324314273694312164407794939E+270 Inexact Rounded
comx3048 compare -6468.903738522951259063099946195 -7877.324314273694312164407794939E+267 -> 1
divx3048 divide -6468.903738522951259063099946195 -7877.324314273694312164407794939E+267 -> 8.212057140774706874666307246628E-268 Inexact Rounded
dvix3048 divideint -6468.903738522951259063099946195 -7877.324314273694312164407794939E+267 -> 0
mulx3048 multiply -6468.903738522951259063099946195 -7877.324314273694312164407794939E+267 -> 5.095765270616284455922747530676E+274 Inexact Rounded
powx3048 power -6468.903738522951259063099946195 -8 -> 3.261027724982089298030362367616E-31 Inexact Rounded
remx3048 remainder -6468.903738522951259063099946195 -7877.324314273694312164407794939E+267 -> -6468.903738522951259063099946195
subx3048 subtract -6468.903738522951259063099946195 -7877.324314273694312164407794939E+267 -> 7.877324314273694312164407794939E+270 Inexact Rounded
addx3049 add -9567221.183663236817239254783372E-203 1650.198961256061165362319471264 -> 1650.198961256061165362319471264 Inexact Rounded
comx3049 compare -9567221.183663236817239254783372E-203 1650.198961256061165362319471264 -> -1
divx3049 divide -9567221.183663236817239254783372E-203 1650.198961256061165362319471264 -> -5.797616777301250711985729776957E-200 Inexact Rounded
dvix3049 divideint -9567221.183663236817239254783372E-203 1650.198961256061165362319471264 -> -0
mulx3049 multiply -9567221.183663236817239254783372E-203 1650.198961256061165362319471264 -> -1.578781845938805737527304303976E-193 Inexact Rounded
powx3049 power -9567221.183663236817239254783372E-203 1650 -> 0E-10029 Underflow Subnormal Inexact Rounded Clamped
remx3049 remainder -9567221.183663236817239254783372E-203 1650.198961256061165362319471264 -> -9.567221183663236817239254783372E-197
subx3049 subtract -9567221.183663236817239254783372E-203 1650.198961256061165362319471264 -> -1650.198961256061165362319471264 Inexact Rounded
addx3050 add 8812306098770.200752139142033569E-428 26790.17380163975186972720427030E+568 -> 2.679017380163975186972720427030E+572 Inexact Rounded
comx3050 compare 8812306098770.200752139142033569E-428 26790.17380163975186972720427030E+568 -> -1
divx3050 divide 8812306098770.200752139142033569E-428 26790.17380163975186972720427030E+568 -> 3.289379965960065573444140749635E-988 Inexact Rounded
dvix3050 divideint 8812306098770.200752139142033569E-428 26790.17380163975186972720427030E+568 -> 0
mulx3050 multiply 8812306098770.200752139142033569E-428 26790.17380163975186972720427030E+568 -> 2.360832119793036398127652187732E+157 Inexact Rounded
powx3050 power 8812306098770.200752139142033569E-428 3 -> 6.843349527476967274129043949969E-1246 Inexact Rounded
remx3050 remainder 8812306098770.200752139142033569E-428 26790.17380163975186972720427030E+568 -> 8.812306098770200752139142033569E-416
subx3050 subtract 8812306098770.200752139142033569E-428 26790.17380163975186972720427030E+568 -> -2.679017380163975186972720427030E+572 Inexact Rounded
addx3051 add 80108033.12724838718736922500904 -706207255092.7645192310078892869 -> -706127147059.6372708438205200619 Inexact Rounded
comx3051 compare 80108033.12724838718736922500904 -706207255092.7645192310078892869 -> 1
divx3051 divide 80108033.12724838718736922500904 -706207255092.7645192310078892869 -> -0.0001134341690057060105325397863996 Inexact Rounded
dvix3051 divideint 80108033.12724838718736922500904 -706207255092.7645192310078892869 -> -0
mulx3051 multiply 80108033.12724838718736922500904 -706207255092.7645192310078892869 -> -56572874185674332398.36004114372 Inexact Rounded
powx3051 power 80108033.12724838718736922500904 -7 -> 4.723539145042336483008674060324E-56 Inexact Rounded
remx3051 remainder 80108033.12724838718736922500904 -706207255092.7645192310078892869 -> 80108033.12724838718736922500904
subx3051 subtract 80108033.12724838718736922500904 -706207255092.7645192310078892869 -> 706287363125.8917676181952585119 Inexact Rounded
addx3052 add -37942846282.76101663789059003505 -5.649456053942850351313869983197 -> -37942846288.41047269183344038636 Inexact Rounded
comx3052 compare -37942846282.76101663789059003505 -5.649456053942850351313869983197 -> -1
divx3052 divide -37942846282.76101663789059003505 -5.649456053942850351313869983197 -> 6716194607.139224735032566328960 Inexact Rounded
dvix3052 divideint -37942846282.76101663789059003505 -5.649456053942850351313869983197 -> 6716194607
mulx3052 multiply -37942846282.76101663789059003505 -5.649456053942850351313869983197 -> 214356442635.9672009449140933366 Inexact Rounded
powx3052 power -37942846282.76101663789059003505 -6 -> 3.351355986382646046773008753885E-64 Inexact Rounded
remx3052 remainder -37942846282.76101663789059003505 -5.649456053942850351313869983197 -> -0.786544022188321089603127981421
subx3052 subtract -37942846282.76101663789059003505 -5.649456053942850351313869983197 -> -37942846277.11156058394773968374 Inexact Rounded
addx3053 add 92659632115305.13735437728445541 6483438.317862851676468094261410E-139 -> 92659632115305.13735437728445541 Inexact Rounded
comx3053 compare 92659632115305.13735437728445541 6483438.317862851676468094261410E-139 -> 1
divx3053 divide 92659632115305.13735437728445541 6483438.317862851676468094261410E-139 -> 1.429174267919135710410529211791E+146 Inexact Rounded
dvix3053 divideint 92659632115305.13735437728445541 6483438.317862851676468094261410E-139 -> NaN Division_impossible
mulx3053 multiply 92659632115305.13735437728445541 6483438.317862851676468094261410E-139 -> 6.007530093754446085819255987878E-119 Inexact Rounded
powx3053 power 92659632115305.13735437728445541 6 -> 6.329121451953461546696051563323E+83 Inexact Rounded
remx3053 remainder 92659632115305.13735437728445541 6483438.317862851676468094261410E-139 -> NaN Division_impossible
subx3053 subtract 92659632115305.13735437728445541 6483438.317862851676468094261410E-139 -> 92659632115305.13735437728445541 Inexact Rounded
addx3054 add 2838948.589837595494152150647194 569547026247.5469563701415715960 -> 569549865196.1367939656357237466 Inexact Rounded
comx3054 compare 2838948.589837595494152150647194 569547026247.5469563701415715960 -> -1
divx3054 divide 2838948.589837595494152150647194 569547026247.5469563701415715960 -> 0.000004984572755198057481907281080406 Inexact Rounded
dvix3054 divideint 2838948.589837595494152150647194 569547026247.5469563701415715960 -> 0
mulx3054 multiply 2838948.589837595494152150647194 569547026247.5469563701415715960 -> 1616914727011669419.390959984273 Inexact Rounded
powx3054 power 2838948.589837595494152150647194 6 -> 5.235343334986059753096884080673E+38 Inexact Rounded
remx3054 remainder 2838948.589837595494152150647194 569547026247.5469563701415715960 -> 2838948.589837595494152150647194
subx3054 subtract 2838948.589837595494152150647194 569547026247.5469563701415715960 -> -569544187298.9571187746474194454 Inexact Rounded
addx3055 add 524995204523.6053307941775794287E+694 1589600879689517100527293028553 -> 5.249952045236053307941775794287E+705 Inexact Rounded
comx3055 compare 524995204523.6053307941775794287E+694 1589600879689517100527293028553 -> 1
divx3055 divide 524995204523.6053307941775794287E+694 1589600879689517100527293028553 -> 3.302685669286670708554753139233E+675 Inexact Rounded
dvix3055 divideint 524995204523.6053307941775794287E+694 1589600879689517100527293028553 -> NaN Division_impossible
mulx3055 multiply 524995204523.6053307941775794287E+694 1589600879689517100527293028553 -> 8.345328389435009812933599889447E+735 Inexact Rounded
powx3055 power 524995204523.6053307941775794287E+694 2 -> 2.756199647727821911857160230849E+1411 Inexact Rounded
remx3055 remainder 524995204523.6053307941775794287E+694 1589600879689517100527293028553 -> NaN Division_impossible
subx3055 subtract 524995204523.6053307941775794287E+694 1589600879689517100527293028553 -> 5.249952045236053307941775794287E+705 Inexact Rounded
addx3056 add -57131573677452.15449921725097290 4669681430736.326858508715643769 -> -52461892246715.82764070853532913 Inexact Rounded
comx3056 compare -57131573677452.15449921725097290 4669681430736.326858508715643769 -> -1
divx3056 divide -57131573677452.15449921725097290 4669681430736.326858508715643769 -> -12.23457628210057733643575143694 Inexact Rounded
dvix3056 divideint -57131573677452.15449921725097290 4669681430736.326858508715643769 -> -12
mulx3056 multiply -57131573677452.15449921725097290 4669681430736.326858508715643769 -> -266786248710342647746063322.0544 Inexact Rounded
powx3056 power -57131573677452.15449921725097290 5 -> -6.086686503752679375430019503679E+68 Inexact Rounded
remx3056 remainder -57131573677452.15449921725097290 4669681430736.326858508715643769 -> -1095396508616.232197112663247672
subx3056 subtract -57131573677452.15449921725097290 4669681430736.326858508715643769 -> -61801255108188.48135772596661667 Inexact Rounded
addx3057 add 90794826.55528018781830463383411 -5.471502270351231110027647216128 -> 90794821.08377791746707352380646 Inexact Rounded
comx3057 compare 90794826.55528018781830463383411 -5.471502270351231110027647216128 -> 1
divx3057 divide 90794826.55528018781830463383411 -5.471502270351231110027647216128 -> -16594131.20365054928428313232246 Inexact Rounded
dvix3057 divideint 90794826.55528018781830463383411 -5.471502270351231110027647216128 -> -16594131
mulx3057 multiply 90794826.55528018781830463383411 -5.471502270351231110027647216128 -> -496784099.6333617958496589124964 Inexact Rounded
powx3057 power 90794826.55528018781830463383411 -5 -> 1.620669590532856523565742953997E-40 Inexact Rounded
remx3057 remainder 90794826.55528018781830463383411 -5.471502270351231110027647216128 -> 1.114274442767230442307896655232
subx3057 subtract 90794826.55528018781830463383411 -5.471502270351231110027647216128 -> 90794832.02678245816953574386176 Inexact Rounded
addx3058 add 58508794729.35191160840980489138 -47060867.24988279680824397447551 -> 58461733862.10202881160156091690 Inexact Rounded
comx3058 compare 58508794729.35191160840980489138 -47060867.24988279680824397447551 -> 1
divx3058 divide 58508794729.35191160840980489138 -47060867.24988279680824397447551 -> -1243.257894477021678809337875304 Inexact Rounded
dvix3058 divideint 58508794729.35191160840980489138 -47060867.24988279680824397447551 -> -1243
mulx3058 multiply 58508794729.35191160840980489138 -47060867.24988279680824397447551 -> -2753474621708672573.249029643967 Inexact Rounded
powx3058 power 58508794729.35191160840980489138 -47060867 -> 0E-10029 Underflow Subnormal Inexact Rounded Clamped
remx3058 remainder 58508794729.35191160840980489138 -47060867.24988279680824397447551 -> 12136737.74759517576254461832107
subx3058 subtract 58508794729.35191160840980489138 -47060867.24988279680824397447551 -> 58555855596.60179440521804886586 Inexact Rounded
addx3059 add -746104.0768078474426464219416332E+006 9595418.300613754556671852801667E+385 -> 9.595418300613754556671852801667E+391 Inexact Rounded
comx3059 compare -746104.0768078474426464219416332E+006 9595418.300613754556671852801667E+385 -> -1
divx3059 divide -746104.0768078474426464219416332E+006 9595418.300613754556671852801667E+385 -> -7.775628465932789700547872511745E-381 Inexact Rounded
dvix3059 divideint -746104.0768078474426464219416332E+006 9595418.300613754556671852801667E+385 -> -0
mulx3059 multiply -746104.0768078474426464219416332E+006 9595418.300613754556671852801667E+385 -> -7.159180712764549711669939947084E+403 Inexact Rounded
powx3059 power -746104.0768078474426464219416332E+006 10 -> 5.345571346302582882805035996696E+118 Inexact Rounded
remx3059 remainder -746104.0768078474426464219416332E+006 9595418.300613754556671852801667E+385 -> -746104076807.8474426464219416332
subx3059 subtract -746104.0768078474426464219416332E+006 9595418.300613754556671852801667E+385 -> -9.595418300613754556671852801667E+391 Inexact Rounded
addx3060 add 55.99427632688387400403789659459E+119 -9.170530450881612853998489340127 -> 5.599427632688387400403789659459E+120 Inexact Rounded
comx3060 compare 55.99427632688387400403789659459E+119 -9.170530450881612853998489340127 -> 1
divx3060 divide 55.99427632688387400403789659459E+119 -9.170530450881612853998489340127 -> -6.105892851759828176655685111491E+119 Inexact Rounded
dvix3060 divideint 55.99427632688387400403789659459E+119 -9.170530450881612853998489340127 -> NaN Division_impossible
mulx3060 multiply 55.99427632688387400403789659459E+119 -9.170530450881612853998489340127 -> -5.134972161307679939281170944556E+121 Inexact Rounded
powx3060 power 55.99427632688387400403789659459E+119 -9 -> 1.848022584764384077672041056396E-1087 Inexact Rounded
remx3060 remainder 55.99427632688387400403789659459E+119 -9.170530450881612853998489340127 -> NaN Division_impossible
subx3060 subtract 55.99427632688387400403789659459E+119 -9.170530450881612853998489340127 -> 5.599427632688387400403789659459E+120 Inexact Rounded
addx3061 add -41214265628.83801241467317270595 1015336323798389903361978271354 -> 1015336323798389903320764005725 Inexact Rounded
comx3061 compare -41214265628.83801241467317270595 1015336323798389903361978271354 -> -1
divx3061 divide -41214265628.83801241467317270595 1015336323798389903361978271354 -> -4.059173759750342247620706384027E-20 Inexact Rounded
dvix3061 divideint -41214265628.83801241467317270595 1015336323798389903361978271354 -> -0
mulx3061 multiply -41214265628.83801241467317270595 1015336323798389903361978271354 -> -4.184634095163472384028549378392E+40 Inexact Rounded
powx3061 power -41214265628.83801241467317270595 1 -> -41214265628.83801241467317270595
remx3061 remainder -41214265628.83801241467317270595 1015336323798389903361978271354 -> -41214265628.83801241467317270595
subx3061 subtract -41214265628.83801241467317270595 1015336323798389903361978271354 -> -1015336323798389903403192536983 Inexact Rounded
addx3062 add 89937.39749201095570357557430822 82351554210093.60879476027800331 -> 82351554300031.00628677123370689 Inexact Rounded
comx3062 compare 89937.39749201095570357557430822 82351554210093.60879476027800331 -> -1
divx3062 divide 89937.39749201095570357557430822 82351554210093.60879476027800331 -> 1.092115362662913415592930982129E-9 Inexact Rounded
dvix3062 divideint 89937.39749201095570357557430822 82351554210093.60879476027800331 -> 0
mulx3062 multiply 89937.39749201095570357557430822 82351554210093.60879476027800331 -> 7406484465078077191.920015793662 Inexact Rounded
powx3062 power 89937.39749201095570357557430822 8 -> 4.280776267723913043050100934291E+39 Inexact Rounded
remx3062 remainder 89937.39749201095570357557430822 82351554210093.60879476027800331 -> 89937.39749201095570357557430822
subx3062 subtract 89937.39749201095570357557430822 82351554210093.60879476027800331 -> -82351554120156.21130274932229973 Inexact Rounded
addx3063 add 01712661.64677082156284125486943E+359 57932.78435529483241552042115837E-037 -> 1.712661646770821562841254869430E+365 Inexact Rounded
comx3063 compare 01712661.64677082156284125486943E+359 57932.78435529483241552042115837E-037 -> 1
divx3063 divide 01712661.64677082156284125486943E+359 57932.78435529483241552042115837E-037 -> 2.956290925475414185960999788848E+397 Inexact Rounded
dvix3063 divideint 01712661.64677082156284125486943E+359 57932.78435529483241552042115837E-037 -> NaN Division_impossible
mulx3063 multiply 01712661.64677082156284125486943E+359 57932.78435529483241552042115837E-037 -> 9.921925785595813587655312307930E+332 Inexact Rounded
powx3063 power 01712661.64677082156284125486943E+359 6 -> 2.523651803323047711735501944959E+2191 Inexact Rounded
remx3063 remainder 01712661.64677082156284125486943E+359 57932.78435529483241552042115837E-037 -> NaN Division_impossible
subx3063 subtract 01712661.64677082156284125486943E+359 57932.78435529483241552042115837E-037 -> 1.712661646770821562841254869430E+365 Inexact Rounded
addx3064 add -2647593306.528617691373470059213 -655531558709.4582168930191014461 -> -658179152015.9868345843925715053 Inexact Rounded
comx3064 compare -2647593306.528617691373470059213 -655531558709.4582168930191014461 -> 1
divx3064 divide -2647593306.528617691373470059213 -655531558709.4582168930191014461 -> 0.004038849497560303158639192895544 Inexact Rounded
dvix3064 divideint -2647593306.528617691373470059213 -655531558709.4582168930191014461 -> 0
mulx3064 multiply -2647593306.528617691373470059213 -655531558709.4582168930191014461 -> 1735580967057433153120.099643641 Inexact Rounded
powx3064 power -2647593306.528617691373470059213 -7 -> -1.096581914005902583413810201571E-66 Inexact Rounded
remx3064 remainder -2647593306.528617691373470059213 -655531558709.4582168930191014461 -> -2647593306.528617691373470059213
subx3064 subtract -2647593306.528617691373470059213 -655531558709.4582168930191014461 -> 652883965402.9295992016456313869 Inexact Rounded
addx3065 add 2904078690665765116603253099668E-329 -71.45586619176091599264717047885E+787 -> -7.145586619176091599264717047885E+788 Inexact Rounded
comx3065 compare 2904078690665765116603253099668E-329 -71.45586619176091599264717047885E+787 -> 1
divx3065 divide 2904078690665765116603253099668E-329 -71.45586619176091599264717047885E+787 -> -4.064157144036712325084472022316E-1088 Inexact Rounded
dvix3065 divideint 2904078690665765116603253099668E-329 -71.45586619176091599264717047885E+787 -> -0
mulx3065 multiply 2904078690665765116603253099668E-329 -71.45586619176091599264717047885E+787 -> -2.075134583305571527962710017262E+490 Inexact Rounded
powx3065 power 2904078690665765116603253099668E-329 -7 -> 5.740389208842895561250128407803E+2089 Inexact Rounded
remx3065 remainder 2904078690665765116603253099668E-329 -71.45586619176091599264717047885E+787 -> 2.904078690665765116603253099668E-299
subx3065 subtract 2904078690665765116603253099668E-329 -71.45586619176091599264717047885E+787 -> 7.145586619176091599264717047885E+788 Inexact Rounded
addx3066 add 22094338972.39109726522477999515 -409846549371.3900805039668417203E-499 -> 22094338972.39109726522477999515 Inexact Rounded
comx3066 compare 22094338972.39109726522477999515 -409846549371.3900805039668417203E-499 -> 1
divx3066 divide 22094338972.39109726522477999515 -409846549371.3900805039668417203E-499 -> -5.390880808019174194010224736965E+497 Inexact Rounded
dvix3066 divideint 22094338972.39109726522477999515 -409846549371.3900805039668417203E-499 -> NaN Division_impossible
mulx3066 multiply 22094338972.39109726522477999515 -409846549371.3900805039668417203E-499 -> -9.055288588476315822113975426730E-478 Inexact Rounded
powx3066 power 22094338972.39109726522477999515 -4 -> 4.196391022354122686725315209967E-42 Inexact Rounded
remx3066 remainder 22094338972.39109726522477999515 -409846549371.3900805039668417203E-499 -> NaN Division_impossible
subx3066 subtract 22094338972.39109726522477999515 -409846549371.3900805039668417203E-499 -> 22094338972.39109726522477999515 Inexact Rounded
addx3067 add -3374988581607586061255542201048 82293895124.90045271504836568681 -> -3374988581607586061173248305923 Inexact Rounded
comx3067 compare -3374988581607586061255542201048 82293895124.90045271504836568681 -> -1
divx3067 divide -3374988581607586061255542201048 82293895124.90045271504836568681 -> -41011408883796817797.81310977038 Inexact Rounded
dvix3067 divideint -3374988581607586061255542201048 82293895124.90045271504836568681 -> -41011408883796817797
mulx3067 multiply -3374988581607586061255542201048 82293895124.90045271504836568681 -> -2.777409563825512202793336132310E+41 Inexact Rounded
powx3067 power -3374988581607586061255542201048 8 -> 1.683365657238878057620634207267E+244 Inexact Rounded
remx3067 remainder -3374988581607586061255542201048 82293895124.90045271504836568681 -> -66913970168.62046257175566384243
subx3067 subtract -3374988581607586061255542201048 82293895124.90045271504836568681 -> -3374988581607586061337836096173 Inexact Rounded
addx3068 add -84172558160661.35863831029352323 -11271.58916600931155937291904890 -> -84172558171932.94780431960508260 Inexact Rounded
comx3068 compare -84172558160661.35863831029352323 -11271.58916600931155937291904890 -> -1
divx3068 divide -84172558160661.35863831029352323 -11271.58916600931155937291904890 -> 7467674426.467986736459678347587 Inexact Rounded
dvix3068 divideint -84172558160661.35863831029352323 -11271.58916600931155937291904890 -> 7467674426
mulx3068 multiply -84172558160661.35863831029352323 -11271.58916600931155937291904890 -> 948758494638999235.1953022970755 Inexact Rounded
powx3068 power -84172558160661.35863831029352323 -11272 -> 0E-10029 Underflow Subnormal Inexact Rounded Clamped
remx3068 remainder -84172558160661.35863831029352323 -11271.58916600931155937291904890 -> -5274.95422851496534479122656860
subx3068 subtract -84172558160661.35863831029352323 -11271.58916600931155937291904890 -> -84172558149389.76947230098196386 Inexact Rounded
addx3069 add -70046932324614.90596396237508541E-568 33.63163964004608865836577297698E-918 -> -7.004693232461490596396237508541E-555 Inexact Rounded
comx3069 compare -70046932324614.90596396237508541E-568 33.63163964004608865836577297698E-918 -> -1
divx3069 divide -70046932324614.90596396237508541E-568 33.63163964004608865836577297698E-918 -> -2.082768876995463487926920072359E+362 Inexact Rounded
dvix3069 divideint -70046932324614.90596396237508541E-568 33.63163964004608865836577297698E-918 -> NaN Division_impossible
mulx3069 multiply -70046932324614.90596396237508541E-568 33.63163964004608865836577297698E-918 -> -2.355793185832144388285949021738E-1471 Inexact Rounded
powx3069 power -70046932324614.90596396237508541E-568 3 -> -3.436903678302639677280508409829E-1663 Inexact Rounded
remx3069 remainder -70046932324614.90596396237508541E-568 33.63163964004608865836577297698E-918 -> NaN Division_impossible
subx3069 subtract -70046932324614.90596396237508541E-568 33.63163964004608865836577297698E-918 -> -7.004693232461490596396237508541E-555 Inexact Rounded
addx3070 add 0004125384407.053782660115680886 -391429084.5847321402514385603223E-648 -> 4125384407.053782660115680886000 Inexact Rounded
comx3070 compare 0004125384407.053782660115680886 -391429084.5847321402514385603223E-648 -> 1
divx3070 divide 0004125384407.053782660115680886 -391429084.5847321402514385603223E-648 -> -1.053928941287132717250540955457E+649 Inexact Rounded
dvix3070 divideint 0004125384407.053782660115680886 -391429084.5847321402514385603223E-648 -> NaN Division_impossible
mulx3070 multiply 0004125384407.053782660115680886 -391429084.5847321402514385603223E-648 -> -1.614795442013190139080634449273E-630 Inexact Rounded
powx3070 power 0004125384407.053782660115680886 -4 -> 3.452568541597450106266555783362E-39 Inexact Rounded
remx3070 remainder 0004125384407.053782660115680886 -391429084.5847321402514385603223E-648 -> NaN Division_impossible
subx3070 subtract 0004125384407.053782660115680886 -391429084.5847321402514385603223E-648 -> 4125384407.053782660115680886000 Inexact Rounded
addx3071 add -31823131.15691583393820628480997E-440 92913.91582947237200286427030028E+771 -> 9.291391582947237200286427030028E+775 Inexact Rounded
comx3071 compare -31823131.15691583393820628480997E-440 92913.91582947237200286427030028E+771 -> -1
divx3071 divide -31823131.15691583393820628480997E-440 92913.91582947237200286427030028E+771 -> -3.425012375468251447194400841658E-1209 Inexact Rounded
dvix3071 divideint -31823131.15691583393820628480997E-440 92913.91582947237200286427030028E+771 -> -0
mulx3071 multiply -31823131.15691583393820628480997E-440 92913.91582947237200286427030028E+771 -> -2.956811729743937541973845029816E+343 Inexact Rounded
powx3071 power -31823131.15691583393820628480997E-440 9 -> -3.347234803487575870321338308655E-3893 Inexact Rounded
remx3071 remainder -31823131.15691583393820628480997E-440 92913.91582947237200286427030028E+771 -> -3.182313115691583393820628480997E-433
subx3071 subtract -31823131.15691583393820628480997E-440 92913.91582947237200286427030028E+771 -> -9.291391582947237200286427030028E+775 Inexact Rounded
addx3072 add 55573867888.91575330563698128150 599.5231614736232188354393212234 -> 55573868488.43891477926020011694 Inexact Rounded
comx3072 compare 55573867888.91575330563698128150 599.5231614736232188354393212234 -> 1
divx3072 divide 55573867888.91575330563698128150 599.5231614736232188354393212234 -> 92696782.14318796763098335498657 Inexact Rounded
dvix3072 divideint 55573867888.91575330563698128150 599.5231614736232188354393212234 -> 92696782
mulx3072 multiply 55573867888.91575330563698128150 599.5231614736232188354393212234 -> 33317820972080.24347717542221477 Inexact Rounded
powx3072 power 55573867888.91575330563698128150 600 -> 8.363240671070136278221965616973E+6446 Inexact Rounded
remx3072 remainder 55573867888.91575330563698128150 599.5231614736232188354393212234 -> 85.8445030391099686478265169012
subx3072 subtract 55573867888.91575330563698128150 599.5231614736232188354393212234 -> 55573867289.39259183201376244606 Inexact Rounded
addx3073 add -5447727448431680878699555714796E-800 5487207.142687001607026665515349E-362 -> 5.487207142687001607026665515349E-356 Inexact Rounded
comx3073 compare -5447727448431680878699555714796E-800 5487207.142687001607026665515349E-362 -> -1
divx3073 divide -5447727448431680878699555714796E-800 5487207.142687001607026665515349E-362 -> -9.928051387110587327889009363069E-415 Inexact Rounded
dvix3073 divideint -5447727448431680878699555714796E-800 5487207.142687001607026665515349E-362 -> -0
mulx3073 multiply -5447727448431680878699555714796E-800 5487207.142687001607026665515349E-362 -> -2.989280896644635352838087864373E-1125 Inexact Rounded
powx3073 power -5447727448431680878699555714796E-800 5 -> -4.798183553278543065204833300725E-3847 Inexact Rounded
remx3073 remainder -5447727448431680878699555714796E-800 5487207.142687001607026665515349E-362 -> -5.447727448431680878699555714796E-770
subx3073 subtract -5447727448431680878699555714796E-800 5487207.142687001607026665515349E-362 -> -5.487207142687001607026665515349E-356 Inexact Rounded
addx3074 add 0418349404834.547110239542290134 09819915.92405288066606124554841 -> 418359224750.4711631202083513795 Inexact Rounded
comx3074 compare 0418349404834.547110239542290134 09819915.92405288066606124554841 -> 1
divx3074 divide 0418349404834.547110239542290134 09819915.92405288066606124554841 -> 42602.13713335803513874339309132 Inexact Rounded
dvix3074 divideint 0418349404834.547110239542290134 09819915.92405288066606124554841 -> 42602
mulx3074 multiply 0418349404834.547110239542290134 09819915.92405288066606124554841 -> 4108155982352814348.343441299082 Inexact Rounded
powx3074 power 0418349404834.547110239542290134 9819916 -> Infinity Overflow Inexact Rounded
remx3074 remainder 0418349404834.547110239542290134 09819915.92405288066606124554841 -> 1346638.04628810400110728063718
subx3074 subtract 0418349404834.547110239542290134 09819915.92405288066606124554841 -> 418339584918.6230573588762288885 Inexact Rounded
addx3075 add -262021.7565194737396448014286436 -7983992600094836304387324162042E+390 -> -7.983992600094836304387324162042E+420 Inexact Rounded
comx3075 compare -262021.7565194737396448014286436 -7983992600094836304387324162042E+390 -> 1
divx3075 divide -262021.7565194737396448014286436 -7983992600094836304387324162042E+390 -> 3.281838669494274896180376328433E-416 Inexact Rounded
dvix3075 divideint -262021.7565194737396448014286436 -7983992600094836304387324162042E+390 -> 0
mulx3075 multiply -262021.7565194737396448014286436 -7983992600094836304387324162042E+390 -> 2.091979765115329268275803385534E+426 Inexact Rounded
powx3075 power -262021.7565194737396448014286436 -8 -> 4.500918721033033032706782304195E-44 Inexact Rounded
remx3075 remainder -262021.7565194737396448014286436 -7983992600094836304387324162042E+390 -> -262021.7565194737396448014286436
subx3075 subtract -262021.7565194737396448014286436 -7983992600094836304387324162042E+390 -> 7.983992600094836304387324162042E+420 Inexact Rounded
addx3076 add 48696050631.42565380301204592392E-505 -33868752339.85057267609967806187E+821 -> -3.386875233985057267609967806187E+831 Inexact Rounded
comx3076 compare 48696050631.42565380301204592392E-505 -33868752339.85057267609967806187E+821 -> 1
divx3076 divide 48696050631.42565380301204592392E-505 -33868752339.85057267609967806187E+821 -> -1.437786964892976582009952172420E-1326 Inexact Rounded
dvix3076 divideint 48696050631.42565380301204592392E-505 -33868752339.85057267609967806187E+821 -> -0
mulx3076 multiply 48696050631.42565380301204592392E-505 -33868752339.85057267609967806187E+821 -> -1.649274478764579569246425611629E+337 Inexact Rounded
powx3076 power 48696050631.42565380301204592392E-505 -3 -> 8.660017688773759463020340778853E+1482 Inexact Rounded
remx3076 remainder 48696050631.42565380301204592392E-505 -33868752339.85057267609967806187E+821 -> 4.869605063142565380301204592392E-495
subx3076 subtract 48696050631.42565380301204592392E-505 -33868752339.85057267609967806187E+821 -> 3.386875233985057267609967806187E+831 Inexact Rounded
addx3077 add 95316999.19440144356471126680708 -60791.33805057402845885978390435 -> 95256207.85635086953625240702318 Inexact Rounded
comx3077 compare 95316999.19440144356471126680708 -60791.33805057402845885978390435 -> 1
divx3077 divide 95316999.19440144356471126680708 -60791.33805057402845885978390435 -> -1567.937180706641856870286122623 Inexact Rounded
dvix3077 divideint 95316999.19440144356471126680708 -60791.33805057402845885978390435 -> -1567
mulx3077 multiply 95316999.19440144356471126680708 -60791.33805057402845885978390435 -> -5794447919993.150493301061195714 Inexact Rounded
powx3077 power 95316999.19440144356471126680708 -60791 -> 0E-10029 Underflow Subnormal Inexact Rounded Clamped
remx3077 remainder 95316999.19440144356471126680708 -60791.33805057402845885978390435 -> 56972.46915194096967798542896355
subx3077 subtract 95316999.19440144356471126680708 -60791.33805057402845885978390435 -> 95377790.53245201759317012659098 Inexact Rounded
addx3078 add -5326702296402708234722215224979E-136 8032459.450998820205916538543258 -> 8032459.450998820205916538543258 Inexact Rounded
comx3078 compare -5326702296402708234722215224979E-136 8032459.450998820205916538543258 -> -1
divx3078 divide -5326702296402708234722215224979E-136 8032459.450998820205916538543258 -> -6.631471131473117487839243582873E-113 Inexact Rounded
dvix3078 divideint -5326702296402708234722215224979E-136 8032459.450998820205916538543258 -> -0
mulx3078 multiply -5326702296402708234722215224979E-136 8032459.450998820205916538543258 -> -4.278652020339705265013632757349E-99 Inexact Rounded
powx3078 power -5326702296402708234722215224979E-136 8032459 -> -0E-10029 Underflow Subnormal Inexact Rounded Clamped
remx3078 remainder -5326702296402708234722215224979E-136 8032459.450998820205916538543258 -> -5.326702296402708234722215224979E-106
subx3078 subtract -5326702296402708234722215224979E-136 8032459.450998820205916538543258 -> -8032459.450998820205916538543258 Inexact Rounded
addx3079 add 67.18750684079501575335482615780E-281 734.1168841683438410314843011541E-854 -> 6.718750684079501575335482615780E-280 Inexact Rounded
comx3079 compare 67.18750684079501575335482615780E-281 734.1168841683438410314843011541E-854 -> 1
divx3079 divide 67.18750684079501575335482615780E-281 734.1168841683438410314843011541E-854 -> 9.152153872187460598958616592442E+571 Inexact Rounded
dvix3079 divideint 67.18750684079501575335482615780E-281 734.1168841683438410314843011541E-854 -> NaN Division_impossible
mulx3079 multiply 67.18750684079501575335482615780E-281 734.1168841683438410314843011541E-854 -> 4.932348317700372401849231767007E-1131 Inexact Rounded
powx3079 power 67.18750684079501575335482615780E-281 7 -> 6.180444071023111300817518409550E-1955 Inexact Rounded
remx3079 remainder 67.18750684079501575335482615780E-281 734.1168841683438410314843011541E-854 -> NaN Division_impossible
subx3079 subtract 67.18750684079501575335482615780E-281 734.1168841683438410314843011541E-854 -> 6.718750684079501575335482615780E-280 Inexact Rounded
addx3080 add -8739299372114.092482914139281669 507610074.7343577029345077385838 -> -8738791762039.358125211204773930 Inexact Rounded
comx3080 compare -8739299372114.092482914139281669 507610074.7343577029345077385838 -> -1
divx3080 divide -8739299372114.092482914139281669 507610074.7343577029345077385838 -> -17216.56012577673731612130068130 Inexact Rounded
dvix3080 divideint -8739299372114.092482914139281669 507610074.7343577029345077385838 -> -17216
mulx3080 multiply -8739299372114.092482914139281669 507610074.7343577029345077385838 -> -4436156407404759833857.580707024 Inexact Rounded
powx3080 power -8739299372114.092482914139281669 507610075 -> -Infinity Overflow Inexact Rounded
remx3080 remainder -8739299372114.092482914139281669 507610074.7343577029345077385838 -> -284325487.3902691936540542102992
subx3080 subtract -8739299372114.092482914139281669 507610074.7343577029345077385838 -> -8739806982188.826840617073789408 Inexact Rounded
addx3081 add 2454.002078468928665008217727731 583546039.6233842869119950982009E-147 -> 2454.002078468928665008217727731 Inexact Rounded
comx3081 compare 2454.002078468928665008217727731 583546039.6233842869119950982009E-147 -> 1
divx3081 divide 2454.002078468928665008217727731 583546039.6233842869119950982009E-147 -> 4.205327278123112611006652533618E+141 Inexact Rounded
dvix3081 divideint 2454.002078468928665008217727731 583546039.6233842869119950982009E-147 -> NaN Division_impossible
mulx3081 multiply 2454.002078468928665008217727731 583546039.6233842869119950982009E-147 -> 1.432023194118096842806010293027E-135 Inexact Rounded
powx3081 power 2454.002078468928665008217727731 6 -> 218398452792293853786.9263054420 Inexact Rounded
remx3081 remainder 2454.002078468928665008217727731 583546039.6233842869119950982009E-147 -> NaN Division_impossible
subx3081 subtract 2454.002078468928665008217727731 583546039.6233842869119950982009E-147 -> 2454.002078468928665008217727731 Inexact Rounded
addx3082 add 764578.5204849936912066033177429 64603.13571259164812609436832506 -> 829181.6561975853393326976860680 Inexact Rounded
comx3082 compare 764578.5204849936912066033177429 64603.13571259164812609436832506 -> 1
divx3082 divide 764578.5204849936912066033177429 64603.13571259164812609436832506 -> 11.83500633601553578851124281417 Inexact Rounded
dvix3082 divideint 764578.5204849936912066033177429 64603.13571259164812609436832506 -> 11
mulx3082 multiply 764578.5204849936912066033177429 64603.13571259164812609436832506 -> 49394169921.82458094138096628957 Inexact Rounded
powx3082 power 764578.5204849936912066033177429 64603 -> Infinity Overflow Inexact Rounded
remx3082 remainder 764578.5204849936912066033177429 64603.13571259164812609436832506 -> 53944.02764648556181956526616724
subx3082 subtract 764578.5204849936912066033177429 64603.13571259164812609436832506 -> 699975.3847724020430805089494178 Inexact Rounded
addx3083 add 079203.7330103777716903518367560 846388934347.6324036132959664705 -> 846389013551.3654139910676568223 Inexact Rounded
comx3083 compare 079203.7330103777716903518367560 846388934347.6324036132959664705 -> -1
divx3083 divide 079203.7330103777716903518367560 846388934347.6324036132959664705 -> 9.357841270860339858146471876044E-8 Inexact Rounded
dvix3083 divideint 079203.7330103777716903518367560 846388934347.6324036132959664705 -> 0
mulx3083 multiply 079203.7330103777716903518367560 846388934347.6324036132959664705 -> 67037163179008037.19983564789203 Inexact Rounded
powx3083 power 079203.7330103777716903518367560 8 -> 1.548692549503356788115682996756E+39 Inexact Rounded
remx3083 remainder 079203.7330103777716903518367560 846388934347.6324036132959664705 -> 79203.7330103777716903518367560
subx3083 subtract 079203.7330103777716903518367560 846388934347.6324036132959664705 -> -846388855143.8993932355242761187 Inexact Rounded
addx3084 add -4278.581514688669249247007127899E-329 5.474973992953902631890208360829 -> 5.474973992953902631890208360829 Inexact Rounded
comx3084 compare -4278.581514688669249247007127899E-329 5.474973992953902631890208360829 -> -1
divx3084 divide -4278.581514688669249247007127899E-329 5.474973992953902631890208360829 -> -7.814797878848469282033896969532E-327 Inexact Rounded
dvix3084 divideint -4278.581514688669249247007127899E-329 5.474973992953902631890208360829 -> -0
mulx3084 multiply -4278.581514688669249247007127899E-329 5.474973992953902631890208360829 -> -2.342512251965378028433584538870E-325 Inexact Rounded
powx3084 power -4278.581514688669249247007127899E-329 5 -> -1.433834587801771244104676682986E-1627 Inexact Rounded
remx3084 remainder -4278.581514688669249247007127899E-329 5.474973992953902631890208360829 -> -4.278581514688669249247007127899E-326
subx3084 subtract -4278.581514688669249247007127899E-329 5.474973992953902631890208360829 -> -5.474973992953902631890208360829 Inexact Rounded
addx3085 add 60867019.81764798845468445196869E+651 6.149612565404080501157093851895E+817 -> 6.149612565404080501157093851895E+817 Inexact Rounded
comx3085 compare 60867019.81764798845468445196869E+651 6.149612565404080501157093851895E+817 -> -1
divx3085 divide 60867019.81764798845468445196869E+651 6.149612565404080501157093851895E+817 -> 9.897699923417617920996187420968E-160 Inexact Rounded
dvix3085 divideint 60867019.81764798845468445196869E+651 6.149612565404080501157093851895E+817 -> 0
mulx3085 multiply 60867019.81764798845468445196869E+651 6.149612565404080501157093851895E+817 -> 3.743085898893072544197564013497E+1476 Inexact Rounded
powx3085 power 60867019.81764798845468445196869E+651 6 -> 5.085014897388871736767602086646E+3952 Inexact Rounded
remx3085 remainder 60867019.81764798845468445196869E+651 6.149612565404080501157093851895E+817 -> 6.086701981764798845468445196869E+658
subx3085 subtract 60867019.81764798845468445196869E+651 6.149612565404080501157093851895E+817 -> -6.149612565404080501157093851895E+817 Inexact Rounded
addx3086 add 18554417738217.62218590965803605E-382 -0894505909529.052378474618435782E+527 -> -8.945059095290523784746184357820E+538 Inexact Rounded
comx3086 compare 18554417738217.62218590965803605E-382 -0894505909529.052378474618435782E+527 -> 1
divx3086 divide 18554417738217.62218590965803605E-382 -0894505909529.052378474618435782E+527 -> -2.074264411286709228674841672954E-908 Inexact Rounded
dvix3086 divideint 18554417738217.62218590965803605E-382 -0894505909529.052378474618435782E+527 -> -0
mulx3086 multiply 18554417738217.62218590965803605E-382 -0894505909529.052378474618435782E+527 -> -1.659703631470633700884136887614E+170 Inexact Rounded
powx3086 power 18554417738217.62218590965803605E-382 -9 -> 3.836842998295531899082688721531E+3318 Inexact Rounded
remx3086 remainder 18554417738217.62218590965803605E-382 -0894505909529.052378474618435782E+527 -> 1.855441773821762218590965803605E-369
subx3086 subtract 18554417738217.62218590965803605E-382 -0894505909529.052378474618435782E+527 -> 8.945059095290523784746184357820E+538 Inexact Rounded
addx3087 add 69073355517144.36356688642213839 997784782535.6104634823627327033E+116 -> 9.977847825356104634823627327033E+127 Inexact Rounded
comx3087 compare 69073355517144.36356688642213839 997784782535.6104634823627327033E+116 -> -1
divx3087 divide 69073355517144.36356688642213839 997784782535.6104634823627327033E+116 -> 6.922670772910807388395384866884E-115 Inexact Rounded
dvix3087 divideint 69073355517144.36356688642213839 997784782535.6104634823627327033E+116 -> 0
mulx3087 multiply 69073355517144.36356688642213839 997784782535.6104634823627327033E+116 -> 6.892034301367879802693422066425E+141 Inexact Rounded
powx3087 power 69073355517144.36356688642213839 10 -> 2.472324890841334302628435461516E+138 Inexact Rounded
remx3087 remainder 69073355517144.36356688642213839 997784782535.6104634823627327033E+116 -> 69073355517144.36356688642213839
subx3087 subtract 69073355517144.36356688642213839 997784782535.6104634823627327033E+116 -> -9.977847825356104634823627327033E+127 Inexact Rounded
addx3088 add 450282259072.8657099359104277477 -1791307965314309175477911369824 -> -1791307965314309175027629110751 Inexact Rounded
comx3088 compare 450282259072.8657099359104277477 -1791307965314309175477911369824 -> 1
divx3088 divide 450282259072.8657099359104277477 -1791307965314309175477911369824 -> -2.513706564096350714213771006483E-19 Inexact Rounded
dvix3088 divideint 450282259072.8657099359104277477 -1791307965314309175477911369824 -> -0
mulx3088 multiply 450282259072.8657099359104277477 -1791307965314309175477911369824 -> -8.065941973169457071650996861677E+41 Inexact Rounded
powx3088 power 450282259072.8657099359104277477 -2 -> 4.932082442194544671633570348838E-24 Inexact Rounded
remx3088 remainder 450282259072.8657099359104277477 -1791307965314309175477911369824 -> 450282259072.8657099359104277477
subx3088 subtract 450282259072.8657099359104277477 -1791307965314309175477911369824 -> 1791307965314309175928193628897 Inexact Rounded
addx3089 add 954678411.7838149266455177850037 142988.7096204254529284334278794 -> 954821400.4934353520984462184316 Inexact Rounded
comx3089 compare 954678411.7838149266455177850037 142988.7096204254529284334278794 -> 1
divx3089 divide 954678411.7838149266455177850037 142988.7096204254529284334278794 -> 6676.599951968811589335427770046 Inexact Rounded
dvix3089 divideint 954678411.7838149266455177850037 142988.7096204254529284334278794 -> 6676
mulx3089 multiply 954678411.7838149266455177850037 142988.7096204254529284334278794 -> 136508234203444.8694879431412375 Inexact Rounded
powx3089 power 954678411.7838149266455177850037 142989 -> Infinity Overflow Inexact Rounded
remx3089 remainder 954678411.7838149266455177850037 142988.7096204254529284334278794 -> 85786.3578546028952962204808256
subx3089 subtract 954678411.7838149266455177850037 142988.7096204254529284334278794 -> 954535423.0741945011925893515758 Inexact Rounded
addx3090 add -9244530976.220812127155852389807E+557 541089.4715446858896619078627941 -> -9.244530976220812127155852389807E+566 Inexact Rounded
comx3090 compare -9244530976.220812127155852389807E+557 541089.4715446858896619078627941 -> -1
divx3090 divide -9244530976.220812127155852389807E+557 541089.4715446858896619078627941 -> -1.708503207395591002370649848757E+561 Inexact Rounded
dvix3090 divideint -9244530976.220812127155852389807E+557 541089.4715446858896619078627941 -> NaN Division_impossible
mulx3090 multiply -9244530976.220812127155852389807E+557 541089.4715446858896619078627941 -> -5.002118380601798392363043558941E+572 Inexact Rounded
powx3090 power -9244530976.220812127155852389807E+557 541089 -> -Infinity Overflow Inexact Rounded
remx3090 remainder -9244530976.220812127155852389807E+557 541089.4715446858896619078627941 -> NaN Division_impossible
subx3090 subtract -9244530976.220812127155852389807E+557 541089.4715446858896619078627941 -> -9.244530976220812127155852389807E+566 Inexact Rounded
addx3091 add -75492024.20990197005974241975449 -14760421311348.35269044633000927 -> -14760496803372.56259241638975169 Inexact Rounded
comx3091 compare -75492024.20990197005974241975449 -14760421311348.35269044633000927 -> 1
divx3091 divide -75492024.20990197005974241975449 -14760421311348.35269044633000927 -> 0.000005114489797920668836278344635108 Inexact Rounded
dvix3091 divideint -75492024.20990197005974241975449 -14760421311348.35269044633000927 -> 0
mulx3091 multiply -75492024.20990197005974241975449 -14760421311348.35269044633000927 -> 1114294082984662825831.464787487 Inexact Rounded
powx3091 power -75492024.20990197005974241975449 -1 -> -1.324643246046162082348970735576E-8 Inexact Rounded
remx3091 remainder -75492024.20990197005974241975449 -14760421311348.35269044633000927 -> -75492024.20990197005974241975449
subx3091 subtract -75492024.20990197005974241975449 -14760421311348.35269044633000927 -> 14760345819324.14278847627026685 Inexact Rounded
addx3092 add 317747.6972215715434186596178036E-452 24759763.33144824613591228097330E+092 -> 2.475976333144824613591228097330E+99 Inexact Rounded
comx3092 compare 317747.6972215715434186596178036E-452 24759763.33144824613591228097330E+092 -> -1
divx3092 divide 317747.6972215715434186596178036E-452 24759763.33144824613591228097330E+092 -> 1.283322837007852247594216151634E-546 Inexact Rounded
dvix3092 divideint 317747.6972215715434186596178036E-452 24759763.33144824613591228097330E+092 -> 0
mulx3092 multiply 317747.6972215715434186596178036E-452 24759763.33144824613591228097330E+092 -> 7.867357782318786860404997647513E-348 Inexact Rounded
powx3092 power 317747.6972215715434186596178036E-452 2 -> 1.009635990896115043331231496209E-893 Inexact Rounded
remx3092 remainder 317747.6972215715434186596178036E-452 24759763.33144824613591228097330E+092 -> 3.177476972215715434186596178036E-447
subx3092 subtract 317747.6972215715434186596178036E-452 24759763.33144824613591228097330E+092 -> -2.475976333144824613591228097330E+99 Inexact Rounded
addx3093 add -8.153334430358647134334545353427 -9.717872025814596548462854853522 -> -17.87120645617324368279740020695 Inexact Rounded
comx3093 compare -8.153334430358647134334545353427 -9.717872025814596548462854853522 -> 1
divx3093 divide -8.153334430358647134334545353427 -9.717872025814596548462854853522 -> 0.8390040956188859972044344532019 Inexact Rounded
dvix3093 divideint -8.153334430358647134334545353427 -9.717872025814596548462854853522 -> 0
mulx3093 multiply -8.153334430358647134334545353427 -9.717872025814596548462854853522 -> 79.23306057789328578902960605222 Inexact Rounded
powx3093 power -8.153334430358647134334545353427 -10 -> 7.702778966876727056635952801162E-10 Inexact Rounded
remx3093 remainder -8.153334430358647134334545353427 -9.717872025814596548462854853522 -> -8.153334430358647134334545353427
subx3093 subtract -8.153334430358647134334545353427 -9.717872025814596548462854853522 -> 1.564537595455949414128309500095
addx3094 add 7.267345197492967332320456062961E-478 5054015481833.263541189916208065 -> 5054015481833.263541189916208065 Inexact Rounded
comx3094 compare 7.267345197492967332320456062961E-478 5054015481833.263541189916208065 -> -1
divx3094 divide 7.267345197492967332320456062961E-478 5054015481833.263541189916208065 -> 1.437934890309606594895299558654E-490 Inexact Rounded
dvix3094 divideint 7.267345197492967332320456062961E-478 5054015481833.263541189916208065 -> 0
mulx3094 multiply 7.267345197492967332320456062961E-478 5054015481833.263541189916208065 -> 3.672927513995607308048737751972E-465 Inexact Rounded
powx3094 power 7.267345197492967332320456062961E-478 5 -> 2.027117616846668568108096583897E-2386 Inexact Rounded
remx3094 remainder 7.267345197492967332320456062961E-478 5054015481833.263541189916208065 -> 7.267345197492967332320456062961E-478
subx3094 subtract 7.267345197492967332320456062961E-478 5054015481833.263541189916208065 -> -5054015481833.263541189916208065 Inexact Rounded
addx3095 add -1223354029.862567054230912271171 8135774223401322785475014855625 -> 8135774223401322785473791501595 Inexact Rounded
comx3095 compare -1223354029.862567054230912271171 8135774223401322785475014855625 -> -1
divx3095 divide -1223354029.862567054230912271171 8135774223401322785475014855625 -> -1.503672540892020337688277553692E-22 Inexact Rounded
dvix3095 divideint -1223354029.862567054230912271171 8135774223401322785475014855625 -> -0
mulx3095 multiply -1223354029.862567054230912271171 8135774223401322785475014855625 -> -9.952932182250005119307429060894E+39 Inexact Rounded
powx3095 power -1223354029.862567054230912271171 8 -> 5.016689887192830666848068841227E+72 Inexact Rounded
remx3095 remainder -1223354029.862567054230912271171 8135774223401322785475014855625 -> -1223354029.862567054230912271171
subx3095 subtract -1223354029.862567054230912271171 8135774223401322785475014855625 -> -8135774223401322785476238209655 Inexact Rounded
addx3096 add 285397644111.5655679961211349982E+645 -2479499427613157519359627280704 -> 2.853976441115655679961211349982E+656 Inexact Rounded
comx3096 compare 285397644111.5655679961211349982E+645 -2479499427613157519359627280704 -> 1
divx3096 divide 285397644111.5655679961211349982E+645 -2479499427613157519359627280704 -> -1.151029280076495626421134733122E+626 Inexact Rounded
dvix3096 divideint 285397644111.5655679961211349982E+645 -2479499427613157519359627280704 -> NaN Division_impossible
mulx3096 multiply 285397644111.5655679961211349982E+645 -2479499427613157519359627280704 -> -7.076432952167704614138411740001E+686 Inexact Rounded
powx3096 power 285397644111.5655679961211349982E+645 -2 -> 1.227719722087860401233030479451E-1313 Inexact Rounded
remx3096 remainder 285397644111.5655679961211349982E+645 -2479499427613157519359627280704 -> NaN Division_impossible
subx3096 subtract 285397644111.5655679961211349982E+645 -2479499427613157519359627280704 -> 2.853976441115655679961211349982E+656 Inexact Rounded
addx3097 add -4673112.663442366293812346653429 -3429.998403142546001438238460958 -> -4676542.661845508839813784891890 Inexact Rounded
comx3097 compare -4673112.663442366293812346653429 -3429.998403142546001438238460958 -> -1
divx3097 divide -4673112.663442366293812346653429 -3429.998403142546001438238460958 -> 1362.424151323477505064686589716 Inexact Rounded
dvix3097 divideint -4673112.663442366293812346653429 -3429.998403142546001438238460958 -> 1362
mulx3097 multiply -4673112.663442366293812346653429 -3429.998403142546001438238460958 -> 16028768973.31252639476148371361 Inexact Rounded
powx3097 power -4673112.663442366293812346653429 -3430 -> 0E-10029 Underflow Subnormal Inexact Rounded Clamped
remx3097 remainder -4673112.663442366293812346653429 -3429.998403142546001438238460958 -> -1454.838362218639853465869604204
subx3097 subtract -4673112.663442366293812346653429 -3429.998403142546001438238460958 -> -4669682.665039223747810908414968 Inexact Rounded
addx3098 add 88.96492479681278079861456051103 386939.4621006514751889096510923E+139 -> 3.869394621006514751889096510923E+144 Inexact Rounded
comx3098 compare 88.96492479681278079861456051103 386939.4621006514751889096510923E+139 -> -1
divx3098 divide 88.96492479681278079861456051103 386939.4621006514751889096510923E+139 -> 2.299194926095985647821385937618E-143 Inexact Rounded
dvix3098 divideint 88.96492479681278079861456051103 386939.4621006514751889096510923E+139 -> 0
mulx3098 multiply 88.96492479681278079861456051103 386939.4621006514751889096510923E+139 -> 3.442404014670364763780946297856E+146 Inexact Rounded
powx3098 power 88.96492479681278079861456051103 4 -> 62643391.73078290226474758858970 Inexact Rounded
remx3098 remainder 88.96492479681278079861456051103 386939.4621006514751889096510923E+139 -> 88.96492479681278079861456051103
subx3098 subtract 88.96492479681278079861456051103 386939.4621006514751889096510923E+139 -> -3.869394621006514751889096510923E+144 Inexact Rounded
addx3099 add 064326846.4286437304788069444326E-942 92.23649942010862087149015091350 -> 92.23649942010862087149015091350 Inexact Rounded
comx3099 compare 064326846.4286437304788069444326E-942 92.23649942010862087149015091350 -> -1
divx3099 divide 064326846.4286437304788069444326E-942 92.23649942010862087149015091350 -> 6.974120530708230229344349531719E-937 Inexact Rounded
dvix3099 divideint 064326846.4286437304788069444326E-942 92.23649942010862087149015091350 -> 0
mulx3099 multiply 064326846.4286437304788069444326E-942 92.23649942010862087149015091350 -> 5.933283133313013755814405436342E-933 Inexact Rounded
powx3099 power 064326846.4286437304788069444326E-942 92 -> 0E-10029 Underflow Subnormal Inexact Rounded Clamped
remx3099 remainder 064326846.4286437304788069444326E-942 92.23649942010862087149015091350 -> 6.43268464286437304788069444326E-935
subx3099 subtract 064326846.4286437304788069444326E-942 92.23649942010862087149015091350 -> -92.23649942010862087149015091350 Inexact Rounded
addx3100 add 504507.0043949324433170405699360 605387.7175522955344659311072099 -> 1109894.721947227977782971677146 Inexact Rounded
comx3100 compare 504507.0043949324433170405699360 605387.7175522955344659311072099 -> -1
divx3100 divide 504507.0043949324433170405699360 605387.7175522955344659311072099 -> 0.8333618105678718895216067463832 Inexact Rounded
dvix3100 divideint 504507.0043949324433170405699360 605387.7175522955344659311072099 -> 0
mulx3100 multiply 504507.0043949324433170405699360 605387.7175522955344659311072099 -> 305422343879.7940838630401656585 Inexact Rounded
powx3100 power 504507.0043949324433170405699360 605388 -> Infinity Overflow Inexact Rounded
remx3100 remainder 504507.0043949324433170405699360 605387.7175522955344659311072099 -> 504507.0043949324433170405699360
subx3100 subtract 504507.0043949324433170405699360 605387.7175522955344659311072099 -> -100880.7131573630911488905372739

-- randomly generated testcases [26 Sep 2001]
precision: 32
rounding: half_up
maxExponent: 9999

addx3201 add 1.5283550163839789319142430253644 -1.6578158484822969520405291379492 -> -0.1294608320983180201262861125848
comx3201 compare 1.5283550163839789319142430253644 -1.6578158484822969520405291379492 -> 1
divx3201 divide 1.5283550163839789319142430253644 -1.6578158484822969520405291379492 -> -0.92190879812324313630282980110280 Inexact Rounded
dvix3201 divideint 1.5283550163839789319142430253644 -1.6578158484822969520405291379492 -> -0
mulx3201 multiply 1.5283550163839789319142430253644 -1.6578158484822969520405291379492 -> -2.5337311682687808926633910761614 Inexact Rounded
powx3201 power 1.5283550163839789319142430253644 -2 -> 0.42810618916584924451466691603128 Inexact Rounded
remx3201 remainder 1.5283550163839789319142430253644 -1.6578158484822969520405291379492 -> 1.5283550163839789319142430253644
subx3201 subtract 1.5283550163839789319142430253644 -1.6578158484822969520405291379492 -> 3.1861708648662758839547721633136
addx3202 add -622903030605.2867503937836507326 6519388607.1331855704471328795821 -> -616383641998.15356482333651785302 Inexact Rounded
comx3202 compare -622903030605.2867503937836507326 6519388607.1331855704471328795821 -> -1
divx3202 divide -622903030605.2867503937836507326 6519388607.1331855704471328795821 -> -95.546234185785110491676894153510 Inexact Rounded
dvix3202 divideint -622903030605.2867503937836507326 6519388607.1331855704471328795821 -> -95
mulx3202 multiply -622903030605.2867503937836507326 6519388607.1331855704471328795821 -> -4060946921076840449949.6988828486 Inexact Rounded
powx3202 power -622903030605.2867503937836507326 7 -> -3.6386736597702404352813308064300E+82 Inexact Rounded
remx3202 remainder -622903030605.2867503937836507326 6519388607.1331855704471328795821 -> -3561112927.6341212013060271723005
subx3202 subtract -622903030605.2867503937836507326 6519388607.1331855704471328795821 -> -629422419212.41993596423078361218 Inexact Rounded
addx3203 add -5675915.2465457487632250245209054 73913909880.381367895205086027416 -> 73908233965.134822146441861002895 Inexact Rounded
comx3203 compare -5675915.2465457487632250245209054 73913909880.381367895205086027416 -> -1
divx3203 divide -5675915.2465457487632250245209054 73913909880.381367895205086027416 -> -0.000076790894376056827552388054657082 Inexact Rounded
dvix3203 divideint -5675915.2465457487632250245209054 73913909880.381367895205086027416 -> -0
mulx3203 multiply -5675915.2465457487632250245209054 73913909880.381367895205086027416 -> -419529088021865067.23307352973589 Inexact Rounded
powx3203 power -5675915.2465457487632250245209054 7 -> -1.8978038060207777231389234721908E+47 Inexact Rounded
remx3203 remainder -5675915.2465457487632250245209054 73913909880.381367895205086027416 -> -5675915.2465457487632250245209054
subx3203 subtract -5675915.2465457487632250245209054 73913909880.381367895205086027416 -> -73919585795.627913643968311051937 Inexact Rounded
addx3204 add 97.647321172555144900685605318635 4.8620911587547548751209841570885 -> 102.50941233130989977580658947572 Inexact Rounded
comx3204 compare 97.647321172555144900685605318635 4.8620911587547548751209841570885 -> 1
divx3204 divide 97.647321172555144900685605318635 4.8620911587547548751209841570885 -> 20.083399916665466374741708949621 Inexact Rounded
dvix3204 divideint 97.647321172555144900685605318635 4.8620911587547548751209841570885 -> 20
mulx3204 multiply 97.647321172555144900685605318635 4.8620911587547548751209841570885 -> 474.77017694916635398652276042175 Inexact Rounded
powx3204 power 97.647321172555144900685605318635 5 -> 8877724578.7935312939231828719842 Inexact Rounded
remx3204 remainder 97.647321172555144900685605318635 4.8620911587547548751209841570885 -> 0.4054979974600473982659221768650
subx3204 subtract 97.647321172555144900685605318635 4.8620911587547548751209841570885 -> 92.785230013800390025564621161547 Inexact Rounded
addx3205 add -9717253267024.5380651435435603552 -2669.2539695193820424002013488480E+363 -> -2.6692539695193820424002013488480E+366 Inexact Rounded
comx3205 compare -9717253267024.5380651435435603552 -2669.2539695193820424002013488480E+363 -> 1
divx3205 divide -9717253267024.5380651435435603552 -2669.2539695193820424002013488480E+363 -> 3.6404378818903462695633337631098E-354 Inexact Rounded
dvix3205 divideint -9717253267024.5380651435435603552 -2669.2539695193820424002013488480E+363 -> 0
mulx3205 multiply -9717253267024.5380651435435603552 -2669.2539695193820424002013488480E+363 -> 2.5937816855830431899123217912144E+379 Inexact Rounded
powx3205 power -9717253267024.5380651435435603552 -3 -> -1.0898567880085337780041328661330E-39 Inexact Rounded
remx3205 remainder -9717253267024.5380651435435603552 -2669.2539695193820424002013488480E+363 -> -9717253267024.5380651435435603552
subx3205 subtract -9717253267024.5380651435435603552 -2669.2539695193820424002013488480E+363 -> 2.6692539695193820424002013488480E+366 Inexact Rounded
addx3206 add -4.0817391717190128506083001702335E-767 12772.807105920712660991033689206 -> 12772.807105920712660991033689206 Inexact Rounded
comx3206 compare -4.0817391717190128506083001702335E-767 12772.807105920712660991033689206 -> -1
divx3206 divide -4.0817391717190128506083001702335E-767 12772.807105920712660991033689206 -> -3.1956477052150593175206769891434E-771 Inexact Rounded
dvix3206 divideint -4.0817391717190128506083001702335E-767 12772.807105920712660991033689206 -> -0
mulx3206 multiply -4.0817391717190128506083001702335E-767 12772.807105920712660991033689206 -> -5.2135267097047531336100750110314E-763 Inexact Rounded
powx3206 power -4.0817391717190128506083001702335E-767 12773 -> -0E-10030 Underflow Subnormal Inexact Rounded Clamped
remx3206 remainder -4.0817391717190128506083001702335E-767 12772.807105920712660991033689206 -> -4.0817391717190128506083001702335E-767
subx3206 subtract -4.0817391717190128506083001702335E-767 12772.807105920712660991033689206 -> -12772.807105920712660991033689206 Inexact Rounded
addx3207 add 68625322655934146845003028928647 -59.634169944840280159782488098700 -> 68625322655934146845003028928587 Inexact Rounded
comx3207 compare 68625322655934146845003028928647 -59.634169944840280159782488098700 -> 1
divx3207 divide 68625322655934146845003028928647 -59.634169944840280159782488098700 -> -1150771826276954946844322988192.5 Inexact Rounded
dvix3207 divideint 68625322655934146845003028928647 -59.634169944840280159782488098700 -> -1150771826276954946844322988192
mulx3207 multiply 68625322655934146845003028928647 -59.634169944840280159782488098700 -> -4.0924141537834748501140151997778E+33 Inexact Rounded
powx3207 power 68625322655934146845003028928647 -60 -> 6.4704731111943370171711131942603E-1911 Inexact Rounded
remx3207 remainder 68625322655934146845003028928647 -59.634169944840280159782488098700 -> 28.201254004897257552939369449600
subx3207 subtract 68625322655934146845003028928647 -59.634169944840280159782488098700 -> 68625322655934146845003028928707 Inexact Rounded
addx3208 add 732515.76532049290815665856727641 -92134479835821.319619827023729829 -> -92134479103305.554299334115573170 Inexact Rounded
comx3208 compare 732515.76532049290815665856727641 -92134479835821.319619827023729829 -> 1
divx3208 divide 732515.76532049290815665856727641 -92134479835821.319619827023729829 -> -7.9505063318943846655593887991914E-9 Inexact Rounded
dvix3208 divideint 732515.76532049290815665856727641 -92134479835821.319619827023729829 -> -0
mulx3208 multiply 732515.76532049290815665856727641 -92134479835821.319619827023729829 -> -67489959009342175728.710494356322 Inexact Rounded
powx3208 power 732515.76532049290815665856727641 -9 -> 1.6468241050443471359358016585877E-53 Inexact Rounded
remx3208 remainder 732515.76532049290815665856727641 -92134479835821.319619827023729829 -> 732515.76532049290815665856727641
subx3208 subtract 732515.76532049290815665856727641 -92134479835821.319619827023729829 -> 92134480568337.084940319931886488 Inexact Rounded
addx3209 add -30.458011942978338421676454778733 -5023372024597665102336430410403E+831 -> -5.0233720245976651023364304104030E+861 Inexact Rounded
comx3209 compare -30.458011942978338421676454778733 -5023372024597665102336430410403E+831 -> 1
divx3209 divide -30.458011942978338421676454778733 -5023372024597665102336430410403E+831 -> 6.0632602550311410821483001305010E-861 Inexact Rounded
dvix3209 divideint -30.458011942978338421676454778733 -5023372024597665102336430410403E+831 -> 0
mulx3209 multiply -30.458011942978338421676454778733 -5023372024597665102336430410403E+831 -> 1.5300192511921895929031818638961E+863 Inexact Rounded
powx3209 power -30.458011942978338421676454778733 -5 -> -3.8149797481405136042487643253109E-8 Inexact Rounded
remx3209 remainder -30.458011942978338421676454778733 -5023372024597665102336430410403E+831 -> -30.458011942978338421676454778733
subx3209 subtract -30.458011942978338421676454778733 -5023372024597665102336430410403E+831 -> 5.0233720245976651023364304104030E+861 Inexact Rounded
addx3210 add -89640.094149414644660480286430462 -58703419758.800889903227509215474 -> -58703509398.895039317872169695760 Inexact Rounded
comx3210 compare -89640.094149414644660480286430462 -58703419758.800889903227509215474 -> 1
divx3210 divide -89640.094149414644660480286430462 -58703419758.800889903227509215474 -> 0.0000015269995260536025237167199970238 Inexact Rounded
dvix3210 divideint -89640.094149414644660480286430462 -58703419758.800889903227509215474 -> 0
mulx3210 multiply -89640.094149414644660480286430462 -58703419758.800889903227509215474 -> 5262180074071519.7018252171579753 Inexact Rounded
powx3210 power -89640.094149414644660480286430462 -6 -> 1.9274635591165405888724595165741E-30 Inexact Rounded
remx3210 remainder -89640.094149414644660480286430462 -58703419758.800889903227509215474 -> -89640.094149414644660480286430462
subx3210 subtract -89640.094149414644660480286430462 -58703419758.800889903227509215474 -> 58703330118.706740488582848735188 Inexact Rounded
addx3211 add 458653.1567870081810052917714259 18353106238.516235116080449814053E-038 -> 458653.15678700818100529177142590 Inexact Rounded
comx3211 compare 458653.1567870081810052917714259 18353106238.516235116080449814053E-038 -> 1
divx3211 divide 458653.1567870081810052917714259 18353106238.516235116080449814053E-038 -> 2.4990492117594160215641311760498E+33 Inexact Rounded
dvix3211 divideint 458653.1567870081810052917714259 18353106238.516235116080449814053E-038 -> NaN Division_impossible
mulx3211 multiply 458653.1567870081810052917714259 18353106238.516235116080449814053E-038 -> 8.4177101131428047497998594379593E-23 Inexact Rounded
powx3211 power 458653.1567870081810052917714259 2 -> 210362718230.68790865117452429990 Inexact Rounded
remx3211 remainder 458653.1567870081810052917714259 18353106238.516235116080449814053E-038 -> NaN Division_impossible
subx3211 subtract 458653.1567870081810052917714259 18353106238.516235116080449814053E-038 -> 458653.15678700818100529177142590 Inexact Rounded
addx3212 add 913391.42744224458216174967853722 -21051638.816432817393202262710630E+432 -> -2.1051638816432817393202262710630E+439 Inexact Rounded
comx3212 compare 913391.42744224458216174967853722 -21051638.816432817393202262710630E+432 -> 1
divx3212 divide 913391.42744224458216174967853722 -21051638.816432817393202262710630E+432 -> -4.3388138824102151127273259092613E-434 Inexact Rounded
dvix3212 divideint 913391.42744224458216174967853722 -21051638.816432817393202262710630E+432 -> -0
mulx3212 multiply 913391.42744224458216174967853722 -21051638.816432817393202262710630E+432 -> -1.9228386428540135340600836707270E+445 Inexact Rounded
powx3212 power 913391.42744224458216174967853722 -2 -> 1.1986327439971532470297300128074E-12 Inexact Rounded
remx3212 remainder 913391.42744224458216174967853722 -21051638.816432817393202262710630E+432 -> 913391.42744224458216174967853722
subx3212 subtract 913391.42744224458216174967853722 -21051638.816432817393202262710630E+432 -> 2.1051638816432817393202262710630E+439 Inexact Rounded
addx3213 add -917591456829.12109027484399536567 -28892177726858026955513438843371E+708 -> -2.8892177726858026955513438843371E+739 Inexact Rounded
comx3213 compare -917591456829.12109027484399536567 -28892177726858026955513438843371E+708 -> 1
divx3213 divide -917591456829.12109027484399536567 -28892177726858026955513438843371E+708 -> 3.1759165595057674196644927106447E-728 Inexact Rounded
dvix3213 divideint -917591456829.12109027484399536567 -28892177726858026955513438843371E+708 -> 0
mulx3213 multiply -917591456829.12109027484399536567 -28892177726858026955513438843371E+708 -> 2.6511215451353541156703914721725E+751 Inexact Rounded
powx3213 power -917591456829.12109027484399536567 -3 -> -1.2943505591853739240003453341911E-36 Inexact Rounded
remx3213 remainder -917591456829.12109027484399536567 -28892177726858026955513438843371E+708 -> -917591456829.12109027484399536567
subx3213 subtract -917591456829.12109027484399536567 -28892177726858026955513438843371E+708 -> 2.8892177726858026955513438843371E+739 Inexact Rounded
addx3214 add 34938410840645.913399699219228218 30.818220393242402846077755480548 -> 34938410840676.731620092461631064 Inexact Rounded
comx3214 compare 34938410840645.913399699219228218 30.818220393242402846077755480548 -> 1
divx3214 divide 34938410840645.913399699219228218 30.818220393242402846077755480548 -> 1133693327999.7879503260098666966 Inexact Rounded
dvix3214 divideint 34938410840645.913399699219228218 30.818220393242402846077755480548 -> 1133693327999
mulx3214 multiply 34938410840645.913399699219228218 30.818220393242402846077755480548 -> 1076739645476675.3318519289128961 Inexact Rounded
powx3214 power 34938410840645.913399699219228218 31 -> 6.9566085958798732786509909683267E+419 Inexact Rounded
remx3214 remainder 34938410840645.913399699219228218 30.818220393242402846077755480548 -> 24.283226805899273551376371736548
subx3214 subtract 34938410840645.913399699219228218 30.818220393242402846077755480548 -> 34938410840615.095179305976825372 Inexact Rounded
addx3215 add 6034.7374411022598081745006769023E-517 29771833428054709077850588904653 -> 29771833428054709077850588904653 Inexact Rounded
comx3215 compare 6034.7374411022598081745006769023E-517 29771833428054709077850588904653 -> -1
divx3215 divide 6034.7374411022598081745006769023E-517 29771833428054709077850588904653 -> 2.0269955680376683526099444523691E-545 Inexact Rounded
dvix3215 divideint 6034.7374411022598081745006769023E-517 29771833428054709077850588904653 -> 0
mulx3215 multiply 6034.7374411022598081745006769023E-517 29771833428054709077850588904653 -> 1.7966519787854159464382359411642E-482 Inexact Rounded
powx3215 power 6034.7374411022598081745006769023E-517 3 -> 2.1977340597301840681528507640032E-1540 Inexact Rounded
remx3215 remainder 6034.7374411022598081745006769023E-517 29771833428054709077850588904653 -> 6.0347374411022598081745006769023E-514
subx3215 subtract 6034.7374411022598081745006769023E-517 29771833428054709077850588904653 -> -29771833428054709077850588904653 Inexact Rounded
addx3216 add -5565747671734.1686009705574503152 -490.30899494881071282787487030303 -> -5565747672224.4775959193681631431 Inexact Rounded
comx3216 compare -5565747671734.1686009705574503152 -490.30899494881071282787487030303 -> -1
divx3216 divide -5565747671734.1686009705574503152 -490.30899494881071282787487030303 -> 11351510433.365074871574519756245 Inexact Rounded
dvix3216 divideint -5565747671734.1686009705574503152 -490.30899494881071282787487030303 -> 11351510433
mulx3216 multiply -5565747671734.1686009705574503152 -490.30899494881071282787487030303 -> 2728936147066663.4580064428639745 Inexact Rounded
powx3216 power -5565747671734.1686009705574503152 -490 -> 4.9371745297619526113991728953197E-6246 Inexact Rounded
remx3216 remainder -5565747671734.1686009705574503152 -490.30899494881071282787487030303 -> -178.99949336276892685183308348801
subx3216 subtract -5565747671734.1686009705574503152 -490.30899494881071282787487030303 -> -5565747671243.8596060217467374873 Inexact Rounded
addx3217 add 319545511.89203495546689273564728E+036 -2955943533943321899150310192061 -> 3.1954551189203199952335879232538E+44 Inexact Rounded
comx3217 compare 319545511.89203495546689273564728E+036 -2955943533943321899150310192061 -> 1
divx3217 divide 319545511.89203495546689273564728E+036 -2955943533943321899150310192061 -> -108102711781422.68663084859902931 Inexact Rounded
dvix3217 divideint 319545511.89203495546689273564728E+036 -2955943533943321899150310192061 -> -108102711781422
mulx3217 multiply 319545511.89203495546689273564728E+036 -2955943533943321899150310192061 -> -9.4455848967786959996525702197139E+74 Inexact Rounded
powx3217 power 319545511.89203495546689273564728E+036 -3 -> 3.0647978448946294457985223953472E-134 Inexact Rounded
remx3217 remainder 319545511.89203495546689273564728E+036 -2955943533943321899150310192061 -> 2029642017122316721531728309258
subx3217 subtract 319545511.89203495546689273564728E+036 -2955943533943321899150310192061 -> 3.1954551189203791141042667896918E+44 Inexact Rounded
addx3218 add -36852134.84194296250843579428931 -5830629.8347085025808716360357940 -> -42682764.676651465089307430325104 Rounded
comx3218 compare -36852134.84194296250843579428931 -5830629.8347085025808716360357940 -> -1
divx3218 divide -36852134.84194296250843579428931 -5830629.8347085025808716360357940 -> 6.3204380807318655475459047410160 Inexact Rounded
dvix3218 divideint -36852134.84194296250843579428931 -5830629.8347085025808716360357940 -> 6
mulx3218 multiply -36852134.84194296250843579428931 -5830629.8347085025808716360357940 -> 214871156882133.34437417534873098 Inexact Rounded
powx3218 power -36852134.84194296250843579428931 -5830630 -> 0E-10030 Underflow Subnormal Inexact Rounded Clamped
remx3218 remainder -36852134.84194296250843579428931 -5830629.8347085025808716360357940 -> -1868355.8336919470232059780745460
subx3218 subtract -36852134.84194296250843579428931 -5830629.8347085025808716360357940 -> -31021505.007234459927564158253516 Rounded
addx3219 add 8.6021905001798578659275880018221E-374 -39505285344943.729681835377530908 -> -39505285344943.729681835377530908 Inexact Rounded
comx3219 compare 8.6021905001798578659275880018221E-374 -39505285344943.729681835377530908 -> 1
divx3219 divide 8.6021905001798578659275880018221E-374 -39505285344943.729681835377530908 -> -2.1774783867700502002511486885272E-387 Inexact Rounded
dvix3219 divideint 8.6021905001798578659275880018221E-374 -39505285344943.729681835377530908 -> -0
mulx3219 multiply 8.6021905001798578659275880018221E-374 -39505285344943.729681835377530908 -> -3.3983199030116951081865430362053E-360 Inexact Rounded
powx3219 power 8.6021905001798578659275880018221E-374 -4 -> 1.8262649155820433126240754123257E+1492 Inexact Rounded
remx3219 remainder 8.6021905001798578659275880018221E-374 -39505285344943.729681835377530908 -> 8.6021905001798578659275880018221E-374
subx3219 subtract 8.6021905001798578659275880018221E-374 -39505285344943.729681835377530908 -> 39505285344943.729681835377530908 Inexact Rounded
addx3220 add -54863165.152174109720312887805017 736.1398476560169141105328256628 -> -54862429.012326453703398777272191 Inexact Rounded
comx3220 compare -54863165.152174109720312887805017 736.1398476560169141105328256628 -> -1
divx3220 divide -54863165.152174109720312887805017 736.1398476560169141105328256628 -> -74528.182826764384088602813142847 Inexact Rounded
dvix3220 divideint -54863165.152174109720312887805017 736.1398476560169141105328256628 -> -74528
mulx3220 multiply -54863165.152174109720312887805017 736.1398476560169141105328256628 -> -40386962037.048345148338122539405 Inexact Rounded
powx3220 power -54863165.152174109720312887805017 736 -> 1.2903643981679111625370174573639E+5696 Inexact Rounded
remx3220 remainder -54863165.152174109720312887805017 736.1398476560169141105328256628 -> -134.5860664811454830973740198416
subx3220 subtract -54863165.152174109720312887805017 736.1398476560169141105328256628 -> -54863901.292021765737226998337843 Inexact Rounded
addx3221 add -3263743464517851012531708810307 2457206.2471248382136273643208109 -> -3263743464517851012531706353100.8 Inexact Rounded
comx3221 compare -3263743464517851012531708810307 2457206.2471248382136273643208109 -> -1
divx3221 divide -3263743464517851012531708810307 2457206.2471248382136273643208109 -> -1328233422952076975055082.5768082 Inexact Rounded
dvix3221 divideint -3263743464517851012531708810307 2457206.2471248382136273643208109 -> -1328233422952076975055082
mulx3221 multiply -3263743464517851012531708810307 2457206.2471248382136273643208109 -> -8.0196908300261262548565838031943E+36 Inexact Rounded
powx3221 power -3263743464517851012531708810307 2457206 -> Infinity Overflow Inexact Rounded
remx3221 remainder -3263743464517851012531708810307 2457206.2471248382136273643208109 -> -1417336.7573398366062994535940062
subx3221 subtract -3263743464517851012531708810307 2457206.2471248382136273643208109 -> -3263743464517851012531711267513.2 Inexact Rounded
addx3222 add 2856586744.0548637797291151154902E-895 953545637646.57694835860339582821E+080 -> 9.5354563764657694835860339582821E+91 Inexact Rounded
comx3222 compare 2856586744.0548637797291151154902E-895 953545637646.57694835860339582821E+080 -> -1
divx3222 divide 2856586744.0548637797291151154902E-895 953545637646.57694835860339582821E+080 -> 2.9957525170007980008712828968300E-978 Inexact Rounded
dvix3222 divideint 2856586744.0548637797291151154902E-895 953545637646.57694835860339582821E+080 -> 0
mulx3222 multiply 2856586744.0548637797291151154902E-895 953545637646.57694835860339582821E+080 -> 2.7238858283525541854826594343954E-794 Inexact Rounded
powx3222 power 2856586744.0548637797291151154902E-895 10 -> 3.6180466753307072256807593988336E-8856 Inexact Rounded
remx3222 remainder 2856586744.0548637797291151154902E-895 953545637646.57694835860339582821E+080 -> 2.8565867440548637797291151154902E-886
subx3222 subtract 2856586744.0548637797291151154902E-895 953545637646.57694835860339582821E+080 -> -9.5354563764657694835860339582821E+91 Inexact Rounded
addx3223 add 5624157233.3433661009203529937625 626098409265.93738586750090160638 -> 631722566499.28075196842125460014 Inexact Rounded
comx3223 compare 5624157233.3433661009203529937625 626098409265.93738586750090160638 -> -1
divx3223 divide 5624157233.3433661009203529937625 626098409265.93738586750090160638 -> 0.0089828645946207580492752544218316 Inexact Rounded
dvix3223 divideint 5624157233.3433661009203529937625 626098409265.93738586750090160638 -> 0
mulx3223 multiply 5624157233.3433661009203529937625 626098409265.93738586750090160638 -> 3521275897257796938833.8975037909 Inexact Rounded
powx3223 power 5624157233.3433661009203529937625 6 -> 3.1647887196303262540158328459030E+58 Inexact Rounded
remx3223 remainder 5624157233.3433661009203529937625 626098409265.93738586750090160638 -> 5624157233.3433661009203529937625
subx3223 subtract 5624157233.3433661009203529937625 626098409265.93738586750090160638 -> -620474252032.59401976658054861262 Inexact Rounded
addx3224 add -213499362.91476998701834067092611 419272438.02555757699863022643444 -> 205773075.11078758998028955550833
comx3224 compare -213499362.91476998701834067092611 419272438.02555757699863022643444 -> -1
divx3224 divide -213499362.91476998701834067092611 419272438.02555757699863022643444 -> -0.50921392286166855779828061147786 Inexact Rounded
dvix3224 divideint -213499362.91476998701834067092611 419272438.02555757699863022643444 -> -0
mulx3224 multiply -213499362.91476998701834067092611 419272438.02555757699863022643444 -> -89514398406178925.073260776410672 Inexact Rounded
powx3224 power -213499362.91476998701834067092611 419272438 -> Infinity Overflow Inexact Rounded
remx3224 remainder -213499362.91476998701834067092611 419272438.02555757699863022643444 -> -213499362.91476998701834067092611
subx3224 subtract -213499362.91476998701834067092611 419272438.02555757699863022643444 -> -632771800.94032756401697089736055
addx3225 add 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 30274.392356614101238316845401518 Inexact Rounded
comx3225 compare 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 1
divx3225 divide 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 6300.1252178837655359831527173832 Inexact Rounded
dvix3225 divideint 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 6300
mulx3225 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.29080119336967191651199283 Inexact Rounded
powx3225 power 30269.587755640502150977251770554 5 -> 25411630481547464128383.220368208 Inexact Rounded
remx3225 remainder 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 0.6016219662519115373766970119100
subx3225 subtract 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 30264.783154666903063637658139590 Inexact Rounded
addx3226 add 47.525676459351505682005359699680E+704 -58631943508436657383369760970210 -> 4.7525676459351505682005359699680E+705 Inexact Rounded
comx3226 compare 47.525676459351505682005359699680E+704 -58631943508436657383369760970210 -> 1
divx3226 divide 47.525676459351505682005359699680E+704 -58631943508436657383369760970210 -> -8.1057651538555854520994438038537E+673 Inexact Rounded
dvix3226 divideint 47.525676459351505682005359699680E+704 -58631943508436657383369760970210 -> NaN Division_impossible
mulx3226 multiply 47.525676459351505682005359699680E+704 -58631943508436657383369760970210 -> -2.7865227773649353769876975366506E+737 Inexact Rounded
powx3226 power 47.525676459351505682005359699680E+704 -6 -> 8.6782100393941226535150385475464E-4235 Inexact Rounded
remx3226 remainder 47.525676459351505682005359699680E+704 -58631943508436657383369760970210 -> NaN Division_impossible
subx3226 subtract 47.525676459351505682005359699680E+704 -58631943508436657383369760970210 -> 4.7525676459351505682005359699680E+705 Inexact Rounded
addx3227 add -74396862273800.625679130772935550 -115616605.52826981284183992013157 -> -74396977890406.153948943614775470 Inexact Rounded
comx3227 compare -74396862273800.625679130772935550 -115616605.52826981284183992013157 -> -1
divx3227 divide -74396862273800.625679130772935550 -115616605.52826981284183992013157 -> 643479.03948459716424778005613112 Inexact Rounded
dvix3227 divideint -74396862273800.625679130772935550 -115616605.52826981284183992013157 -> 643479
mulx3227 multiply -74396862273800.625679130772935550 -115616605.52826981284183992013157 -> 8601512678051025297297.7169654467 Inexact Rounded
powx3227 power -74396862273800.625679130772935550 -115616606 -> 0E-10030 Underflow Subnormal Inexact Rounded Clamped
remx3227 remainder -74396862273800.625679130772935550 -115616605.52826981284183992013157 -> -4565075.09478147646296920746797
subx3227 subtract -74396862273800.625679130772935550 -115616605.52826981284183992013157 -> -74396746657195.097409317931095630 Inexact Rounded
addx3228 add 67585560.562561229497720955705979 826.96290288608566737177503451613 -> 67586387.525464115583388327481014 Inexact Rounded
comx3228 compare 67585560.562561229497720955705979 826.96290288608566737177503451613 -> 1
divx3228 divide 67585560.562561229497720955705979 826.96290288608566737177503451613 -> 81727.439437354248789852715586510 Inexact Rounded
dvix3228 divideint 67585560.562561229497720955705979 826.96290288608566737177503451613 -> 81727
mulx3228 multiply 67585560.562561229497720955705979 826.96290288608566737177503451613 -> 55890751355.998983433895910295596 Inexact Rounded
powx3228 power 67585560.562561229497720955705979 827 -> 1.9462204583352191108781197788255E+6475 Inexact Rounded
remx3228 remainder 67585560.562561229497720955705979 826.96290288608566737177503451613 -> 363.39839010616042789746007924349
subx3228 subtract 67585560.562561229497720955705979 826.96290288608566737177503451613 -> 67584733.599658343412053583930944 Inexact Rounded
addx3229 add 6877386868.9498051860742298735156E-232 390.3154289860643509393769754551 -> 390.31542898606435093937697545510 Inexact Rounded
comx3229 compare 6877386868.9498051860742298735156E-232 390.3154289860643509393769754551 -> -1
divx3229 divide 6877386868.9498051860742298735156E-232 390.3154289860643509393769754551 -> 1.7620074325054038174571564409871E-225 Inexact Rounded
dvix3229 divideint 6877386868.9498051860742298735156E-232 390.3154289860643509393769754551 -> 0
mulx3229 multiply 6877386868.9498051860742298735156E-232 390.3154289860643509393769754551 -> 2.6843502060572691408091663822732E-220 Inexact Rounded
powx3229 power 6877386868.9498051860742298735156E-232 390 -> 0E-10030 Underflow Subnormal Inexact Rounded Clamped
remx3229 remainder 6877386868.9498051860742298735156E-232 390.3154289860643509393769754551 -> 6.8773868689498051860742298735156E-223
subx3229 subtract 6877386868.9498051860742298735156E-232 390.3154289860643509393769754551 -> -390.31542898606435093937697545510 Inexact Rounded
addx3230 add -1647335.201144609256134925838937 -186654823782.50459802235024230856 -> -186656471117.70574263160637723440 Inexact Rounded
comx3230 compare -1647335.201144609256134925838937 -186654823782.50459802235024230856 -> 1
divx3230 divide -1647335.201144609256134925838937 -186654823782.50459802235024230856 -> 0.0000088255699357876233458660331146583 Inexact Rounded
dvix3230 divideint -1647335.201144609256134925838937 -186654823782.50459802235024230856 -> 0
mulx3230 multiply -1647335.201144609256134925838937 -186654823782.50459802235024230856 -> 307483061680363807.48775619333285 Inexact Rounded
powx3230 power -1647335.201144609256134925838937 -2 -> 3.6849876990439502152784389237891E-13 Inexact Rounded
remx3230 remainder -1647335.201144609256134925838937 -186654823782.50459802235024230856 -> -1647335.201144609256134925838937
subx3230 subtract -1647335.201144609256134925838937 -186654823782.50459802235024230856 -> 186653176447.30345341309410738272 Inexact Rounded
addx3231 add 41407818140948.866630923934138155 -5156.7624534000311342310106671627E-963 -> 41407818140948.866630923934138155 Inexact Rounded
comx3231 compare 41407818140948.866630923934138155 -5156.7624534000311342310106671627E-963 -> 1
divx3231 divide 41407818140948.866630923934138155 -5156.7624534000311342310106671627E-963 -> -8.0298091128179204076796507697517E+972 Inexact Rounded
dvix3231 divideint 41407818140948.866630923934138155 -5156.7624534000311342310106671627E-963 -> NaN Division_impossible
mulx3231 multiply 41407818140948.866630923934138155 -5156.7624534000311342310106671627E-963 -> -2.1353028186646179369220834691156E-946 Inexact Rounded
powx3231 power 41407818140948.866630923934138155 -5 -> 8.2146348556648547525693528004081E-69 Inexact Rounded
remx3231 remainder 41407818140948.866630923934138155 -5156.7624534000311342310106671627E-963 -> NaN Division_impossible
subx3231 subtract 41407818140948.866630923934138155 -5156.7624534000311342310106671627E-963 -> 41407818140948.866630923934138155 Inexact Rounded
addx3232 add -6.6547424012516834662011706165297 -574454585580.06215974139884746441 -> -574454585586.71690214265053093061 Inexact Rounded
comx3232 compare -6.6547424012516834662011706165297 -574454585580.06215974139884746441 -> 1
divx3232 divide -6.6547424012516834662011706165297 -574454585580.06215974139884746441 -> 1.1584453442097568745411568037078E-11 Inexact Rounded
dvix3232 divideint -6.6547424012516834662011706165297 -574454585580.06215974139884746441 -> 0
mulx3232 multiply -6.6547424012516834662011706165297 -574454585580.06215974139884746441 -> 3822847288253.1035559206691532826 Inexact Rounded
powx3232 power -6.6547424012516834662011706165297 -6 -> 0.000011513636283388791488128239232906 Inexact Rounded
remx3232 remainder -6.6547424012516834662011706165297 -574454585580.06215974139884746441 -> -6.6547424012516834662011706165297
subx3232 subtract -6.6547424012516834662011706165297 -574454585580.06215974139884746441 -> 574454585573.40741734014716399821 Inexact Rounded
addx3233 add -27627.758745381267780885067447671 -23385972189441.709586433758111062 -> -23385972217069.468331815025891947 Inexact Rounded
comx3233 compare -27627.758745381267780885067447671 -23385972189441.709586433758111062 -> 1
divx3233 divide -27627.758745381267780885067447671 -23385972189441.709586433758111062 -> 1.1813816642548920194709898111624E-9 Inexact Rounded
dvix3233 divideint -27627.758745381267780885067447671 -23385972189441.709586433758111062 -> 0
mulx3233 multiply -27627.758745381267780885067447671 -23385972189441.709586433758111062 -> 646101997676091306.41485393678655 Inexact Rounded
powx3233 power -27627.758745381267780885067447671 -2 -> 1.3101128009560812529198521922269E-9 Inexact Rounded
remx3233 remainder -27627.758745381267780885067447671 -23385972189441.709586433758111062 -> -27627.758745381267780885067447671
subx3233 subtract -27627.758745381267780885067447671 -23385972189441.709586433758111062 -> 23385972161813.950841052490330177 Inexact Rounded
addx3234 add 209819.74379099914752963711944307E-228 -440230.6700989532467831370320266E-960 -> 2.0981974379099914752963711944307E-223 Inexact Rounded
comx3234 compare 209819.74379099914752963711944307E-228 -440230.6700989532467831370320266E-960 -> 1
divx3234 divide 209819.74379099914752963711944307E-228 -440230.6700989532467831370320266E-960 -> -4.7661318949867060595545765053187E+731 Inexact Rounded
dvix3234 divideint 209819.74379099914752963711944307E-228 -440230.6700989532467831370320266E-960 -> NaN Division_impossible
mulx3234 multiply 209819.74379099914752963711944307E-228 -440230.6700989532467831370320266E-960 -> -9.2369086409102239573726316593648E-1178 Inexact Rounded
powx3234 power 209819.74379099914752963711944307E-228 -4 -> 5.1595828494111690910650919776705E+890 Inexact Rounded
remx3234 remainder 209819.74379099914752963711944307E-228 -440230.6700989532467831370320266E-960 -> NaN Division_impossible
subx3234 subtract 209819.74379099914752963711944307E-228 -440230.6700989532467831370320266E-960 -> 2.0981974379099914752963711944307E-223 Inexact Rounded
addx3235 add 2.3488457600415474270259330865184 9182434.6660212482500376220508605E-612 -> 2.3488457600415474270259330865184 Inexact Rounded
comx3235 compare 2.3488457600415474270259330865184 9182434.6660212482500376220508605E-612 -> 1
divx3235 divide 2.3488457600415474270259330865184 9182434.6660212482500376220508605E-612 -> 2.5579771002708402753412266574941E+605 Inexact Rounded
dvix3235 divideint 2.3488457600415474270259330865184 9182434.6660212482500376220508605E-612 -> NaN Division_impossible
mulx3235 multiply 2.3488457600415474270259330865184 9182434.6660212482500376220508605E-612 -> 2.1568122732142531556215204459407E-605 Inexact Rounded
powx3235 power 2.3488457600415474270259330865184 9 -> 2176.1583446147511579113022622255 Inexact Rounded
remx3235 remainder 2.3488457600415474270259330865184 9182434.6660212482500376220508605E-612 -> NaN Division_impossible
subx3235 subtract 2.3488457600415474270259330865184 9182434.6660212482500376220508605E-612 -> 2.3488457600415474270259330865184 Inexact Rounded
addx3236 add -5107586300197.9703941034404557409 56609.05486055057838678039496686E-768 -> -5107586300197.9703941034404557409 Inexact Rounded
comx3236 compare -5107586300197.9703941034404557409 56609.05486055057838678039496686E-768 -> -1
divx3236 divide -5107586300197.9703941034404557409 56609.05486055057838678039496686E-768 -> -9.0225606358909877855326357402242E+775 Inexact Rounded
dvix3236 divideint -5107586300197.9703941034404557409 56609.05486055057838678039496686E-768 -> NaN Division_impossible
mulx3236 multiply -5107586300197.9703941034404557409 56609.05486055057838678039496686E-768 -> -2.8913563307290346152596212593532E-751 Inexact Rounded
powx3236 power -5107586300197.9703941034404557409 6 -> 1.7753920894188022125919559565029E+76 Inexact Rounded
remx3236 remainder -5107586300197.9703941034404557409 56609.05486055057838678039496686E-768 -> NaN Division_impossible
subx3236 subtract -5107586300197.9703941034404557409 56609.05486055057838678039496686E-768 -> -5107586300197.9703941034404557409 Inexact Rounded
addx3237 add -70454070095869.70717871212601390 -6200178.370249260009168888392406 -> -70454076296048.077427972135182788 Inexact Rounded
comx3237 compare -70454070095869.70717871212601390 -6200178.370249260009168888392406 -> -1
divx3237 divide -70454070095869.70717871212601390 -6200178.370249260009168888392406 -> 11363232.779549422490548997517194 Inexact Rounded
dvix3237 divideint -70454070095869.70717871212601390 -6200178.370249260009168888392406 -> 11363232
mulx3237 multiply -70454070095869.70717871212601390 -6200178.370249260009168888392406 -> 436827801504436566945.76663687924 Inexact Rounded
powx3237 power -70454070095869.70717871212601390 -6200178 -> 0E-10030 Underflow Subnormal Inexact Rounded Clamped
remx3237 remainder -70454070095869.70717871212601390 -6200178.370249260009168888392406 -> -4833345.467866203920028883583808
subx3237 subtract -70454070095869.70717871212601390 -6200178.370249260009168888392406 -> -70454063895691.336929452116845012 Inexact Rounded
addx3238 add 29119.220621511046558757900645228 3517612.8810761470018676311863010E-222 -> 29119.220621511046558757900645228 Inexact Rounded
comx3238 compare 29119.220621511046558757900645228 3517612.8810761470018676311863010E-222 -> 1
divx3238 divide 29119.220621511046558757900645228 3517612.8810761470018676311863010E-222 -> 8.2781197380089684063239752337467E+219 Inexact Rounded
dvix3238 divideint 29119.220621511046558757900645228 3517612.8810761470018676311863010E-222 -> NaN Division_impossible
mulx3238 multiply 29119.220621511046558757900645228 3517612.8810761470018676311863010E-222 -> 1.0243014554512542440592768088600E-211 Inexact Rounded
powx3238 power 29119.220621511046558757900645228 4 -> 718983605328417461.32835984217255 Inexact Rounded
remx3238 remainder 29119.220621511046558757900645228 3517612.8810761470018676311863010E-222 -> NaN Division_impossible
subx3238 subtract 29119.220621511046558757900645228 3517612.8810761470018676311863010E-222 -> 29119.220621511046558757900645228 Inexact Rounded
addx3239 add -5168.2214111091132913776042214525 -5690274.0971173476527123568627720 -> -5695442.3185284567660037344669935 Inexact Rounded
comx3239 compare -5168.2214111091132913776042214525 -5690274.0971173476527123568627720 -> 1
divx3239 divide -5168.2214111091132913776042214525 -5690274.0971173476527123568627720 -> 0.00090825526554639915580539316714451 Inexact Rounded
dvix3239 divideint -5168.2214111091132913776042214525 -5690274.0971173476527123568627720 -> 0
mulx3239 multiply -5168.2214111091132913776042214525 -5690274.0971173476527123568627720 -> 29408596423.801454053855793898323 Inexact Rounded
powx3239 power -5168.2214111091132913776042214525 -5690274 -> 0E-10030 Underflow Subnormal Inexact Rounded Clamped
remx3239 remainder -5168.2214111091132913776042214525 -5690274.0971173476527123568627720 -> -5168.2214111091132913776042214525
subx3239 subtract -5168.2214111091132913776042214525 -5690274.0971173476527123568627720 -> 5685105.8757062385394209792585505 Inexact Rounded
addx3240 add 33783.060857197067391462144517964 -2070.0806959465088198508322815406 -> 31712.980161250558571611312236423 Inexact Rounded
comx3240 compare 33783.060857197067391462144517964 -2070.0806959465088198508322815406 -> 1
divx3240 divide 33783.060857197067391462144517964 -2070.0806959465088198508322815406 -> -16.319683055519892881394358449220 Inexact Rounded
dvix3240 divideint 33783.060857197067391462144517964 -2070.0806959465088198508322815406 -> -16
mulx3240 multiply 33783.060857197067391462144517964 -2070.0806959465088198508322815406 -> -69933662.130469766080574235843448 Inexact Rounded
powx3240 power 33783.060857197067391462144517964 -2070 -> 3.9181336001803008597293818984406E-9375 Inexact Rounded
remx3240 remainder 33783.060857197067391462144517964 -2070.0806959465088198508322815406 -> 661.7697220529262738488280133144
subx3240 subtract 33783.060857197067391462144517964 -2070.0806959465088198508322815406 -> 35853.141553143576211312976799505 Inexact Rounded
addx3241 add 42207435091050.840296353874733169E-905 73330633078.828216018536326743325E+976 -> 7.3330633078828216018536326743325E+986 Inexact Rounded
comx3241 compare 42207435091050.840296353874733169E-905 73330633078.828216018536326743325E+976 -> -1
divx3241 divide 42207435091050.840296353874733169E-905 73330633078.828216018536326743325E+976 -> 5.7557712676064206636178247554056E-1879 Inexact Rounded
dvix3241 divideint 42207435091050.840296353874733169E-905 73330633078.828216018536326743325E+976 -> 0
mulx3241 multiply 42207435091050.840296353874733169E-905 73330633078.828216018536326743325E+976 -> 3.0950979358603075650592433398939E+95 Inexact Rounded
powx3241 power 42207435091050.840296353874733169E-905 7 -> 2.3862872940615283599573082966642E-6240 Inexact Rounded
remx3241 remainder 42207435091050.840296353874733169E-905 73330633078.828216018536326743325E+976 -> 4.2207435091050840296353874733169E-892
subx3241 subtract 42207435091050.840296353874733169E-905 73330633078.828216018536326743325E+976 -> -7.3330633078828216018536326743325E+986 Inexact Rounded
addx3242 add -71800.806700868784841045406679641 -39617456964250697902519150526701 -> -39617456964250697902519150598502 Inexact Rounded
comx3242 compare -71800.806700868784841045406679641 -39617456964250697902519150526701 -> 1
divx3242 divide -71800.806700868784841045406679641 -39617456964250697902519150526701 -> 1.8123527405017220178579049964126E-27 Inexact Rounded
dvix3242 divideint -71800.806700868784841045406679641 -39617456964250697902519150526701 -> 0
mulx3242 multiply -71800.806700868784841045406679641 -39617456964250697902519150526701 -> 2.8445653694701522164901827524538E+36 Inexact Rounded
powx3242 power -71800.806700868784841045406679641 -4 -> 3.7625536850895480882178599428774E-20 Inexact Rounded
remx3242 remainder -71800.806700868784841045406679641 -39617456964250697902519150526701 -> -71800.806700868784841045406679641
subx3242 subtract -71800.806700868784841045406679641 -39617456964250697902519150526701 -> 39617456964250697902519150454900 Inexact Rounded
addx3243 add 53627480801.631504892310016062160 328259.56947661049313311983109503 -> 53627809061.200981502803149181991 Inexact Rounded
comx3243 compare 53627480801.631504892310016062160 328259.56947661049313311983109503 -> 1
divx3243 divide 53627480801.631504892310016062160 328259.56947661049313311983109503 -> 163369.13159039717901500465109839 Inexact Rounded
dvix3243 divideint 53627480801.631504892310016062160 328259.56947661049313311983109503 -> 163369
mulx3243 multiply 53627480801.631504892310016062160 328259.56947661049313311983109503 -> 17603733760058752.363123585224369 Inexact Rounded
powx3243 power 53627480801.631504892310016062160 328260 -> Infinity Overflow Inexact Rounded
remx3243 remainder 53627480801.631504892310016062160 328259.56947661049313311983109503 -> 43195.80712523964536237599604393
subx3243 subtract 53627480801.631504892310016062160 328259.56947661049313311983109503 -> 53627152542.062028281816882942329 Inexact Rounded
addx3244 add -5052601598.5559371338428368438728 -97855372.224321664785314782556064 -> -5150456970.7802587986281516264289 Inexact Rounded
comx3244 compare -5052601598.5559371338428368438728 -97855372.224321664785314782556064 -> -1
divx3244 divide -5052601598.5559371338428368438728 -97855372.224321664785314782556064 -> 51.633359351732432283879274192947 Inexact Rounded
dvix3244 divideint -5052601598.5559371338428368438728 -97855372.224321664785314782556064 -> 51
mulx3244 multiply -5052601598.5559371338428368438728 -97855372.224321664785314782556064 -> 494424210127893893.12581512954787 Inexact Rounded
powx3244 power -5052601598.5559371338428368438728 -97855372 -> 0E-10030 Underflow Subnormal Inexact Rounded Clamped
remx3244 remainder -5052601598.5559371338428368438728 -97855372.224321664785314782556064 -> -61977615.115532229791782933513536
subx3244 subtract -5052601598.5559371338428368438728 -97855372.224321664785314782556064 -> -4954746226.3316154690575220613167 Inexact Rounded
addx3245 add 4208134320733.7069742988228068191E+146 4270869.1760149477598920960628392E+471 -> 4.2708691760149477598920960628392E+477 Inexact Rounded
comx3245 compare 4208134320733.7069742988228068191E+146 4270869.1760149477598920960628392E+471 -> -1
divx3245 divide 4208134320733.7069742988228068191E+146 4270869.1760149477598920960628392E+471 -> 9.8531098643021951048744078027283E-320 Inexact Rounded
dvix3245 divideint 4208134320733.7069742988228068191E+146 4270869.1760149477598920960628392E+471 -> 0
mulx3245 multiply 4208134320733.7069742988228068191E+146 4270869.1760149477598920960628392E+471 -> 1.7972391158952189002169082753183E+636 Inexact Rounded
powx3245 power 4208134320733.7069742988228068191E+146 4 -> 3.1358723439830872127129821963857E+634 Inexact Rounded
remx3245 remainder 4208134320733.7069742988228068191E+146 4270869.1760149477598920960628392E+471 -> 4.2081343207337069742988228068191E+158
subx3245 subtract 4208134320733.7069742988228068191E+146 4270869.1760149477598920960628392E+471 -> -4.2708691760149477598920960628392E+477 Inexact Rounded
addx3246 add -8.5077009657942581515590471189084E+308 9652145155.374217047842114042376E-250 -> -8.5077009657942581515590471189084E+308 Inexact Rounded
comx3246 compare -8.5077009657942581515590471189084E+308 9652145155.374217047842114042376E-250 -> -1
divx3246 divide -8.5077009657942581515590471189084E+308 9652145155.374217047842114042376E-250 -> -8.8143110457236089978070419047970E+548 Inexact Rounded
dvix3246 divideint -8.5077009657942581515590471189084E+308 9652145155.374217047842114042376E-250 -> NaN Division_impossible
mulx3246 multiply -8.5077009657942581515590471189084E+308 9652145155.374217047842114042376E-250 -> -8.2117564660363596283732942091852E+68 Inexact Rounded
powx3246 power -8.5077009657942581515590471189084E+308 10 -> 1.9866536812573207868350640760678E+3089 Inexact Rounded
remx3246 remainder -8.5077009657942581515590471189084E+308 9652145155.374217047842114042376E-250 -> NaN Division_impossible
subx3246 subtract -8.5077009657942581515590471189084E+308 9652145155.374217047842114042376E-250 -> -8.5077009657942581515590471189084E+308 Inexact Rounded
addx3247 add -9504.9703032286960790904181078063E+619 -86.245956949049186533469206485003 -> -9.5049703032286960790904181078063E+622 Inexact Rounded
comx3247 compare -9504.9703032286960790904181078063E+619 -86.245956949049186533469206485003 -> -1
divx3247 divide -9504.9703032286960790904181078063E+619 -86.245956949049186533469206485003 -> 1.1020772033225707125391212519421E+621 Inexact Rounded
dvix3247 divideint -9504.9703032286960790904181078063E+619 -86.245956949049186533469206485003 -> NaN Division_impossible
mulx3247 multiply -9504.9703032286960790904181078063E+619 -86.245956949049186533469206485003 -> 8.1976525957425311427858087117655E+624 Inexact Rounded
powx3247 power -9504.9703032286960790904181078063E+619 -86 -> 0E-10030 Underflow Subnormal Inexact Rounded Clamped
remx3247 remainder -9504.9703032286960790904181078063E+619 -86.245956949049186533469206485003 -> NaN Division_impossible
subx3247 subtract -9504.9703032286960790904181078063E+619 -86.245956949049186533469206485003 -> -9.5049703032286960790904181078063E+622 Inexact Rounded
addx3248 add -440220916.66716743026896931194749 -102725.01594377871560564824358775 -> -440323641.68311120898457496019108 Inexact Rounded
comx3248 compare -440220916.66716743026896931194749 -102725.01594377871560564824358775 -> -1
divx3248 divide -440220916.66716743026896931194749 -102725.01594377871560564824358775 -> 4285.4305022264473269770246126234 Inexact Rounded
dvix3248 divideint -440220916.66716743026896931194749 -102725.01594377871560564824358775 -> 4285
mulx3248 multiply -440220916.66716743026896931194749 -102725.01594377871560564824358775 -> 45221700683419.655596771711603505 Inexact Rounded
powx3248 power -440220916.66716743026896931194749 -102725 -> -0E-10030 Underflow Subnormal Inexact Rounded Clamped
remx3248 remainder -440220916.66716743026896931194749 -102725.01594377871560564824358775 -> -44223.34807563389876658817398125
subx3248 subtract -440220916.66716743026896931194749 -102725.01594377871560564824358775 -> -440118191.65122365155336366370390 Inexact Rounded
addx3249 add -46.250735086006350517943464758019 14656357555174.263295266074908024 -> 14656357555128.012560180068557506 Inexact Rounded
comx3249 compare -46.250735086006350517943464758019 14656357555174.263295266074908024 -> -1
divx3249 divide -46.250735086006350517943464758019 14656357555174.263295266074908024 -> -3.1556773169523313932207725522866E-12 Inexact Rounded
dvix3249 divideint -46.250735086006350517943464758019 14656357555174.263295266074908024 -> -0
mulx3249 multiply -46.250735086006350517943464758019 14656357555174.263295266074908024 -> -677867310610152.55569620459788530 Inexact Rounded
powx3249 power -46.250735086006350517943464758019 1 -> -46.250735086006350517943464758019
remx3249 remainder -46.250735086006350517943464758019 14656357555174.263295266074908024 -> -46.250735086006350517943464758019
subx3249 subtract -46.250735086006350517943464758019 14656357555174.263295266074908024 -> -14656357555220.514030352081258542 Inexact Rounded
addx3250 add -61641121299391.316420647102699627E+763 -91896469863.461006903590004188187E+474 -> -6.1641121299391316420647102699627E+776 Inexact Rounded
comx3250 compare -61641121299391.316420647102699627E+763 -91896469863.461006903590004188187E+474 -> -1
divx3250 divide -61641121299391.316420647102699627E+763 -91896469863.461006903590004188187E+474 -> 6.7076702065897819604716946852581E+291 Inexact Rounded
dvix3250 divideint -61641121299391.316420647102699627E+763 -91896469863.461006903590004188187E+474 -> NaN Division_impossible
mulx3250 multiply -61641121299391.316420647102699627E+763 -91896469863.461006903590004188187E+474 -> 5.6646014458394584921579417504939E+1261 Inexact Rounded
powx3250 power -61641121299391.316420647102699627E+763 -9 -> -7.7833261179975532508748150708605E-6992 Inexact Rounded
remx3250 remainder -61641121299391.316420647102699627E+763 -91896469863.461006903590004188187E+474 -> NaN Division_impossible
subx3250 subtract -61641121299391.316420647102699627E+763 -91896469863.461006903590004188187E+474 -> -6.1641121299391316420647102699627E+776 Inexact Rounded
addx3251 add 96668419802749.555738010239087449E-838 -19498732131365824921639467044927E-542 -> -1.9498732131365824921639467044927E-511 Inexact Rounded
comx3251 compare 96668419802749.555738010239087449E-838 -19498732131365824921639467044927E-542 -> 1
divx3251 divide 96668419802749.555738010239087449E-838 -19498732131365824921639467044927E-542 -> -4.9576772044192514715453215933704E-314 Inexact Rounded
dvix3251 divideint 96668419802749.555738010239087449E-838 -19498732131365824921639467044927E-542 -> -0
mulx3251 multiply 96668419802749.555738010239087449E-838 -19498732131365824921639467044927E-542 -> -1.8849116232962331617140676274611E-1335 Inexact Rounded
powx3251 power 96668419802749.555738010239087449E-838 -2 -> 1.0701157625268896323611633350003E+1648 Inexact Rounded
remx3251 remainder 96668419802749.555738010239087449E-838 -19498732131365824921639467044927E-542 -> 9.6668419802749555738010239087449E-825
subx3251 subtract 96668419802749.555738010239087449E-838 -19498732131365824921639467044927E-542 -> 1.9498732131365824921639467044927E-511 Inexact Rounded
addx3252 add -8534543911197995906031245719519E+124 16487117050031.594886541650897974 -> -8.5345439111979959060312457195190E+154 Inexact Rounded
comx3252 compare -8534543911197995906031245719519E+124 16487117050031.594886541650897974 -> -1
divx3252 divide -8534543911197995906031245719519E+124 16487117050031.594886541650897974 -> -5.1764925822381062287959523371316E+141 Inexact Rounded
dvix3252 divideint -8534543911197995906031245719519E+124 16487117050031.594886541650897974 -> NaN Division_impossible
mulx3252 multiply -8534543911197995906031245719519E+124 16487117050031.594886541650897974 -> -1.4071002443255581217471698731240E+168 Inexact Rounded
powx3252 power -8534543911197995906031245719519E+124 2 -> 7.2838439772166785429482995041337E+309 Inexact Rounded
remx3252 remainder -8534543911197995906031245719519E+124 16487117050031.594886541650897974 -> NaN Division_impossible
subx3252 subtract -8534543911197995906031245719519E+124 16487117050031.594886541650897974 -> -8.5345439111979959060312457195190E+154 Inexact Rounded
addx3253 add -62663404777.352508979582846931050 9.2570938837239134052589184917186E+916 -> 9.2570938837239134052589184917186E+916 Inexact Rounded
comx3253 compare -62663404777.352508979582846931050 9.2570938837239134052589184917186E+916 -> -1
divx3253 divide -62663404777.352508979582846931050 9.2570938837239134052589184917186E+916 -> -6.7692307720384142592597124956951E-907 Inexact Rounded
dvix3253 divideint -62663404777.352508979582846931050 9.2570938837239134052589184917186E+916 -> -0
mulx3253 multiply -62663404777.352508979582846931050 9.2570938837239134052589184917186E+916 -> -5.8008102109774576654709018012876E+927 Inexact Rounded
powx3253 power -62663404777.352508979582846931050 9 -> -1.4897928814133059615670462753825E+97 Inexact Rounded
remx3253 remainder -62663404777.352508979582846931050 9.2570938837239134052589184917186E+916 -> -62663404777.352508979582846931050
subx3253 subtract -62663404777.352508979582846931050 9.2570938837239134052589184917186E+916 -> -9.2570938837239134052589184917186E+916 Inexact Rounded
addx3254 add 1.744601214474560992754529320172E-827 -17.353669504253419489494030651507E-631 -> -1.7353669504253419489494030651507E-630 Inexact Rounded
comx3254 compare 1.744601214474560992754529320172E-827 -17.353669504253419489494030651507E-631 -> 1
divx3254 divide 1.744601214474560992754529320172E-827 -17.353669504253419489494030651507E-631 -> -1.0053212169604565230497117966004E-197 Inexact Rounded
dvix3254 divideint 1.744601214474560992754529320172E-827 -17.353669504253419489494030651507E-631 -> -0
mulx3254 multiply 1.744601214474560992754529320172E-827 -17.353669504253419489494030651507E-631 -> -3.0275232892710668432895049546233E-1457 Inexact Rounded
powx3254 power 1.744601214474560992754529320172E-827 -2 -> 3.2855468099615282394992542515980E+1653 Inexact Rounded
remx3254 remainder 1.744601214474560992754529320172E-827 -17.353669504253419489494030651507E-631 -> 1.744601214474560992754529320172E-827
subx3254 subtract 1.744601214474560992754529320172E-827 -17.353669504253419489494030651507E-631 -> 1.7353669504253419489494030651507E-630 Inexact Rounded
addx3255 add 0367191549036702224827734853471 4410320662415266533763143837742E+721 -> 4.4103206624152665337631438377420E+751 Inexact Rounded
comx3255 compare 0367191549036702224827734853471 4410320662415266533763143837742E+721 -> -1
divx3255 divide 0367191549036702224827734853471 4410320662415266533763143837742E+721 -> 8.3257335949720619093963917942525E-723 Inexact Rounded
dvix3255 divideint 0367191549036702224827734853471 4410320662415266533763143837742E+721 -> 0
mulx3255 multiply 0367191549036702224827734853471 4410320662415266533763143837742E+721 -> 1.6194324757808363802947192054966E+781 Inexact Rounded
powx3255 power 0367191549036702224827734853471 4 -> 1.8179030119354318182493703269258E+118 Inexact Rounded
remx3255 remainder 0367191549036702224827734853471 4410320662415266533763143837742E+721 -> 367191549036702224827734853471
subx3255 subtract 0367191549036702224827734853471 4410320662415266533763143837742E+721 -> -4.4103206624152665337631438377420E+751 Inexact Rounded
addx3256 add 097704116.4492566721965710365073 -96736.400939809433556067504289145 -> 97607380.048316862763014969003011 Inexact Rounded
comx3256 compare 097704116.4492566721965710365073 -96736.400939809433556067504289145 -> 1
divx3256 divide 097704116.4492566721965710365073 -96736.400939809433556067504289145 -> -1010.0036335861757252324592571874 Inexact Rounded
dvix3256 divideint 097704116.4492566721965710365073 -96736.400939809433556067504289145 -> -1010
mulx3256 multiply 097704116.4492566721965710365073 -96736.400939809433556067504289145 -> -9451544582305.1234805483449772252 Inexact Rounded
powx3256 power 097704116.4492566721965710365073 -96736 -> 0E-10030 Underflow Subnormal Inexact Rounded Clamped
remx3256 remainder 097704116.4492566721965710365073 -96736.400939809433556067504289145 -> 351.500049144304942857175263550
subx3256 subtract 097704116.4492566721965710365073 -96736.400939809433556067504289145 -> 97800852.850196481630127104011589 Inexact Rounded
addx3257 add 19533298.147150158931958733807878 80.141668338350708476637377647025E-641 -> 19533298.147150158931958733807878 Inexact Rounded
comx3257 compare 19533298.147150158931958733807878 80.141668338350708476637377647025E-641 -> 1
divx3257 divide 19533298.147150158931958733807878 80.141668338350708476637377647025E-641 -> 2.4373460837728485395672882395171E+646 Inexact Rounded
dvix3257 divideint 19533298.147150158931958733807878 80.141668338350708476637377647025E-641 -> NaN Division_impossible
mulx3257 multiply 19533298.147150158931958733807878 80.141668338350708476637377647025E-641 -> 1.5654311016630284502459158971272E-632 Inexact Rounded
powx3257 power 19533298.147150158931958733807878 8 -> 2.1193595047638230427530063654613E+58 Inexact Rounded
remx3257 remainder 19533298.147150158931958733807878 80.141668338350708476637377647025E-641 -> NaN Division_impossible
subx3257 subtract 19533298.147150158931958733807878 80.141668338350708476637377647025E-641 -> 19533298.147150158931958733807878 Inexact Rounded
addx3258 add -23765003221220177430797028997378 -15203369569.373411506379096871224E-945 -> -23765003221220177430797028997378 Inexact Rounded
comx3258 compare -23765003221220177430797028997378 -15203369569.373411506379096871224E-945 -> -1
divx3258 divide -23765003221220177430797028997378 -15203369569.373411506379096871224E-945 -> 1.5631405336020930064824135669541E+966 Inexact Rounded
dvix3258 divideint -23765003221220177430797028997378 -15203369569.373411506379096871224E-945 -> NaN Division_impossible
mulx3258 multiply -23765003221220177430797028997378 -15203369569.373411506379096871224E-945 -> 3.6130812678955994625210007005216E-904 Inexact Rounded
powx3258 power -23765003221220177430797028997378 -2 -> 1.7706154318483481190364979209436E-63 Inexact Rounded
remx3258 remainder -23765003221220177430797028997378 -15203369569.373411506379096871224E-945 -> NaN Division_impossible
subx3258 subtract -23765003221220177430797028997378 -15203369569.373411506379096871224E-945 -> -23765003221220177430797028997378 Inexact Rounded
addx3259 add 129255.41937932433359193338910552E+932 -281253953.38990382799508873560320 -> 1.2925541937932433359193338910552E+937 Inexact Rounded
comx3259 compare 129255.41937932433359193338910552E+932 -281253953.38990382799508873560320 -> 1
divx3259 divide 129255.41937932433359193338910552E+932 -281253953.38990382799508873560320 -> -4.5956836453828213050223260551064E+928 Inexact Rounded
dvix3259 divideint 129255.41937932433359193338910552E+932 -281253953.38990382799508873560320 -> NaN Division_impossible
mulx3259 multiply 129255.41937932433359193338910552E+932 -281253953.38990382799508873560320 -> -3.6353597697504958096931088780367E+945 Inexact Rounded
powx3259 power 129255.41937932433359193338910552E+932 -281253953 -> 0E-10030 Underflow Subnormal Inexact Rounded Clamped
remx3259 remainder 129255.41937932433359193338910552E+932 -281253953.38990382799508873560320 -> NaN Division_impossible
subx3259 subtract 129255.41937932433359193338910552E+932 -281253953.38990382799508873560320 -> 1.2925541937932433359193338910552E+937 Inexact Rounded
addx3260 add -86863.276249466008289214762980838 531.50602652732088208397655484476 -> -86331.770222938687407130786425993 Inexact Rounded
comx3260 compare -86863.276249466008289214762980838 531.50602652732088208397655484476 -> -1
divx3260 divide -86863.276249466008289214762980838 531.50602652732088208397655484476 -> -163.42858201815891408475902229649 Inexact Rounded
dvix3260 divideint -86863.276249466008289214762980838 531.50602652732088208397655484476 -> -163
mulx3260 multiply -86863.276249466008289214762980838 531.50602652732088208397655484476 -> -46168354.810498682140456143534524 Inexact Rounded
powx3260 power -86863.276249466008289214762980838 532 -> 2.8897579184173839519859710217510E+2627 Inexact Rounded
remx3260 remainder -86863.276249466008289214762980838 531.50602652732088208397655484476 -> -227.79392551270450952658454114212
subx3260 subtract -86863.276249466008289214762980838 531.50602652732088208397655484476 -> -87394.782275993329171298739535683 Inexact Rounded
addx3261 add -40707.169006771111855573524157083 -68795521421321853333274411827749 -> -68795521421321853333274411868456 Inexact Rounded
comx3261 compare -40707.169006771111855573524157083 -68795521421321853333274411827749 -> 1
divx3261 divide -40707.169006771111855573524157083 -68795521421321853333274411827749 -> 5.9171248601300236694386185513139E-28 Inexact Rounded
dvix3261 divideint -40707.169006771111855573524157083 -68795521421321853333274411827749 -> 0
mulx3261 multiply -40707.169006771111855573524157083 -68795521421321853333274411827749 -> 2.8004709174066910577370895499575E+36 Inexact Rounded
powx3261 power -40707.169006771111855573524157083 -7 -> -5.3988802915897595722440392884051E-33 Inexact Rounded
remx3261 remainder -40707.169006771111855573524157083 -68795521421321853333274411827749 -> -40707.169006771111855573524157083
subx3261 subtract -40707.169006771111855573524157083 -68795521421321853333274411827749 -> 68795521421321853333274411787042 Inexact Rounded
addx3262 add -90838752568673.728630494658778003E+095 -738.01370301217606577533107981431 -> -9.0838752568673728630494658778003E+108 Inexact Rounded
comx3262 compare -90838752568673.728630494658778003E+095 -738.01370301217606577533107981431 -> -1
divx3262 divide -90838752568673.728630494658778003E+095 -738.01370301217606577533107981431 -> 1.2308545518588430875268553851424E+106 Inexact Rounded
dvix3262 divideint -90838752568673.728630494658778003E+095 -738.01370301217606577533107981431 -> NaN Division_impossible
mulx3262 multiply -90838752568673.728630494658778003E+095 -738.01370301217606577533107981431 -> 6.7040244160213718891633678248127E+111 Inexact Rounded
powx3262 power -90838752568673.728630494658778003E+095 -738 -> 0E-10030 Underflow Subnormal Inexact Rounded Clamped
remx3262 remainder -90838752568673.728630494658778003E+095 -738.01370301217606577533107981431 -> NaN Division_impossible
subx3262 subtract -90838752568673.728630494658778003E+095 -738.01370301217606577533107981431 -> -9.0838752568673728630494658778003E+108 Inexact Rounded
addx3263 add -4245360967593.9206771555839718158E-682 -3.119606239042530207103203508057 -> -3.1196062390425302071032035080570 Inexact Rounded
comx3263 compare -4245360967593.9206771555839718158E-682 -3.119606239042530207103203508057 -> 1
divx3263 divide -4245360967593.9206771555839718158E-682 -3.119606239042530207103203508057 -> 1.3608643662980066356437236081969E-670 Inexact Rounded
dvix3263 divideint -4245360967593.9206771555839718158E-682 -3.119606239042530207103203508057 -> 0
mulx3263 multiply -4245360967593.9206771555839718158E-682 -3.119606239042530207103203508057 -> 1.3243854561493627844105290415330E-669 Inexact Rounded
powx3263 power -4245360967593.9206771555839718158E-682 -3 -> -1.3069414504933253288042820429894E+2008 Inexact Rounded
remx3263 remainder -4245360967593.9206771555839718158E-682 -3.119606239042530207103203508057 -> -4.2453609675939206771555839718158E-670
subx3263 subtract -4245360967593.9206771555839718158E-682 -3.119606239042530207103203508057 -> 3.1196062390425302071032035080570 Inexact Rounded
addx3264 add -3422145405774.0800213000547612240E-023 -60810.964656409650839011321706310 -> -60810.964656409685060465379447110 Inexact Rounded
comx3264 compare -3422145405774.0800213000547612240E-023 -60810.964656409650839011321706310 -> 1
divx3264 divide -3422145405774.0800213000547612240E-023 -60810.964656409650839011321706310 -> 5.6275137635287882875914124742650E-16 Inexact Rounded
dvix3264 divideint -3422145405774.0800213000547612240E-023 -60810.964656409650839011321706310 -> 0
mulx3264 multiply -3422145405774.0800213000547612240E-023 -60810.964656409650839011321706310 -> 0.0000020810396331962224323288744910607 Inexact Rounded
powx3264 power -3422145405774.0800213000547612240E-023 -60811 -> -Infinity Overflow Inexact Rounded
remx3264 remainder -3422145405774.0800213000547612240E-023 -60810.964656409650839011321706310 -> -3.4221454057740800213000547612240E-11
subx3264 subtract -3422145405774.0800213000547612240E-023 -60810.964656409650839011321706310 -> 60810.964656409616617557263965510 Inexact Rounded
addx3265 add -24521811.07649485796598387627478E-661 -94860846133404815410816234000694 -> -94860846133404815410816234000694 Inexact Rounded
comx3265 compare -24521811.07649485796598387627478E-661 -94860846133404815410816234000694 -> 1
divx3265 divide -24521811.07649485796598387627478E-661 -94860846133404815410816234000694 -> 2.5850297647576657819483988845904E-686 Inexact Rounded
dvix3265 divideint -24521811.07649485796598387627478E-661 -94860846133404815410816234000694 -> 0
mulx3265 multiply -24521811.07649485796598387627478E-661 -94860846133404815410816234000694 -> 2.3261597474398006215017751785104E-622 Inexact Rounded
powx3265 power -24521811.07649485796598387627478E-661 -9 -> -3.1190843559949184618590264246586E+5882 Inexact Rounded
remx3265 remainder -24521811.07649485796598387627478E-661 -94860846133404815410816234000694 -> -2.452181107649485796598387627478E-654
subx3265 subtract -24521811.07649485796598387627478E-661 -94860846133404815410816234000694 -> 94860846133404815410816234000694 Inexact Rounded
addx3266 add -5042529937498.8944492248538951438 3891904674.4549170968807145612549 -> -5038638032824.4395321279731805825 Inexact Rounded
comx3266 compare -5042529937498.8944492248538951438 3891904674.4549170968807145612549 -> -1
divx3266 divide -5042529937498.8944492248538951438 3891904674.4549170968807145612549 -> -1295.6457979549894351378127413283 Inexact Rounded
dvix3266 divideint -5042529937498.8944492248538951438 3891904674.4549170968807145612549 -> -1295
mulx3266 multiply -5042529937498.8944492248538951438 3891904674.4549170968807145612549 -> -19625045834830808256871.952659048 Inexact Rounded
powx3266 power -5042529937498.8944492248538951438 4 -> 6.4653782991800009492580180960839E+50 Inexact Rounded
remx3266 remainder -5042529937498.8944492248538951438 3891904674.4549170968807145612549 -> -2513384079.7768087643285383187045
subx3266 subtract -5042529937498.8944492248538951438 3891904674.4549170968807145612549 -> -5046421842173.3493663217346097051 Inexact Rounded
addx3267 add -535824163.54531747646293693868651E-665 2732988.5891363639325008206099712 -> 2732988.5891363639325008206099712 Inexact Rounded
comx3267 compare -535824163.54531747646293693868651E-665 2732988.5891363639325008206099712 -> -1
divx3267 divide -535824163.54531747646293693868651E-665 2732988.5891363639325008206099712 -> -1.9605795855687791246611683328346E-663 Inexact Rounded
dvix3267 divideint -535824163.54531747646293693868651E-665 2732988.5891363639325008206099712 -> -0
mulx3267 multiply -535824163.54531747646293693868651E-665 2732988.5891363639325008206099712 -> -1.4644013247528895376254850705597E-650 Inexact Rounded
powx3267 power -535824163.54531747646293693868651E-665 2732989 -> -0E-10030 Underflow Subnormal Inexact Rounded Clamped
remx3267 remainder -535824163.54531747646293693868651E-665 2732988.5891363639325008206099712 -> -5.3582416354531747646293693868651E-657
subx3267 subtract -535824163.54531747646293693868651E-665 2732988.5891363639325008206099712 -> -2732988.5891363639325008206099712 Inexact Rounded
addx3268 add 24032.702008553084252925140858134E-509 52864854.899420632375589206704068 -> 52864854.899420632375589206704068 Inexact Rounded
comx3268 compare 24032.702008553084252925140858134E-509 52864854.899420632375589206704068 -> -1
divx3268 divide 24032.702008553084252925140858134E-509 52864854.899420632375589206704068 -> 4.5460641203455697917573431961511E-513 Inexact Rounded
dvix3268 divideint 24032.702008553084252925140858134E-509 52864854.899420632375589206704068 -> 0
mulx3268 multiply 24032.702008553084252925140858134E-509 52864854.899420632375589206704068 -> 1.2704853045231735885074945710576E-497 Inexact Rounded
powx3268 power 24032.702008553084252925140858134E-509 52864855 -> 0E-10030 Underflow Subnormal Inexact Rounded Clamped
remx3268 remainder 24032.702008553084252925140858134E-509 52864854.899420632375589206704068 -> 2.4032702008553084252925140858134E-505
subx3268 subtract 24032.702008553084252925140858134E-509 52864854.899420632375589206704068 -> -52864854.899420632375589206704068 Inexact Rounded
addx3269 add 71553220259.938950698030519906727E-496 754.44220417415325444943566016062 -> 754.44220417415325444943566016062 Inexact Rounded
comx3269 compare 71553220259.938950698030519906727E-496 754.44220417415325444943566016062 -> -1
divx3269 divide 71553220259.938950698030519906727E-496 754.44220417415325444943566016062 -> 9.4842547068617879794218050008353E-489 Inexact Rounded
dvix3269 divideint 71553220259.938950698030519906727E-496 754.44220417415325444943566016062 -> 0
mulx3269 multiply 71553220259.938950698030519906727E-496 754.44220417415325444943566016062 -> 5.3982769208667021044675146787248E-483 Inexact Rounded
powx3269 power 71553220259.938950698030519906727E-496 754 -> 0E-10030 Underflow Subnormal Inexact Rounded Clamped
remx3269 remainder 71553220259.938950698030519906727E-496 754.44220417415325444943566016062 -> 7.1553220259938950698030519906727E-486
subx3269 subtract 71553220259.938950698030519906727E-496 754.44220417415325444943566016062 -> -754.44220417415325444943566016062 Inexact Rounded
addx3270 add 35572.960284795962697740953932508 520.39506364687594082725754878910E-731 -> 35572.960284795962697740953932508 Inexact Rounded
comx3270 compare 35572.960284795962697740953932508 520.39506364687594082725754878910E-731 -> 1
divx3270 divide 35572.960284795962697740953932508 520.39506364687594082725754878910E-731 -> 6.8357605153869556504869061469852E+732 Inexact Rounded
dvix3270 divideint 35572.960284795962697740953932508 520.39506364687594082725754878910E-731 -> NaN Division_impossible
mulx3270 multiply 35572.960284795962697740953932508 520.39506364687594082725754878910E-731 -> 1.8511992931514185102474609686066E-724 Inexact Rounded
powx3270 power 35572.960284795962697740953932508 5 -> 56963942247985404337401.149353169 Inexact Rounded
remx3270 remainder 35572.960284795962697740953932508 520.39506364687594082725754878910E-731 -> NaN Division_impossible
subx3270 subtract 35572.960284795962697740953932508 520.39506364687594082725754878910E-731 -> 35572.960284795962697740953932508 Inexact Rounded
addx3271 add 53035405018123760598334895413057E+818 -9558464247240.4476790042911379151 -> 5.3035405018123760598334895413057E+849 Inexact Rounded
comx3271 compare 53035405018123760598334895413057E+818 -9558464247240.4476790042911379151 -> 1
divx3271 divide 53035405018123760598334895413057E+818 -9558464247240.4476790042911379151 -> -5.5485278436266802470202487233285E+836 Inexact Rounded
dvix3271 divideint 53035405018123760598334895413057E+818 -9558464247240.4476790042911379151 -> NaN Division_impossible
mulx3271 multiply 53035405018123760598334895413057E+818 -9558464247240.4476790042911379151 -> -5.0693702270365259274203181894613E+862 Inexact Rounded
powx3271 power 53035405018123760598334895413057E+818 -10 -> 5.6799053935427267944455848135618E-8498 Inexact Rounded
remx3271 remainder 53035405018123760598334895413057E+818 -9558464247240.4476790042911379151 -> NaN Division_impossible
subx3271 subtract 53035405018123760598334895413057E+818 -9558464247240.4476790042911379151 -> 5.3035405018123760598334895413057E+849 Inexact Rounded
addx3272 add 95.490751127249945886828257312118 987.01498316307365714167410690192E+133 -> 9.8701498316307365714167410690192E+135 Inexact Rounded
comx3272 compare 95.490751127249945886828257312118 987.01498316307365714167410690192E+133 -> -1
divx3272 divide 95.490751127249945886828257312118 987.01498316307365714167410690192E+133 -> 9.6747012716293341927566515915016E-135 Inexact Rounded
dvix3272 divideint 95.490751127249945886828257312118 987.01498316307365714167410690192E+133 -> 0
mulx3272 multiply 95.490751127249945886828257312118 987.01498316307365714167410690192E+133 -> 9.4250802116091862185764800227004E+137 Inexact Rounded
powx3272 power 95.490751127249945886828257312118 10 -> 63039548646186864162.847491534337 Inexact Rounded
remx3272 remainder 95.490751127249945886828257312118 987.01498316307365714167410690192E+133 -> 95.490751127249945886828257312118
subx3272 subtract 95.490751127249945886828257312118 987.01498316307365714167410690192E+133 -> -9.8701498316307365714167410690192E+135 Inexact Rounded
addx3273 add 69434850287.460788550936730296153 -35119136549015044241569827542264 -> -35119136549015044241500392691977 Inexact Rounded
comx3273 compare 69434850287.460788550936730296153 -35119136549015044241569827542264 -> 1
divx3273 divide 69434850287.460788550936730296153 -35119136549015044241569827542264 -> -1.9771229338327273644129394734299E-21 Inexact Rounded
dvix3273 divideint 69434850287.460788550936730296153 -35119136549015044241569827542264 -> -0
mulx3273 multiply 69434850287.460788550936730296153 -35119136549015044241569827542264 -> -2.4384919885057519302646522425980E+42 Inexact Rounded
powx3273 power 69434850287.460788550936730296153 -4 -> 4.3021939605842038995370443743844E-44 Inexact Rounded
remx3273 remainder 69434850287.460788550936730296153 -35119136549015044241569827542264 -> 69434850287.460788550936730296153
subx3273 subtract 69434850287.460788550936730296153 -35119136549015044241569827542264 -> 35119136549015044241639262392551 Inexact Rounded
addx3274 add -392.22739924621965621739098725407 -65551274.987160998195282109612136 -> -65551667.214560244414938327003123 Inexact Rounded
comx3274 compare -392.22739924621965621739098725407 -65551274.987160998195282109612136 -> 1
divx3274 divide -392.22739924621965621739098725407 -65551274.987160998195282109612136 -> 0.0000059835205237890809449684317245033 Inexact Rounded
dvix3274 divideint -392.22739924621965621739098725407 -65551274.987160998195282109612136 -> 0
mulx3274 multiply -392.22739924621965621739098725407 -65551274.987160998195282109612136 -> 25711006105.487929108329637701882 Inexact Rounded
powx3274 power -392.22739924621965621739098725407 -65551275 -> -0E-10030 Underflow Subnormal Inexact Rounded Clamped
remx3274 remainder -392.22739924621965621739098725407 -65551274.987160998195282109612136 -> -392.22739924621965621739098725407
subx3274 subtract -392.22739924621965621739098725407 -65551274.987160998195282109612136 -> 65550882.759761751975625892221149 Inexact Rounded
addx3275 add 6413265.4423561191792972085539457 24514.222704714139350026165721146 -> 6437779.6650608333186472347196668 Inexact Rounded
comx3275 compare 6413265.4423561191792972085539457 24514.222704714139350026165721146 -> 1
divx3275 divide 6413265.4423561191792972085539457 24514.222704714139350026165721146 -> 261.61406460270241498757868681883 Inexact Rounded
dvix3275 divideint 6413265.4423561191792972085539457 24514.222704714139350026165721146 -> 261
mulx3275 multiply 6413265.4423561191792972085539457 24514.222704714139350026165721146 -> 157216217318.36494525300694583138 Inexact Rounded
powx3275 power 6413265.4423561191792972085539457 24514 -> Infinity Overflow Inexact Rounded
remx3275 remainder 6413265.4423561191792972085539457 24514.222704714139350026165721146 -> 15053.316425728808940379300726594
subx3275 subtract 6413265.4423561191792972085539457 24514.222704714139350026165721146 -> 6388751.2196514050399471823882246 Inexact Rounded
addx3276 add -6.9667706389122107760046184064057E+487 32.405810703971538278419625527234 -> -6.9667706389122107760046184064057E+487 Inexact Rounded
comx3276 compare -6.9667706389122107760046184064057E+487 32.405810703971538278419625527234 -> -1
divx3276 divide -6.9667706389122107760046184064057E+487 32.405810703971538278419625527234 -> -2.1498522911689997341347293419761E+486 Inexact Rounded
dvix3276 divideint -6.9667706389122107760046184064057E+487 32.405810703971538278419625527234 -> NaN Division_impossible
mulx3276 multiply -6.9667706389122107760046184064057E+487 32.405810703971538278419625527234 -> -2.2576385054257595259511556258470E+489 Inexact Rounded
powx3276 power -6.9667706389122107760046184064057E+487 32 -> Infinity Overflow Inexact Rounded
remx3276 remainder -6.9667706389122107760046184064057E+487 32.405810703971538278419625527234 -> NaN Division_impossible
subx3276 subtract -6.9667706389122107760046184064057E+487 32.405810703971538278419625527234 -> -6.9667706389122107760046184064057E+487 Inexact Rounded
addx3277 add 378204716633.40024100602896057615 -0300218714378.322231269606216516 -> 77986002255.07800973642274406015
comx3277 compare 378204716633.40024100602896057615 -0300218714378.322231269606216516 -> 1
divx3277 divide 378204716633.40024100602896057615 -0300218714378.322231269606216516 -> -1.2597639604731753284599748820876 Inexact Rounded
dvix3277 divideint 378204716633.40024100602896057615 -0300218714378.322231269606216516 -> -1
mulx3277 multiply 378204716633.40024100602896057615 -0300218714378.322231269606216516 -> -113544133799497082075557.21180430 Inexact Rounded
powx3277 power 378204716633.40024100602896057615 -3 -> 1.8484988212401886887562779996737E-35 Inexact Rounded
remx3277 remainder 378204716633.40024100602896057615 -0300218714378.322231269606216516 -> 77986002255.07800973642274406015
subx3277 subtract 378204716633.40024100602896057615 -0300218714378.322231269606216516 -> 678423431011.72247227563517709215
addx3278 add -44234.512012457148027685282969235E+501 2132572.4571987908375002100894927 -> -4.4234512012457148027685282969235E+505 Inexact Rounded
comx3278 compare -44234.512012457148027685282969235E+501 2132572.4571987908375002100894927 -> -1
divx3278 divide -44234.512012457148027685282969235E+501 2132572.4571987908375002100894927 -> -2.0742325477916347193181603963257E+499 Inexact Rounded
dvix3278 divideint -44234.512012457148027685282969235E+501 2132572.4571987908375002100894927 -> NaN Division_impossible
mulx3278 multiply -44234.512012457148027685282969235E+501 2132572.4571987908375002100894927 -> -9.4333301975395170465982968019915E+511 Inexact Rounded
powx3278 power -44234.512012457148027685282969235E+501 2132572 -> Infinity Overflow Inexact Rounded
remx3278 remainder -44234.512012457148027685282969235E+501 2132572.4571987908375002100894927 -> NaN Division_impossible
subx3278 subtract -44234.512012457148027685282969235E+501 2132572.4571987908375002100894927 -> -4.4234512012457148027685282969235E+505 Inexact Rounded
addx3279 add -3554.5895974968741465654022772100E-073 9752.0428746722497621936998533848E+516 -> 9.7520428746722497621936998533848E+519 Inexact Rounded
comx3279 compare -3554.5895974968741465654022772100E-073 9752.0428746722497621936998533848E+516 -> -1
divx3279 divide -3554.5895974968741465654022772100E-073 9752.0428746722497621936998533848E+516 -> -3.6449692061227100572768330912162E-590 Inexact Rounded
dvix3279 divideint -3554.5895974968741465654022772100E-073 9752.0428746722497621936998533848E+516 -> -0
mulx3279 multiply -3554.5895974968741465654022772100E-073 9752.0428746722497621936998533848E+516 -> -3.4664510156653491769901435777060E+450 Inexact Rounded
powx3279 power -3554.5895974968741465654022772100E-073 10 -> 3.2202875716019266933215387456197E-695 Inexact Rounded
remx3279 remainder -3554.5895974968741465654022772100E-073 9752.0428746722497621936998533848E+516 -> -3.5545895974968741465654022772100E-70
subx3279 subtract -3554.5895974968741465654022772100E-073 9752.0428746722497621936998533848E+516 -> -9.7520428746722497621936998533848E+519 Inexact Rounded
addx3280 add 750187685.63632608923397234391668 4633194252863.6730625972669246025 -> 4633944440549.3093886865008969464 Inexact Rounded
comx3280 compare 750187685.63632608923397234391668 4633194252863.6730625972669246025 -> -1
divx3280 divide 750187685.63632608923397234391668 4633194252863.6730625972669246025 -> 0.00016191587157664541463871807382759 Inexact Rounded
dvix3280 divideint 750187685.63632608923397234391668 4633194252863.6730625972669246025 -> 0
mulx3280 multiply 750187685.63632608923397234391668 4633194252863.6730625972669246025 -> 3475765273659325895012.7612107556 Inexact Rounded
powx3280 power 750187685.63632608923397234391668 5 -> 2.3760176068829529745152188798557E+44 Inexact Rounded
remx3280 remainder 750187685.63632608923397234391668 4633194252863.6730625972669246025 -> 750187685.63632608923397234391668
subx3280 subtract 750187685.63632608923397234391668 4633194252863.6730625972669246025 -> -4632444065178.0367365080329522586 Inexact Rounded
addx3281 add 30190034242853.251165951457589386E-028 8038885676.3204238322976087799751E+018 -> 8038885676320423832297608779.9751 Inexact Rounded
comx3281 compare 30190034242853.251165951457589386E-028 8038885676.3204238322976087799751E+018 -> -1
divx3281 divide 30190034242853.251165951457589386E-028 8038885676.3204238322976087799751E+018 -> 3.7554998862319807295903348960280E-43 Inexact Rounded
dvix3281 divideint 30190034242853.251165951457589386E-028 8038885676.3204238322976087799751E+018 -> 0
mulx3281 multiply 30190034242853.251165951457589386E-028 8038885676.3204238322976087799751E+018 -> 24269423384249.611263728854793731 Inexact Rounded
powx3281 power 30190034242853.251165951457589386E-028 8 -> 6.9009494305612589578332690692113E-117 Inexact Rounded
remx3281 remainder 30190034242853.251165951457589386E-028 8038885676.3204238322976087799751E+018 -> 3.0190034242853251165951457589386E-15
subx3281 subtract 30190034242853.251165951457589386E-028 8038885676.3204238322976087799751E+018 -> -8038885676320423832297608779.9751 Inexact Rounded
addx3282 add 65.537942676774715953400768803539 125946917260.87536506197191782198 -> 125946917326.41330773874663377538 Inexact Rounded
comx3282 compare 65.537942676774715953400768803539 125946917260.87536506197191782198 -> -1
divx3282 divide 65.537942676774715953400768803539 125946917260.87536506197191782198 -> 5.2036162616846894920389414735878E-10 Inexact Rounded
dvix3282 divideint 65.537942676774715953400768803539 125946917260.87536506197191782198 -> 0
mulx3282 multiply 65.537942676774715953400768803539 125946917260.87536506197191782198 -> 8254301843759.7376990957355411370 Inexact Rounded
powx3282 power 65.537942676774715953400768803539 1 -> 65.537942676774715953400768803539
remx3282 remainder 65.537942676774715953400768803539 125946917260.87536506197191782198 -> 65.537942676774715953400768803539
subx3282 subtract 65.537942676774715953400768803539 125946917260.87536506197191782198 -> -125946917195.33742238519720186858 Inexact Rounded
addx3283 add 8015272348677.5489394183881813700 949.23027111499779258284877920022 -> 8015272349626.7792105333859739528 Inexact Rounded
comx3283 compare 8015272348677.5489394183881813700 949.23027111499779258284877920022 -> 1
divx3283 divide 8015272348677.5489394183881813700 949.23027111499779258284877920022 -> 8443970438.5560107978790084430110 Inexact Rounded
dvix3283 divideint 8015272348677.5489394183881813700 949.23027111499779258284877920022 -> 8443970438
mulx3283 multiply 8015272348677.5489394183881813700 949.23027111499779258284877920022 -> 7608339144595734.8984281431471741 Inexact Rounded
powx3283 power 8015272348677.5489394183881813700 949 -> Infinity Overflow Inexact Rounded
remx3283 remainder 8015272348677.5489394183881813700 949.23027111499779258284877920022 -> 527.78228041355742397895303690364
subx3283 subtract 8015272348677.5489394183881813700 949.23027111499779258284877920022 -> 8015272347728.3186683033903887872 Inexact Rounded
addx3284 add -32595333982.67068622120451804225 69130.052233649808750113141502465E-862 -> -32595333982.670686221204518042250 Inexact Rounded
comx3284 compare -32595333982.67068622120451804225 69130.052233649808750113141502465E-862 -> -1
divx3284 divide -32595333982.67068622120451804225 69130.052233649808750113141502465E-862 -> -4.7150744038935574574681609457727E+867 Inexact Rounded
dvix3284 divideint -32595333982.67068622120451804225 69130.052233649808750113141502465E-862 -> NaN Division_impossible
mulx3284 multiply -32595333982.67068622120451804225 69130.052233649808750113141502465E-862 -> -2.2533171407952851885446213697715E-847 Inexact Rounded
powx3284 power -32595333982.67068622120451804225 7 -> -3.9092014148031739666525606093306E+73 Inexact Rounded
remx3284 remainder -32595333982.67068622120451804225 69130.052233649808750113141502465E-862 -> NaN Division_impossible
subx3284 subtract -32595333982.67068622120451804225 69130.052233649808750113141502465E-862 -> -32595333982.670686221204518042250 Inexact Rounded
addx3285 add -17544189017145.710117633021800426E-537 292178000.06450804618299520094843 -> 292178000.06450804618299520094843 Inexact Rounded
comx3285 compare -17544189017145.710117633021800426E-537 292178000.06450804618299520094843 -> -1
divx3285 divide -17544189017145.710117633021800426E-537 292178000.06450804618299520094843 -> -6.0046235559392715334668277026896E-533 Inexact Rounded
dvix3285 divideint -17544189017145.710117633021800426E-537 292178000.06450804618299520094843 -> -0
mulx3285 multiply -17544189017145.710117633021800426E-537 292178000.06450804618299520094843 -> -5.1260260597833406461110136952456E-516 Inexact Rounded
powx3285 power -17544189017145.710117633021800426E-537 292178000 -> 0E-10030 Underflow Subnormal Inexact Rounded Clamped
remx3285 remainder -17544189017145.710117633021800426E-537 292178000.06450804618299520094843 -> -1.7544189017145710117633021800426E-524
subx3285 subtract -17544189017145.710117633021800426E-537 292178000.06450804618299520094843 -> -292178000.06450804618299520094843 Inexact Rounded
addx3286 add -506650.99395649907139204052441630 11.018427502031650148962067367158 -> -506639.97552899703974189156234893 Inexact Rounded
comx3286 compare -506650.99395649907139204052441630 11.018427502031650148962067367158 -> -1
divx3286 divide -506650.99395649907139204052441630 11.018427502031650148962067367158 -> -45982.150707356329027698717189104 Inexact Rounded
dvix3286 divideint -506650.99395649907139204052441630 11.018427502031650148962067367158 -> -45982
mulx3286 multiply -506650.99395649907139204052441630 11.018427502031650148962067367158 -> -5582497.2457419607392940234271222 Inexact Rounded
powx3286 power -506650.99395649907139204052441630 11 -> -5.6467412678809885333313818078829E+62 Inexact Rounded
remx3286 remainder -506650.99395649907139204052441630 11.018427502031650148962067367158 -> -1.660558079734242466742739640844
subx3286 subtract -506650.99395649907139204052441630 11.018427502031650148962067367158 -> -506662.01238400110304218948648367 Inexact Rounded
addx3287 add 4846835159.5922372307656000769395E-241 -84.001893040865864590122330800768 -> -84.001893040865864590122330800768 Inexact Rounded
comx3287 compare 4846835159.5922372307656000769395E-241 -84.001893040865864590122330800768 -> 1
divx3287 divide 4846835159.5922372307656000769395E-241 -84.001893040865864590122330800768 -> -5.7699118247660357814641813260524E-234 Inexact Rounded
dvix3287 divideint 4846835159.5922372307656000769395E-241 -84.001893040865864590122330800768 -> -0
mulx3287 multiply 4846835159.5922372307656000769395E-241 -84.001893040865864590122330800768 -> -4.0714332866277514481192856925775E-230 Inexact Rounded
powx3287 power 4846835159.5922372307656000769395E-241 -84 -> Infinity Overflow Inexact Rounded
remx3287 remainder 4846835159.5922372307656000769395E-241 -84.001893040865864590122330800768 -> 4.8468351595922372307656000769395E-232
subx3287 subtract 4846835159.5922372307656000769395E-241 -84.001893040865864590122330800768 -> 84.001893040865864590122330800768 Inexact Rounded
addx3288 add -35.029311013822259358116556164908 -3994308878.1994645313414534209127 -> -3994308913.2287755451637127790293 Inexact Rounded
comx3288 compare -35.029311013822259358116556164908 -3994308878.1994645313414534209127 -> 1
divx3288 divide -35.029311013822259358116556164908 -3994308878.1994645313414534209127 -> 8.7698052609323004543538163061774E-9 Inexact Rounded
dvix3288 divideint -35.029311013822259358116556164908 -3994308878.1994645313414534209127 -> 0
mulx3288 multiply -35.029311013822259358116556164908 -3994308878.1994645313414534209127 -> 139917887979.72053637272961120639 Inexact Rounded
powx3288 power -35.029311013822259358116556164908 -4 -> 6.6416138040522124693495178218096E-7 Inexact Rounded
remx3288 remainder -35.029311013822259358116556164908 -3994308878.1994645313414534209127 -> -35.029311013822259358116556164908
subx3288 subtract -35.029311013822259358116556164908 -3994308878.1994645313414534209127 -> 3994308843.1701535175191940627961 Inexact Rounded
addx3289 add 7606663750.6735265233044420887018E+166 -5491814639.4484565418284686127552E+365 -> -5.4918146394484565418284686127552E+374 Inexact Rounded
comx3289 compare 7606663750.6735265233044420887018E+166 -5491814639.4484565418284686127552E+365 -> 1
divx3289 divide 7606663750.6735265233044420887018E+166 -5491814639.4484565418284686127552E+365 -> -1.3850911310869487895947733090204E-199 Inexact Rounded
dvix3289 divideint 7606663750.6735265233044420887018E+166 -5491814639.4484565418284686127552E+365 -> -0
mulx3289 multiply 7606663750.6735265233044420887018E+166 -5491814639.4484565418284686127552E+365 -> -4.1774387343310777190917128006589E+550 Inexact Rounded
powx3289 power 7606663750.6735265233044420887018E+166 -5 -> 3.9267106978887346373957314818178E-880 Inexact Rounded
remx3289 remainder 7606663750.6735265233044420887018E+166 -5491814639.4484565418284686127552E+365 -> 7.6066637506735265233044420887018E+175
subx3289 subtract 7606663750.6735265233044420887018E+166 -5491814639.4484565418284686127552E+365 -> 5.4918146394484565418284686127552E+374 Inexact Rounded
addx3290 add -25677.829660831741274207326697052E-163 -94135395124193048560172705082029E-862 -> -2.5677829660831741274207326697052E-159 Inexact Rounded
comx3290 compare -25677.829660831741274207326697052E-163 -94135395124193048560172705082029E-862 -> -1
divx3290 divide -25677.829660831741274207326697052E-163 -94135395124193048560172705082029E-862 -> 2.7277550199853009708493167299838E+671 Inexact Rounded
dvix3290 divideint -25677.829660831741274207326697052E-163 -94135395124193048560172705082029E-862 -> NaN Division_impossible
mulx3290 multiply -25677.829660831741274207326697052E-163 -94135395124193048560172705082029E-862 -> 2.4171926410541199393728294762559E-989 Inexact Rounded
powx3290 power -25677.829660831741274207326697052E-163 -9 -> -2.0605121420682764897867221992174E+1427 Inexact Rounded
remx3290 remainder -25677.829660831741274207326697052E-163 -94135395124193048560172705082029E-862 -> NaN Division_impossible
subx3290 subtract -25677.829660831741274207326697052E-163 -94135395124193048560172705082029E-862 -> -2.5677829660831741274207326697052E-159 Inexact Rounded
addx3291 add 97271576094.456406729671729224827 -1.5412563837540810793697955063295E+554 -> -1.5412563837540810793697955063295E+554 Inexact Rounded
comx3291 compare 97271576094.456406729671729224827 -1.5412563837540810793697955063295E+554 -> 1
divx3291 divide 97271576094.456406729671729224827 -1.5412563837540810793697955063295E+554 -> -6.3111872313890646144473652645030E-544 Inexact Rounded
dvix3291 divideint 97271576094.456406729671729224827 -1.5412563837540810793697955063295E+554 -> -0
mulx3291 multiply 97271576094.456406729671729224827 -1.5412563837540810793697955063295E+554 -> -1.4992043761340180288065959300090E+565 Inexact Rounded
powx3291 power 97271576094.456406729671729224827 -2 -> 1.0568858765852073181352309401343E-22 Inexact Rounded
remx3291 remainder 97271576094.456406729671729224827 -1.5412563837540810793697955063295E+554 -> 97271576094.456406729671729224827
subx3291 subtract 97271576094.456406729671729224827 -1.5412563837540810793697955063295E+554 -> 1.5412563837540810793697955063295E+554 Inexact Rounded
addx3292 add 41139789894.401826915757391777563 -1.8729920305671057957156159690823E-020 -> 41139789894.401826915757391777544 Inexact Rounded
comx3292 compare 41139789894.401826915757391777563 -1.8729920305671057957156159690823E-020 -> 1
divx3292 divide 41139789894.401826915757391777563 -1.8729920305671057957156159690823E-020 -> -2196474369511625389289506461551.0 Inexact Rounded
dvix3292 divideint 41139789894.401826915757391777563 -1.8729920305671057957156159690823E-020 -> -2196474369511625389289506461551
mulx3292 multiply 41139789894.401826915757391777563 -1.8729920305671057957156159690823E-020 -> -7.7054498611419776714291080928601E-10 Inexact Rounded
powx3292 power 41139789894.401826915757391777563 -2 -> 5.9084812442741091550891451069919E-22 Inexact Rounded
remx3292 remainder 41139789894.401826915757391777563 -1.8729920305671057957156159690823E-020 -> 6.98141022640544018935102953527E-22
subx3292 subtract 41139789894.401826915757391777563 -1.8729920305671057957156159690823E-020 -> 41139789894.401826915757391777582 Inexact Rounded
addx3293 add -83310831287241.777598696853498149 -54799825033.797100418162985103519E-330 -> -83310831287241.777598696853498149 Inexact Rounded
comx3293 compare -83310831287241.777598696853498149 -54799825033.797100418162985103519E-330 -> -1
divx3293 divide -83310831287241.777598696853498149 -54799825033.797100418162985103519E-330 -> 1.5202754978845438636605170857478E+333 Inexact Rounded
dvix3293 divideint -83310831287241.777598696853498149 -54799825033.797100418162985103519E-330 -> NaN Division_impossible
mulx3293 multiply -83310831287241.777598696853498149 -54799825033.797100418162985103519E-330 -> 4.5654189779610386760330527839588E-306 Inexact Rounded
powx3293 power -83310831287241.777598696853498149 -5 -> -2.4916822606682624827900581728387E-70 Inexact Rounded
remx3293 remainder -83310831287241.777598696853498149 -54799825033.797100418162985103519E-330 -> NaN Division_impossible
subx3293 subtract -83310831287241.777598696853498149 -54799825033.797100418162985103519E-330 -> -83310831287241.777598696853498149 Inexact Rounded
addx3294 add 4506653461.4414974233678331771169 -74955470.977653038010031457181957E-887 -> 4506653461.4414974233678331771169 Inexact Rounded
comx3294 compare 4506653461.4414974233678331771169 -74955470.977653038010031457181957E-887 -> 1
divx3294 divide 4506653461.4414974233678331771169 -74955470.977653038010031457181957E-887 -> -6.0124409901781490054438220048629E+888 Inexact Rounded
dvix3294 divideint 4506653461.4414974233678331771169 -74955470.977653038010031457181957E-887 -> NaN Division_impossible
mulx3294 multiply 4506653461.4414974233678331771169 -74955470.977653038010031457181957E-887 -> -3.3779833273541776470902903512949E-870 Inexact Rounded
powx3294 power 4506653461.4414974233678331771169 -7 -> 2.6486272911486461102735412463189E-68 Inexact Rounded
remx3294 remainder 4506653461.4414974233678331771169 -74955470.977653038010031457181957E-887 -> NaN Division_impossible
subx3294 subtract 4506653461.4414974233678331771169 -74955470.977653038010031457181957E-887 -> 4506653461.4414974233678331771169 Inexact Rounded
addx3295 add 23777.857951114967684767609401626 720760.03897144157012301385227528 -> 744537.89692255653780778146167691 Inexact Rounded
comx3295 compare 23777.857951114967684767609401626 720760.03897144157012301385227528 -> -1
divx3295 divide 23777.857951114967684767609401626 720760.03897144157012301385227528 -> 0.032989978169498808275308039034795 Inexact Rounded
dvix3295 divideint 23777.857951114967684767609401626 720760.03897144157012301385227528 -> 0
mulx3295 multiply 23777.857951114967684767609401626 720760.03897144157012301385227528 -> 17138129823.503025913034987537096 Inexact Rounded
powx3295 power 23777.857951114967684767609401626 720760 -> Infinity Overflow Inexact Rounded
remx3295 remainder 23777.857951114967684767609401626 720760.03897144157012301385227528 -> 23777.857951114967684767609401626
subx3295 subtract 23777.857951114967684767609401626 720760.03897144157012301385227528 -> -696982.18102032660243824624287365 Inexact Rounded
addx3296 add -21337853323980858055292469611948 6080272840.3071490445256786982100E+532 -> 6.0802728403071490445256786982100E+541 Inexact Rounded
comx3296 compare -21337853323980858055292469611948 6080272840.3071490445256786982100E+532 -> -1
divx3296 divide -21337853323980858055292469611948 6080272840.3071490445256786982100E+532 -> -3.5093578667274020123788514069885E-511 Inexact Rounded
dvix3296 divideint -21337853323980858055292469611948 6080272840.3071490445256786982100E+532 -> -0
mulx3296 multiply -21337853323980858055292469611948 6080272840.3071490445256786982100E+532 -> -1.2973997003625843317417981902198E+573 Inexact Rounded
powx3296 power -21337853323980858055292469611948 6 -> 9.4385298321304970306180652097874E+187 Inexact Rounded
remx3296 remainder -21337853323980858055292469611948 6080272840.3071490445256786982100E+532 -> -21337853323980858055292469611948
subx3296 subtract -21337853323980858055292469611948 6080272840.3071490445256786982100E+532 -> -6.0802728403071490445256786982100E+541 Inexact Rounded
addx3297 add -818409238.0423893439849743856947 756.39156265972753259267069158760 -> -818408481.65082668425744179302401 Inexact Rounded
comx3297 compare -818409238.0423893439849743856947 756.39156265972753259267069158760 -> -1
divx3297 divide -818409238.0423893439849743856947 756.39156265972753259267069158760 -> -1081991.4954690752676494129493403 Inexact Rounded
dvix3297 divideint -818409238.0423893439849743856947 756.39156265972753259267069158760 -> -1081991
mulx3297 multiply -818409238.0423893439849743856947 756.39156265972753259267069158760 -> -619037842458.03980537370328252817 Inexact Rounded
powx3297 power -818409238.0423893439849743856947 756 -> 1.6058883946373232750995543573461E+6738 Inexact Rounded
remx3297 remainder -818409238.0423893439849743856947 756.39156265972753259267069158760 -> -374.76862809126749803143314108840
subx3297 subtract -818409238.0423893439849743856947 756.39156265972753259267069158760 -> -818409994.43395200371250697836539 Inexact Rounded
addx3298 add 47567380384943.87013600286155046 65.084709374908275826942076480326 -> 47567380385008.954845377769826287 Inexact Rounded
comx3298 compare 47567380384943.87013600286155046 65.084709374908275826942076480326 -> 1
divx3298 divide 47567380384943.87013600286155046 65.084709374908275826942076480326 -> 730853388480.86247690474303493972 Inexact Rounded
dvix3298 divideint 47567380384943.87013600286155046 65.084709374908275826942076480326 -> 730853388480
mulx3298 multiply 47567380384943.87013600286155046 65.084709374908275826942076480326 -> 3095909128079784.3348591472999468 Inexact Rounded
powx3298 power 47567380384943.87013600286155046 65 -> 1.0594982876763214301042437482634E+889 Inexact Rounded
remx3298 remainder 47567380384943.87013600286155046 65.084709374908275826942076480326 -> 56.134058687770878126430844955520
subx3298 subtract 47567380384943.87013600286155046 65.084709374908275826942076480326 -> 47567380384878.785426627953274633 Inexact Rounded
addx3299 add -296615544.05897984545127115290251 -5416115.4315053536014016450973264 -> -302031659.49048519905267279799984 Inexact Rounded
comx3299 compare -296615544.05897984545127115290251 -5416115.4315053536014016450973264 -> -1
divx3299 divide -296615544.05897984545127115290251 -5416115.4315053536014016450973264 -> 54.765366028496664530688259272591 Inexact Rounded
dvix3299 divideint -296615544.05897984545127115290251 -5416115.4315053536014016450973264 -> 54
mulx3299 multiply -296615544.05897984545127115290251 -5416115.4315053536014016450973264 -> 1606504025402196.8484885386501478 Inexact Rounded
powx3299 power -296615544.05897984545127115290251 -5416115 -> -0E-10030 Underflow Subnormal Inexact Rounded Clamped
remx3299 remainder -296615544.05897984545127115290251 -5416115.4315053536014016450973264 -> -4145310.7576907509755823176468844
subx3299 subtract -296615544.05897984545127115290251 -5416115.4315053536014016450973264 -> -291199428.62747449184986950780518 Inexact Rounded
addx3300 add 61391705914.046707180652185247584E+739 -675982087721.91856456125242297346 -> 6.1391705914046707180652185247584E+749 Inexact Rounded
comx3300 compare 61391705914.046707180652185247584E+739 -675982087721.91856456125242297346 -> 1
divx3300 divide 61391705914.046707180652185247584E+739 -675982087721.91856456125242297346 -> -9.0818539468906248593699700040068E+737 Inexact Rounded
dvix3300 divideint 61391705914.046707180652185247584E+739 -675982087721.91856456125242297346 -> NaN Division_impossible
mulx3300 multiply 61391705914.046707180652185247584E+739 -675982087721.91856456125242297346 -> -4.1499693532587347944890300176290E+761 Inexact Rounded
powx3300 power 61391705914.046707180652185247584E+739 -7 -> 3.0425105291210947473420999890124E-5249 Inexact Rounded
remx3300 remainder 61391705914.046707180652185247584E+739 -675982087721.91856456125242297346 -> NaN Division_impossible
subx3300 subtract 61391705914.046707180652185247584E+739 -675982087721.91856456125242297346 -> 6.1391705914046707180652185247584E+749 Inexact Rounded

-- randomly generated testcases [26 Sep 2001]
precision: 33
rounding: half_up
maxExponent: 9999

addx3401 add 042.668056830485571428877212944418 -01364112374639.4941124016455321071 -> -1364112374596.82605557115996067822 Inexact Rounded
comx3401 compare 042.668056830485571428877212944418 -01364112374639.4941124016455321071 -> 1
divx3401 divide 042.668056830485571428877212944418 -01364112374639.4941124016455321071 -> -3.12789896373176963160811150593867E-11 Inexact Rounded
dvix3401 divideint 042.668056830485571428877212944418 -01364112374639.4941124016455321071 -> -0
mulx3401 multiply 042.668056830485571428877212944418 -01364112374639.4941124016455321071 -> -58204024324286.5595453066065234923 Inexact Rounded
powx3401 power 042.668056830485571428877212944418 -1 -> 0.0234367363850869744523417227148909 Inexact Rounded
remx3401 remainder 042.668056830485571428877212944418 -01364112374639.4941124016455321071 -> 42.668056830485571428877212944418
subx3401 subtract 042.668056830485571428877212944418 -01364112374639.4941124016455321071 -> 1364112374682.16216923213110353598 Inexact Rounded
addx3402 add -327.179426341653256363531809227344E+453 759067457.107518663444899356760793 -> -3.27179426341653256363531809227344E+455 Inexact Rounded
comx3402 compare -327.179426341653256363531809227344E+453 759067457.107518663444899356760793 -> -1
divx3402 divide -327.179426341653256363531809227344E+453 759067457.107518663444899356760793 -> -4.31028129684803083255704680611589E+446 Inexact Rounded
dvix3402 divideint -327.179426341653256363531809227344E+453 759067457.107518663444899356760793 -> NaN Division_impossible
mulx3402 multiply -327.179426341653256363531809227344E+453 759067457.107518663444899356760793 -> -2.48351255171055445110558613627379E+464 Inexact Rounded
powx3402 power -327.179426341653256363531809227344E+453 759067457 -> -Infinity Overflow Inexact Rounded
remx3402 remainder -327.179426341653256363531809227344E+453 759067457.107518663444899356760793 -> NaN Division_impossible
subx3402 subtract -327.179426341653256363531809227344E+453 759067457.107518663444899356760793 -> -3.27179426341653256363531809227344E+455 Inexact Rounded
addx3403 add 81721.5803096185422787702538493471 900099473.245809628076790757217328 -> 900181194.826119246619069527471177 Inexact Rounded
comx3403 compare 81721.5803096185422787702538493471 900099473.245809628076790757217328 -> -1
divx3403 divide 81721.5803096185422787702538493471 900099473.245809628076790757217328 -> 0.0000907917210693679220610511319812826 Inexact Rounded
dvix3403 divideint 81721.5803096185422787702538493471 900099473.245809628076790757217328 -> 0
mulx3403 multiply 81721.5803096185422787702538493471 900099473.245809628076790757217328 -> 73557551389502.7779979042453102926 Inexact Rounded
powx3403 power 81721.5803096185422787702538493471 900099473 -> Infinity Overflow Inexact Rounded
remx3403 remainder 81721.5803096185422787702538493471 900099473.245809628076790757217328 -> 81721.5803096185422787702538493471
subx3403 subtract 81721.5803096185422787702538493471 900099473.245809628076790757217328 -> -900017751.665500009534511986963479 Inexact Rounded
addx3404 add 3991.56734635183403814636354392163E-807 72.3239822255871305731314565069132 -> 72.3239822255871305731314565069132 Inexact Rounded
comx3404 compare 3991.56734635183403814636354392163E-807 72.3239822255871305731314565069132 -> -1
divx3404 divide 3991.56734635183403814636354392163E-807 72.3239822255871305731314565069132 -> 5.51900935695390664984598248115290E-806 Inexact Rounded
dvix3404 divideint 3991.56734635183403814636354392163E-807 72.3239822255871305731314565069132 -> 0
mulx3404 multiply 3991.56734635183403814636354392163E-807 72.3239822255871305731314565069132 -> 2.88686045809784034794803928177854E-802 Inexact Rounded
powx3404 power 3991.56734635183403814636354392163E-807 72 -> 0E-10031 Underflow Subnormal Inexact Rounded Clamped
remx3404 remainder 3991.56734635183403814636354392163E-807 72.3239822255871305731314565069132 -> 3.99156734635183403814636354392163E-804
subx3404 subtract 3991.56734635183403814636354392163E-807 72.3239822255871305731314565069132 -> -72.3239822255871305731314565069132 Inexact Rounded
addx3405 add -66.3393308595957726456416979163306 5.08486573050459213864684589662559 -> -61.2544651290911805069948520197050 Inexact Rounded
comx3405 compare -66.3393308595957726456416979163306 5.08486573050459213864684589662559 -> -1
divx3405 divide -66.3393308595957726456416979163306 5.08486573050459213864684589662559 -> -13.0464272560079276694749924915850 Inexact Rounded
dvix3405 divideint -66.3393308595957726456416979163306 5.08486573050459213864684589662559 -> -13
mulx3405 multiply -66.3393308595957726456416979163306 5.08486573050459213864684589662559 -> -337.326590072564290813539036280205 Inexact Rounded
powx3405 power -66.3393308595957726456416979163306 5 -> -1284858888.27285822259184896667990 Inexact Rounded
remx3405 remainder -66.3393308595957726456416979163306 5.08486573050459213864684589662559 -> -0.23607636303607484323270126019793
subx3405 subtract -66.3393308595957726456416979163306 5.08486573050459213864684589662559 -> -71.4241965901003647842885438129562 Inexact Rounded
addx3406 add -393606.873703567753255097095208112E+111 -2124339550.86891093200758095660557 -> -3.93606873703567753255097095208112E+116 Inexact Rounded
comx3406 compare -393606.873703567753255097095208112E+111 -2124339550.86891093200758095660557 -> -1
divx3406 divide -393606.873703567753255097095208112E+111 -2124339550.86891093200758095660557 -> 1.85284350396137075010428736564737E+107 Inexact Rounded
dvix3406 divideint -393606.873703567753255097095208112E+111 -2124339550.86891093200758095660557 -> NaN Division_impossible
mulx3406 multiply -393606.873703567753255097095208112E+111 -2124339550.86891093200758095660557 -> 8.36154649302353269818801263275941E+125 Inexact Rounded
powx3406 power -393606.873703567753255097095208112E+111 -2 -> 6.45467904123103560528919747688443E-234 Inexact Rounded
remx3406 remainder -393606.873703567753255097095208112E+111 -2124339550.86891093200758095660557 -> NaN Division_impossible
subx3406 subtract -393606.873703567753255097095208112E+111 -2124339550.86891093200758095660557 -> -3.93606873703567753255097095208112E+116 Inexact Rounded
addx3407 add -019133598.609812524622150421584346 -858439846.628367734642622922030051 -> -877573445.238180259264773343614397
comx3407 compare -019133598.609812524622150421584346 -858439846.628367734642622922030051 -> 1
divx3407 divide -019133598.609812524622150421584346 -858439846.628367734642622922030051 -> 0.0222888053076312565797460650311070 Inexact Rounded
dvix3407 divideint -019133598.609812524622150421584346 -858439846.628367734642622922030051 -> 0
mulx3407 multiply -019133598.609812524622150421584346 -858439846.628367734642622922030051 -> 16425043456056213.7395191514029288 Inexact Rounded
powx3407 power -019133598.609812524622150421584346 -858439847 -> -0E-10031 Underflow Subnormal Inexact Rounded Clamped
remx3407 remainder -019133598.609812524622150421584346 -858439846.628367734642622922030051 -> -19133598.609812524622150421584346
subx3407 subtract -019133598.609812524622150421584346 -858439846.628367734642622922030051 -> 839306248.018555210020472500445705
addx3408 add 465.351982159046525762715549761814 240444.975944666924657629172844780 -> 240910.327926825971183391888394542 Inexact Rounded
comx3408 compare 465.351982159046525762715549761814 240444.975944666924657629172844780 -> -1
divx3408 divide 465.351982159046525762715549761814 240444.975944666924657629172844780 -> 0.00193537827243326122782974132829095 Inexact Rounded
dvix3408 divideint 465.351982159046525762715549761814 240444.975944666924657629172844780 -> 0
mulx3408 multiply 465.351982159046525762715549761814 240444.975944666924657629172844780 -> 111891546.156035013780371395668674 Inexact Rounded
powx3408 power 465.351982159046525762715549761814 240445 -> Infinity Overflow Inexact Rounded
remx3408 remainder 465.351982159046525762715549761814 240444.975944666924657629172844780 -> 465.351982159046525762715549761814
subx3408 subtract 465.351982159046525762715549761814 240444.975944666924657629172844780 -> -239979.623962507878131866457295018 Inexact Rounded
addx3409 add 28066955004783.1076824222873384828 571699969.220753535758504907561016E-718 -> 28066955004783.1076824222873384828 Inexact Rounded
comx3409 compare 28066955004783.1076824222873384828 571699969.220753535758504907561016E-718 -> 1
divx3409 divide 28066955004783.1076824222873384828 571699969.220753535758504907561016E-718 -> 4.90938543219432390013656968123815E+722 Inexact Rounded
dvix3409 divideint 28066955004783.1076824222873384828 571699969.220753535758504907561016E-718 -> NaN Division_impossible
mulx3409 multiply 28066955004783.1076824222873384828 571699969.220753535758504907561016E-718 -> 1.60458773123547770690452195569223E-696 Inexact Rounded
powx3409 power 28066955004783.1076824222873384828 6 -> 4.88845689938951583020171325568218E+80 Inexact Rounded
remx3409 remainder 28066955004783.1076824222873384828 571699969.220753535758504907561016E-718 -> NaN Division_impossible
subx3409 subtract 28066955004783.1076824222873384828 571699969.220753535758504907561016E-718 -> 28066955004783.1076824222873384828 Inexact Rounded
addx3410 add 28275236927392.4960902824105246047 28212038.4825243127096613158419270E+422 -> 2.82120384825243127096613158419270E+429 Inexact Rounded
comx3410 compare 28275236927392.4960902824105246047 28212038.4825243127096613158419270E+422 -> -1
divx3410 divide 28275236927392.4960902824105246047 28212038.4825243127096613158419270E+422 -> 1.00224012330435927467559203688861E-416 Inexact Rounded
dvix3410 divideint 28275236927392.4960902824105246047 28212038.4825243127096613158419270E+422 -> 0
mulx3410 multiply 28275236927392.4960902824105246047 28212038.4825243127096613158419270E+422 -> 7.97702072298089605706798770013561E+442 Inexact Rounded
powx3410 power 28275236927392.4960902824105246047 3 -> 2.26057415546622161347322061281516E+40 Inexact Rounded
remx3410 remainder 28275236927392.4960902824105246047 28212038.4825243127096613158419270E+422 -> 28275236927392.4960902824105246047
subx3410 subtract 28275236927392.4960902824105246047 28212038.4825243127096613158419270E+422 -> -2.82120384825243127096613158419270E+429 Inexact Rounded
addx3411 add 11791.8644211874630234271801789996 -8.45457275930363860982261343159741 -> 11783.4098484281593848173575655680 Inexact Rounded
comx3411 compare 11791.8644211874630234271801789996 -8.45457275930363860982261343159741 -> 1
divx3411 divide 11791.8644211874630234271801789996 -8.45457275930363860982261343159741 -> -1394.73214754836418731335761858151 Inexact Rounded
dvix3411 divideint 11791.8644211874630234271801789996 -8.45457275930363860982261343159741 -> -1394
mulx3411 multiply 11791.8644211874630234271801789996 -8.45457275930363860982261343159741 -> -99695.1757167732926302533138186716 Inexact Rounded
powx3411 power 11791.8644211874630234271801789996 -8 -> 2.67510099318723516565332928253711E-33 Inexact Rounded
remx3411 remainder 11791.8644211874630234271801789996 -8.45457275930363860982261343159741 -> 6.18999471819080133445705535281046
subx3411 subtract 11791.8644211874630234271801789996 -8.45457275930363860982261343159741 -> 11800.3189939467666620370027924312 Inexact Rounded
addx3412 add 44.7085340739581668975502342787578 -9337.05408133023920640485556647937 -> -9292.34554725628103950730533220061 Inexact Rounded
comx3412 compare 44.7085340739581668975502342787578 -9337.05408133023920640485556647937 -> 1
divx3412 divide 44.7085340739581668975502342787578 -9337.05408133023920640485556647937 -> -0.00478829121953512281527242631775613 Inexact Rounded
dvix3412 divideint 44.7085340739581668975502342787578 -9337.05408133023920640485556647937 -> -0
mulx3412 multiply 44.7085340739581668975502342787578 -9337.05408133023920640485556647937 -> -417446.000545543168866158913077419 Inexact Rounded
powx3412 power 44.7085340739581668975502342787578 -9337 -> 0E-10031 Underflow Subnormal Inexact Rounded Clamped
remx3412 remainder 44.7085340739581668975502342787578 -9337.05408133023920640485556647937 -> 44.7085340739581668975502342787578
subx3412 subtract 44.7085340739581668975502342787578 -9337.05408133023920640485556647937 -> 9381.76261540419737330240580075813 Inexact Rounded
addx3413 add 93354527428804.5458053295581965867E+576 -856525909852.318790321300941615314 -> 9.33545274288045458053295581965867E+589 Inexact Rounded
comx3413 compare 93354527428804.5458053295581965867E+576 -856525909852.318790321300941615314 -> 1
divx3413 divide 93354527428804.5458053295581965867E+576 -856525909852.318790321300941615314 -> -1.08992064752484400353231056271614E+578 Inexact Rounded
dvix3413 divideint 93354527428804.5458053295581965867E+576 -856525909852.318790321300941615314 -> NaN Division_impossible
mulx3413 multiply 93354527428804.5458053295581965867E+576 -856525909852.318790321300941615314 -> -7.99605715447900642683774360731254E+601 Inexact Rounded
powx3413 power 93354527428804.5458053295581965867E+576 -9 -> 1.85687015691763406448005521221518E-5310 Inexact Rounded
remx3413 remainder 93354527428804.5458053295581965867E+576 -856525909852.318790321300941615314 -> NaN Division_impossible
subx3413 subtract 93354527428804.5458053295581965867E+576 -856525909852.318790321300941615314 -> 9.33545274288045458053295581965867E+589 Inexact Rounded
addx3414 add -367399415798804503177950040443482 -54845683.9691776397285506712812754 -> -367399415798804503177950095289166 Inexact Rounded
comx3414 compare -367399415798804503177950040443482 -54845683.9691776397285506712812754 -> -1
divx3414 divide -367399415798804503177950040443482 -54845683.9691776397285506712812754 -> 6698784465980529140072174.30474769 Inexact Rounded
dvix3414 divideint -367399415798804503177950040443482 -54845683.9691776397285506712812754 -> 6698784465980529140072174
mulx3414 multiply -367399415798804503177950040443482 -54845683.9691776397285506712812754 -> 2.01502722493617222018040789291414E+40 Inexact Rounded
powx3414 power -367399415798804503177950040443482 -54845684 -> 0E-10031 Underflow Subnormal Inexact Rounded Clamped
remx3414 remainder -367399415798804503177950040443482 -54845683.9691776397285506712812754 -> -16714095.6549657189177857892292804
subx3414 subtract -367399415798804503177950040443482 -54845683.9691776397285506712812754 -> -367399415798804503177949985597798 Inexact Rounded
addx3415 add -2.87155919781024108503670175443740 89529730130.6427881332776797193807 -> 89529730127.7712289354674386343440 Inexact Rounded
comx3415 compare -2.87155919781024108503670175443740 89529730130.6427881332776797193807 -> -1
divx3415 divide -2.87155919781024108503670175443740 89529730130.6427881332776797193807 -> -3.20738060264454013174835928754430E-11 Inexact Rounded
dvix3415 divideint -2.87155919781024108503670175443740 89529730130.6427881332776797193807 -> -0
mulx3415 multiply -2.87155919781024108503670175443740 89529730130.6427881332776797193807 -> -257089920034.115975469931085527642 Inexact Rounded
powx3415 power -2.87155919781024108503670175443740 9 -> -13275.7774683251354527310820885737 Inexact Rounded
remx3415 remainder -2.87155919781024108503670175443740 89529730130.6427881332776797193807 -> -2.87155919781024108503670175443740
subx3415 subtract -2.87155919781024108503670175443740 89529730130.6427881332776797193807 -> -89529730133.5143473310879208044174 Inexact Rounded
addx3416 add -010.693934338179479652178057279204E+188 26484.8887731973153745666514260684 -> -1.06939343381794796521780572792040E+189 Inexact Rounded
comx3416 compare -010.693934338179479652178057279204E+188 26484.8887731973153745666514260684 -> -1
divx3416 divide -010.693934338179479652178057279204E+188 26484.8887731973153745666514260684 -> -4.03774938598259547575707503087638E+184 Inexact Rounded
dvix3416 divideint -010.693934338179479652178057279204E+188 26484.8887731973153745666514260684 -> NaN Division_impossible
mulx3416 multiply -010.693934338179479652178057279204E+188 26484.8887731973153745666514260684 -> -2.83227661494558963558481633880647E+193 Inexact Rounded
powx3416 power -010.693934338179479652178057279204E+188 26485 -> -Infinity Overflow Inexact Rounded
remx3416 remainder -010.693934338179479652178057279204E+188 26484.8887731973153745666514260684 -> NaN Division_impossible
subx3416 subtract -010.693934338179479652178057279204E+188 26484.8887731973153745666514260684 -> -1.06939343381794796521780572792040E+189 Inexact Rounded
addx3417 add 611655569568.832698912762075889186 010182743219.475839030505966016982 -> 621838312788.308537943268041906168
comx3417 compare 611655569568.832698912762075889186 010182743219.475839030505966016982 -> 1
divx3417 divide 611655569568.832698912762075889186 010182743219.475839030505966016982 -> 60.0678575886074367081836436812959 Inexact Rounded
dvix3417 divideint 611655569568.832698912762075889186 010182743219.475839030505966016982 -> 60
mulx3417 multiply 611655569568.832698912762075889186 010182743219.475839030505966016982 -> 6228331603681663511826.60450280350 Inexact Rounded
powx3417 power 611655569568.832698912762075889186 1 -> 611655569568.832698912762075889186
remx3417 remainder 611655569568.832698912762075889186 010182743219.475839030505966016982 -> 690976400.282357082404114870266
subx3417 subtract 611655569568.832698912762075889186 010182743219.475839030505966016982 -> 601472826349.356859882256109872204
addx3418 add 3457947.39062863674882672518304442 -01.9995218868908849056866549811425 -> 3457945.39110674985794181949638944 Inexact Rounded
comx3418 compare 3457947.39062863674882672518304442 -01.9995218868908849056866549811425 -> 1
divx3418 divide 3457947.39062863674882672518304442 -01.9995218868908849056866549811425 -> -1729387.11663991852426428263230475 Inexact Rounded
dvix3418 divideint 3457947.39062863674882672518304442 -01.9995218868908849056866549811425 -> -1729387
mulx3418 multiply 3457947.39062863674882672518304442 -01.9995218868908849056866549811425 -> -6914241.49127918361259252956576654 Inexact Rounded
powx3418 power 3457947.39062863674882672518304442 -2 -> 8.36302195229701913376802373659526E-14 Inexact Rounded
remx3418 remainder 3457947.39062863674882672518304442 -01.9995218868908849056866549811425 -> 0.2332240699744359979851713353525
subx3418 subtract 3457947.39062863674882672518304442 -01.9995218868908849056866549811425 -> 3457949.39015052363971163086969940 Inexact Rounded
addx3419 add -53308666960535.7393391289364591513 -6527.00547629475578694521436764596E-442 -> -53308666960535.7393391289364591513 Inexact Rounded
comx3419 compare -53308666960535.7393391289364591513 -6527.00547629475578694521436764596E-442 -> -1
divx3419 divide -53308666960535.7393391289364591513 -6527.00547629475578694521436764596E-442 -> 8.16740037282731870883136714441204E+451 Inexact Rounded
dvix3419 divideint -53308666960535.7393391289364591513 -6527.00547629475578694521436764596E-442 -> NaN Division_impossible
mulx3419 multiply -53308666960535.7393391289364591513 -6527.00547629475578694521436764596E-442 -> 3.47945961185390084641156250100085E-425 Inexact Rounded
powx3419 power -53308666960535.7393391289364591513 -7 -> -8.17363502380497033342380498988958E-97 Inexact Rounded
remx3419 remainder -53308666960535.7393391289364591513 -6527.00547629475578694521436764596E-442 -> NaN Division_impossible
subx3419 subtract -53308666960535.7393391289364591513 -6527.00547629475578694521436764596E-442 -> -53308666960535.7393391289364591513 Inexact Rounded
addx3420 add -5568057.17870139549478277980540034 -407906443.141342175740471849723638 -> -413474500.320043571235254629529038 Inexact Rounded
comx3420 compare -5568057.17870139549478277980540034 -407906443.141342175740471849723638 -> 1
divx3420 divide -5568057.17870139549478277980540034 -407906443.141342175740471849723638 -> 0.0136503290701197094953429018013146 Inexact Rounded
dvix3420 divideint -5568057.17870139549478277980540034 -407906443.141342175740471849723638 -> 0
mulx3420 multiply -5568057.17870139549478277980540034 -407906443.141342175740471849723638 -> 2271246398971702.91169807728132089 Inexact Rounded
powx3420 power -5568057.17870139549478277980540034 -407906443 -> -0E-10031 Underflow Subnormal Inexact Rounded Clamped
remx3420 remainder -5568057.17870139549478277980540034 -407906443.141342175740471849723638 -> -5568057.17870139549478277980540034
subx3420 subtract -5568057.17870139549478277980540034 -407906443.141342175740471849723638 -> 402338385.962640780245689069918238 Inexact Rounded
addx3421 add 9804385273.49533524416415189990857 84.1433929743544659553964804646569 -> 9804385357.63872821851861785530505 Inexact Rounded
comx3421 compare 9804385273.49533524416415189990857 84.1433929743544659553964804646569 -> 1
divx3421 divide 9804385273.49533524416415189990857 84.1433929743544659553964804646569 -> 116519965.821719977402398190558439 Inexact Rounded
dvix3421 divideint 9804385273.49533524416415189990857 84.1433929743544659553964804646569 -> 116519965
mulx3421 multiply 9804385273.49533524416415189990857 84.1433929743544659553964804646569 -> 824974242939.691780798621180901714 Inexact Rounded
powx3421 power 9804385273.49533524416415189990857 84 -> 1.90244010779692739037080418507909E+839 Inexact Rounded
remx3421 remainder 9804385273.49533524416415189990857 84.1433929743544659553964804646569 -> 69.1423069734476624350435642749915
subx3421 subtract 9804385273.49533524416415189990857 84.1433929743544659553964804646569 -> 9804385189.35194226980968594451209 Inexact Rounded
addx3422 add -5234910986592.18801727046580014273E-547 -5874220715892.91440069210512515154 -> -5874220715892.91440069210512515154 Inexact Rounded
comx3422 compare -5234910986592.18801727046580014273E-547 -5874220715892.91440069210512515154 -> 1
divx3422 divide -5234910986592.18801727046580014273E-547 -5874220715892.91440069210512515154 -> 8.91166886601477021757439826903776E-548 Inexact Rounded
dvix3422 divideint -5234910986592.18801727046580014273E-547 -5874220715892.91440069210512515154 -> 0
mulx3422 multiply -5234910986592.18801727046580014273E-547 -5874220715892.91440069210512515154 -> 3.07510225632952455144944282925583E-522 Inexact Rounded
powx3422 power -5234910986592.18801727046580014273E-547 -6 -> 4.85896970703117149235935037271084E+3205 Inexact Rounded
remx3422 remainder -5234910986592.18801727046580014273E-547 -5874220715892.91440069210512515154 -> -5.23491098659218801727046580014273E-535
subx3422 subtract -5234910986592.18801727046580014273E-547 -5874220715892.91440069210512515154 -> 5874220715892.91440069210512515154 Inexact Rounded
addx3423 add 698416560151955285929747633786867E-495 51754681.6784872628933218985216916E-266 -> 5.17546816784872628933218985216916E-259 Inexact Rounded
comx3423 compare 698416560151955285929747633786867E-495 51754681.6784872628933218985216916E-266 -> -1
divx3423 divide 698416560151955285929747633786867E-495 51754681.6784872628933218985216916E-266 -> 1.34947513442491971488363250398908E-204 Inexact Rounded
dvix3423 divideint 698416560151955285929747633786867E-495 51754681.6784872628933218985216916E-266 -> 0
mulx3423 multiply 698416560151955285929747633786867E-495 51754681.6784872628933218985216916E-266 -> 3.61463267496484976064271305679796E-721 Inexact Rounded
powx3423 power 698416560151955285929747633786867E-495 5 -> 1.66177661007189430761396979787413E-2311 Inexact Rounded
remx3423 remainder 698416560151955285929747633786867E-495 51754681.6784872628933218985216916E-266 -> 6.98416560151955285929747633786867E-463
subx3423 subtract 698416560151955285929747633786867E-495 51754681.6784872628933218985216916E-266 -> -5.17546816784872628933218985216916E-259 Inexact Rounded
addx3424 add 107635.497735316515080720330536027 -3972075.83989512668362609609006425E-605 -> 107635.497735316515080720330536027 Inexact Rounded
comx3424 compare 107635.497735316515080720330536027 -3972075.83989512668362609609006425E-605 -> 1
divx3424 divide 107635.497735316515080720330536027 -3972075.83989512668362609609006425E-605 -> -2.70980469844599888443309571235597E+603 Inexact Rounded
dvix3424 divideint 107635.497735316515080720330536027 -3972075.83989512668362609609006425E-605 -> NaN Division_impossible
mulx3424 multiply 107635.497735316515080720330536027 -3972075.83989512668362609609006425E-605 -> -4.27536360069537352698066408021773E-594 Inexact Rounded
powx3424 power 107635.497735316515080720330536027 -4 -> 7.45037111502910487803432806334714E-21 Inexact Rounded
remx3424 remainder 107635.497735316515080720330536027 -3972075.83989512668362609609006425E-605 -> NaN Division_impossible
subx3424 subtract 107635.497735316515080720330536027 -3972075.83989512668362609609006425E-605 -> 107635.497735316515080720330536027 Inexact Rounded
addx3425 add -32174291345686.5371446616670961807 79518863759385.5925052747867099091E+408 -> 7.95188637593855925052747867099091E+421 Inexact Rounded
comx3425 compare -32174291345686.5371446616670961807 79518863759385.5925052747867099091E+408 -> -1
divx3425 divide -32174291345686.5371446616670961807 79518863759385.5925052747867099091E+408 -> -4.04612060894658007715621807881076E-409 Inexact Rounded
dvix3425 divideint -32174291345686.5371446616670961807 79518863759385.5925052747867099091E+408 -> -0
mulx3425 multiply -32174291345686.5371446616670961807 79518863759385.5925052747867099091E+408 -> -2.55846309007242668513226814043593E+435 Inexact Rounded
powx3425 power -32174291345686.5371446616670961807 8 -> 1.14834377656109143210058690590666E+108 Inexact Rounded
remx3425 remainder -32174291345686.5371446616670961807 79518863759385.5925052747867099091E+408 -> -32174291345686.5371446616670961807
subx3425 subtract -32174291345686.5371446616670961807 79518863759385.5925052747867099091E+408 -> -7.95188637593855925052747867099091E+421 Inexact Rounded
addx3426 add -8151730494.53190523620899410544099E+688 -93173.0631474527142307644239919480E+900 -> -9.31730631474527142307644239919480E+904 Inexact Rounded
comx3426 compare -8151730494.53190523620899410544099E+688 -93173.0631474527142307644239919480E+900 -> 1
divx3426 divide -8151730494.53190523620899410544099E+688 -93173.0631474527142307644239919480E+900 -> 8.74902060655796717043678441884283E-208 Inexact Rounded
dvix3426 divideint -8151730494.53190523620899410544099E+688 -93173.0631474527142307644239919480E+900 -> 0
mulx3426 multiply -8151730494.53190523620899410544099E+688 -93173.0631474527142307644239919480E+900 -> 7.59521700128037149179751467730962E+1602 Inexact Rounded
powx3426 power -8151730494.53190523620899410544099E+688 -9 -> -6.29146352774842448375275282183700E-6282 Inexact Rounded
remx3426 remainder -8151730494.53190523620899410544099E+688 -93173.0631474527142307644239919480E+900 -> -8.15173049453190523620899410544099E+697
subx3426 subtract -8151730494.53190523620899410544099E+688 -93173.0631474527142307644239919480E+900 -> 9.31730631474527142307644239919480E+904 Inexact Rounded
addx3427 add 1.33649801345976199708341799505220 -56623.0530039528969825480755159562E+459 -> -5.66230530039528969825480755159562E+463 Inexact Rounded
comx3427 compare 1.33649801345976199708341799505220 -56623.0530039528969825480755159562E+459 -> 1
divx3427 divide 1.33649801345976199708341799505220 -56623.0530039528969825480755159562E+459 -> -2.36034255052700900395787131334608E-464 Inexact Rounded
dvix3427 divideint 1.33649801345976199708341799505220 -56623.0530039528969825480755159562E+459 -> -0
mulx3427 multiply 1.33649801345976199708341799505220 -56623.0530039528969825480755159562E+459 -> -7.56765978558098558928268129700052E+463 Inexact Rounded
powx3427 power 1.33649801345976199708341799505220 -6 -> 0.175464835912284900180305028965188 Inexact Rounded
remx3427 remainder 1.33649801345976199708341799505220 -56623.0530039528969825480755159562E+459 -> 1.33649801345976199708341799505220
subx3427 subtract 1.33649801345976199708341799505220 -56623.0530039528969825480755159562E+459 -> 5.66230530039528969825480755159562E+463 Inexact Rounded
addx3428 add 67762238162788.6551061476018185196 -6140.75837959248100352788853809376E-822 -> 67762238162788.6551061476018185196 Inexact Rounded
comx3428 compare 67762238162788.6551061476018185196 -6140.75837959248100352788853809376E-822 -> 1
divx3428 divide 67762238162788.6551061476018185196 -6140.75837959248100352788853809376E-822 -> -1.10348321777294157014941951870409E+832 Inexact Rounded
dvix3428 divideint 67762238162788.6551061476018185196 -6140.75837959248100352788853809376E-822 -> NaN Division_impossible
mulx3428 multiply 67762238162788.6551061476018185196 -6140.75837959248100352788853809376E-822 -> -4.16111531818085838717201828773857E-805 Inexact Rounded
powx3428 power 67762238162788.6551061476018185196 -6 -> 1.03293631708006509074972764670281E-83 Inexact Rounded
remx3428 remainder 67762238162788.6551061476018185196 -6140.75837959248100352788853809376E-822 -> NaN Division_impossible
subx3428 subtract 67762238162788.6551061476018185196 -6140.75837959248100352788853809376E-822 -> 67762238162788.6551061476018185196 Inexact Rounded
addx3429 add 4286562.76568866751577306056498271 6286.77291578497580015557979349893E+820 -> 6.28677291578497580015557979349893E+823 Inexact Rounded
comx3429 compare 4286562.76568866751577306056498271 6286.77291578497580015557979349893E+820 -> -1
divx3429 divide 4286562.76568866751577306056498271 6286.77291578497580015557979349893E+820 -> 6.81838333133660025740681459349372E-818 Inexact Rounded
dvix3429 divideint 4286562.76568866751577306056498271 6286.77291578497580015557979349893E+820 -> 0
mulx3429 multiply 4286562.76568866751577306056498271 6286.77291578497580015557979349893E+820 -> 2.69486466971438542975159893306219E+830 Inexact Rounded
powx3429 power 4286562.76568866751577306056498271 6 -> 6.20376193064412081058181881805108E+39 Inexact Rounded
remx3429 remainder 4286562.76568866751577306056498271 6286.77291578497580015557979349893E+820 -> 4286562.76568866751577306056498271
subx3429 subtract 4286562.76568866751577306056498271 6286.77291578497580015557979349893E+820 -> -6.28677291578497580015557979349893E+823 Inexact Rounded
addx3430 add -765782.827432642697305644096365566 67.1634368459576834692758114618652 -> -765715.663995796739622174820554104 Inexact Rounded
comx3430 compare -765782.827432642697305644096365566 67.1634368459576834692758114618652 -> -1
divx3430 divide -765782.827432642697305644096365566 67.1634368459576834692758114618652 -> -11401.7814363639478774761697650867 Inexact Rounded
dvix3430 divideint -765782.827432642697305644096365566 67.1634368459576834692758114618652 -> -11401
mulx3430 multiply -765782.827432642697305644096365566 67.1634368459576834692758114618652 -> -51432606.5679912088468256122315944 Inexact Rounded
powx3430 power -765782.827432642697305644096365566 67 -> -1.71821200770749773595473594136582E+394 Inexact Rounded
remx3430 remainder -765782.827432642697305644096365566 67.1634368459576834692758114618652 -> -52.4839518791480724305698888408548
subx3430 subtract -765782.827432642697305644096365566 67.1634368459576834692758114618652 -> -765849.990869488654989113372177028 Inexact Rounded
addx3431 add 46.2835931916106252756465724211276 59.2989237834093118332826617957791 -> 105.582516975019937108929234216907 Inexact Rounded
comx3431 compare 46.2835931916106252756465724211276 59.2989237834093118332826617957791 -> -1
divx3431 divide 46.2835931916106252756465724211276 59.2989237834093118332826617957791 -> 0.780513207299722975882416995140701 Inexact Rounded
dvix3431 divideint 46.2835931916106252756465724211276 59.2989237834093118332826617957791 -> 0
mulx3431 multiply 46.2835931916106252756465724211276 59.2989237834093118332826617957791 -> 2744.56726509164060561370653286614 Inexact Rounded
powx3431 power 46.2835931916106252756465724211276 59 -> 1.82052645780601002671007943923993E+98 Inexact Rounded
remx3431 remainder 46.2835931916106252756465724211276 59.2989237834093118332826617957791 -> 46.2835931916106252756465724211276
subx3431 subtract 46.2835931916106252756465724211276 59.2989237834093118332826617957791 -> -13.0153305917986865576360893746515
addx3432 add -3029555.82298840234029474459694644 857535844655004737373089601128532 -> 857535844655004737373089598098976 Inexact Rounded
comx3432 compare -3029555.82298840234029474459694644 857535844655004737373089601128532 -> -1
divx3432 divide -3029555.82298840234029474459694644 857535844655004737373089601128532 -> -3.53286202771759704502126811323937E-27 Inexact Rounded
dvix3432 divideint -3029555.82298840234029474459694644 857535844655004737373089601128532 -> -0
mulx3432 multiply -3029555.82298840234029474459694644 857535844655004737373089601128532 -> -2.59795271159584761928986181925721E+39 Inexact Rounded
powx3432 power -3029555.82298840234029474459694644 9 -> -2.14986224790431302561340100746360E+58 Inexact Rounded
remx3432 remainder -3029555.82298840234029474459694644 857535844655004737373089601128532 -> -3029555.82298840234029474459694644
subx3432 subtract -3029555.82298840234029474459694644 857535844655004737373089601128532 -> -857535844655004737373089604158088 Inexact Rounded
addx3433 add -0138466789523.10694176543700501945E-948 481026979918882487383654367924619 -> 481026979918882487383654367924619 Inexact Rounded
comx3433 compare -0138466789523.10694176543700501945E-948 481026979918882487383654367924619 -> -1
divx3433 divide -0138466789523.10694176543700501945E-948 481026979918882487383654367924619 -> -2.87856597038397207797777811199804E-970 Inexact Rounded
dvix3433 divideint -0138466789523.10694176543700501945E-948 481026979918882487383654367924619 -> -0
mulx3433 multiply -0138466789523.10694176543700501945E-948 481026979918882487383654367924619 -> -6.66062615833636908683785283687416E-905 Inexact Rounded
powx3433 power -0138466789523.10694176543700501945E-948 5 -> -5.09012109092637525843636056746667E-4685 Inexact Rounded
remx3433 remainder -0138466789523.10694176543700501945E-948 481026979918882487383654367924619 -> -1.3846678952310694176543700501945E-937
subx3433 subtract -0138466789523.10694176543700501945E-948 481026979918882487383654367924619 -> -481026979918882487383654367924619 Inexact Rounded
addx3434 add -9593566466.96690575714244442109870 -87632034347.4845477961976776833770E+769 -> -8.76320343474845477961976776833770E+779 Inexact Rounded
comx3434 compare -9593566466.96690575714244442109870 -87632034347.4845477961976776833770E+769 -> 1
divx3434 divide -9593566466.96690575714244442109870 -87632034347.4845477961976776833770E+769 -> 1.09475564939253134070730299863765E-770 Inexact Rounded
dvix3434 divideint -9593566466.96690575714244442109870 -87632034347.4845477961976776833770E+769 -> 0
mulx3434 multiply -9593566466.96690575714244442109870 -87632034347.4845477961976776833770E+769 -> 8.40703746148119867711463485065336E+789 Inexact Rounded
powx3434 power -9593566466.96690575714244442109870 -9 -> -1.45271091841882960010964421066745E-90 Inexact Rounded
remx3434 remainder -9593566466.96690575714244442109870 -87632034347.4845477961976776833770E+769 -> -9593566466.96690575714244442109870
subx3434 subtract -9593566466.96690575714244442109870 -87632034347.4845477961976776833770E+769 -> 8.76320343474845477961976776833770E+779 Inexact Rounded
addx3435 add -3189.30765477670526823106100241863E-898 565688889.355241946154894311253202E-466 -> 5.65688889355241946154894311253202E-458 Inexact Rounded
comx3435 compare -3189.30765477670526823106100241863E-898 565688889.355241946154894311253202E-466 -> -1
divx3435 divide -3189.30765477670526823106100241863E-898 565688889.355241946154894311253202E-466 -> -5.63791814686655486612569970629128E-438 Inexact Rounded
dvix3435 divideint -3189.30765477670526823106100241863E-898 565688889.355241946154894311253202E-466 -> -0
mulx3435 multiply -3189.30765477670526823106100241863E-898 565688889.355241946154894311253202E-466 -> -1.80415590504280580443565448126548E-1352 Inexact Rounded
powx3435 power -3189.30765477670526823106100241863E-898 6 -> 1.05239431027683904514311527228736E-5367 Inexact Rounded
remx3435 remainder -3189.30765477670526823106100241863E-898 565688889.355241946154894311253202E-466 -> -3.18930765477670526823106100241863E-895
subx3435 subtract -3189.30765477670526823106100241863E-898 565688889.355241946154894311253202E-466 -> -5.65688889355241946154894311253202E-458 Inexact Rounded
addx3436 add -17084552395.6714834680088150543965 -631925802672.685034379197328370812E+527 -> -6.31925802672685034379197328370812E+538 Inexact Rounded
comx3436 compare -17084552395.6714834680088150543965 -631925802672.685034379197328370812E+527 -> 1
divx3436 divide -17084552395.6714834680088150543965 -631925802672.685034379197328370812E+527 -> 2.70356936263934622050341328519534E-529 Inexact Rounded
dvix3436 divideint -17084552395.6714834680088150543965 -631925802672.685034379197328370812E+527 -> 0
mulx3436 multiply -17084552395.6714834680088150543965 -631925802672.685034379197328370812E+527 -> 1.07961694859382462346866817306769E+549 Inexact Rounded
powx3436 power -17084552395.6714834680088150543965 -6 -> 4.02141014977177984123011868387622E-62 Inexact Rounded
remx3436 remainder -17084552395.6714834680088150543965 -631925802672.685034379197328370812E+527 -> -17084552395.6714834680088150543965
subx3436 subtract -17084552395.6714834680088150543965 -631925802672.685034379197328370812E+527 -> 6.31925802672685034379197328370812E+538 Inexact Rounded
addx3437 add 034956830.349823306815911887469760 -61600816.0672274126966042956781665E-667 -> 34956830.3498233068159118874697600 Inexact Rounded
comx3437 compare 034956830.349823306815911887469760 -61600816.0672274126966042956781665E-667 -> 1
divx3437 divide 034956830.349823306815911887469760 -61600816.0672274126966042956781665E-667 -> -5.67473494371787737607169979602343E+666 Inexact Rounded
dvix3437 divideint 034956830.349823306815911887469760 -61600816.0672274126966042956781665E-667 -> NaN Division_impossible
mulx3437 multiply 034956830.349823306815911887469760 -61600816.0672274126966042956781665E-667 -> -2.15336927667273841617128781173293E-652 Inexact Rounded
powx3437 power 034956830.349823306815911887469760 -6 -> 5.48034272566098493462169431762597E-46 Inexact Rounded
remx3437 remainder 034956830.349823306815911887469760 -61600816.0672274126966042956781665E-667 -> NaN Division_impossible
subx3437 subtract 034956830.349823306815911887469760 -61600816.0672274126966042956781665E-667 -> 34956830.3498233068159118874697600 Inexact Rounded
addx3438 add -763.440067781256632695791981893608 19.9263811350611007833220620745413 -> -743.513686646195531912469919819067 Inexact Rounded
comx3438 compare -763.440067781256632695791981893608 19.9263811350611007833220620745413 -> -1
divx3438 divide -763.440067781256632695791981893608 19.9263811350611007833220620745413 -> -38.3130314835722766807703585435688 Inexact Rounded
dvix3438 divideint -763.440067781256632695791981893608 19.9263811350611007833220620745413 -> -38
mulx3438 multiply -763.440067781256632695791981893608 19.9263811350611007833220620745413 -> -15212.5977643862002585039364868883 Inexact Rounded
powx3438 power -763.440067781256632695791981893608 20 -> 4.52375407727336769552481661250924E+57 Inexact Rounded
remx3438 remainder -763.440067781256632695791981893608 19.9263811350611007833220620745413 -> -6.2375846489348029295536230610386
subx3438 subtract -763.440067781256632695791981893608 19.9263811350611007833220620745413 -> -783.366448916317733479114043968149 Inexact Rounded
addx3439 add -510472027868440667684575147556654E+789 834872378550801889983927148587909 -> -5.10472027868440667684575147556654E+821 Inexact Rounded
comx3439 compare -510472027868440667684575147556654E+789 834872378550801889983927148587909 -> -1
divx3439 divide -510472027868440667684575147556654E+789 834872378550801889983927148587909 -> -6.11437198047603754107526874071737E+788 Inexact Rounded
dvix3439 divideint -510472027868440667684575147556654E+789 834872378550801889983927148587909 -> NaN Division_impossible
mulx3439 multiply -510472027868440667684575147556654E+789 834872378550801889983927148587909 -> -4.26178996090176289115594057419892E+854 Inexact Rounded
powx3439 power -510472027868440667684575147556654E+789 8 -> 4.61079266619522147262600755274182E+6573 Inexact Rounded
remx3439 remainder -510472027868440667684575147556654E+789 834872378550801889983927148587909 -> NaN Division_impossible
subx3439 subtract -510472027868440667684575147556654E+789 834872378550801889983927148587909 -> -5.10472027868440667684575147556654E+821 Inexact Rounded
addx3440 add 070304761.560517086676993503034828E-094 -17773.7446959771077104057845273992E-761 -> 7.03047615605170866769935030348280E-87 Inexact Rounded
comx3440 compare 070304761.560517086676993503034828E-094 -17773.7446959771077104057845273992E-761 -> 1
divx3440 divide 070304761.560517086676993503034828E-094 -17773.7446959771077104057845273992E-761 -> -3.95554019499502537743883483402608E+670 Inexact Rounded
dvix3440 divideint 070304761.560517086676993503034828E-094 -17773.7446959771077104057845273992E-761 -> NaN Division_impossible
mulx3440 multiply 070304761.560517086676993503034828E-094 -17773.7446959771077104057845273992E-761 -> -1.24957888288817581538108991453732E-843 Inexact Rounded
powx3440 power 070304761.560517086676993503034828E-094 -2 -> 2.02316135427631488479902919959627E+172 Inexact Rounded
remx3440 remainder 070304761.560517086676993503034828E-094 -17773.7446959771077104057845273992E-761 -> NaN Division_impossible
subx3440 subtract 070304761.560517086676993503034828E-094 -17773.7446959771077104057845273992E-761 -> 7.03047615605170866769935030348280E-87 Inexact Rounded
addx3441 add -0970725697662.27605454336231195463 -4541.41897546697187157913886433474 -> -970725702203.695030010334183533769 Inexact Rounded
comx3441 compare -0970725697662.27605454336231195463 -4541.41897546697187157913886433474 -> -1
divx3441 divide -0970725697662.27605454336231195463 -4541.41897546697187157913886433474 -> 213749425.654447811698884007553614 Inexact Rounded
dvix3441 divideint -0970725697662.27605454336231195463 -4541.41897546697187157913886433474 -> 213749425
mulx3441 multiply -0970725697662.27605454336231195463 -4541.41897546697187157913886433474 -> 4408472103336875.21161867891724392 Inexact Rounded
powx3441 power -0970725697662.27605454336231195463 -4541 -> -0E-10031 Underflow Subnormal Inexact Rounded Clamped
remx3441 remainder -0970725697662.27605454336231195463 -4541.41897546697187157913886433474 -> -2972.12171050214753770792631747550
subx3441 subtract -0970725697662.27605454336231195463 -4541.41897546697187157913886433474 -> -970725693120.857079076390440375491 Inexact Rounded
addx3442 add -808178238631844268316111259558675 -598400.265108644514211244980426520 -> -808178238631844268316111260157075 Inexact Rounded
comx3442 compare -808178238631844268316111259558675 -598400.265108644514211244980426520 -> -1
divx3442 divide -808178238631844268316111259558675 -598400.265108644514211244980426520 -> 1350564640015847635178945884.97836 Inexact Rounded
dvix3442 divideint -808178238631844268316111259558675 -598400.265108644514211244980426520 -> 1350564640015847635178945884
mulx3442 multiply -808178238631844268316111259558675 -598400.265108644514211244980426520 -> 4.83614072252332979731348423145208E+38 Inexact Rounded
powx3442 power -808178238631844268316111259558675 -598400 -> 0E-10031 Underflow Subnormal Inexact Rounded Clamped
remx3442 remainder -808178238631844268316111259558675 -598400.265108644514211244980426520 -> -585452.097764536570956813681556320
subx3442 subtract -808178238631844268316111259558675 -598400.265108644514211244980426520 -> -808178238631844268316111258960275 Inexact Rounded
addx3443 add -9.90826595069053564311371766315200 -031.625916781307847864872329806646 -> -41.5341827319983835079860474697980 Rounded
comx3443 compare -9.90826595069053564311371766315200 -031.625916781307847864872329806646 -> 1
divx3443 divide -9.90826595069053564311371766315200 -031.625916781307847864872329806646 -> 0.313295770023233218639213140599856 Inexact Rounded
dvix3443 divideint -9.90826595069053564311371766315200 -031.625916781307847864872329806646 -> 0
mulx3443 multiply -9.90826595069053564311371766315200 -031.625916781307847864872329806646 -> 313.357994403604968250936036978086 Inexact Rounded
powx3443 power -9.90826595069053564311371766315200 -32 -> 1.34299698259038003011439568004625E-32 Inexact Rounded
remx3443 remainder -9.90826595069053564311371766315200 -031.625916781307847864872329806646 -> -9.90826595069053564311371766315200
subx3443 subtract -9.90826595069053564311371766315200 -031.625916781307847864872329806646 -> 21.7176508306173122217586121434940 Rounded
addx3444 add -196925.469891897719160698483752907 -41268.9975444533794067723958739778 -> -238194.467436351098567470879626885 Inexact Rounded
comx3444 compare -196925.469891897719160698483752907 -41268.9975444533794067723958739778 -> -1
divx3444 divide -196925.469891897719160698483752907 -41268.9975444533794067723958739778 -> 4.77175317088274715226553516820589 Inexact Rounded
dvix3444 divideint -196925.469891897719160698483752907 -41268.9975444533794067723958739778 -> 4
mulx3444 multiply -196925.469891897719160698483752907 -41268.9975444533794067723958739778 -> 8126916733.40905487026003135987472 Inexact Rounded
powx3444 power -196925.469891897719160698483752907 -41269 -> -0E-10031 Underflow Subnormal Inexact Rounded Clamped
remx3444 remainder -196925.469891897719160698483752907 -41268.9975444533794067723958739778 -> -31849.4797140842015336089002569958
subx3444 subtract -196925.469891897719160698483752907 -41268.9975444533794067723958739778 -> -155656.472347444339753926087878929 Inexact Rounded
addx3445 add 421071135212152225162086005824310 1335320330.08964354845796510145246E-604 -> 421071135212152225162086005824310 Inexact Rounded
comx3445 compare 421071135212152225162086005824310 1335320330.08964354845796510145246E-604 -> 1
divx3445 divide 421071135212152225162086005824310 1335320330.08964354845796510145246E-604 -> 3.15333426537349744281860005497304E+627 Inexact Rounded
dvix3445 divideint 421071135212152225162086005824310 1335320330.08964354845796510145246E-604 -> NaN Division_impossible
mulx3445 multiply 421071135212152225162086005824310 1335320330.08964354845796510145246E-604 -> 5.62264847262712040027311932121460E-563 Inexact Rounded
powx3445 power 421071135212152225162086005824310 1 -> 421071135212152225162086005824310
remx3445 remainder 421071135212152225162086005824310 1335320330.08964354845796510145246E-604 -> NaN Division_impossible
subx3445 subtract 421071135212152225162086005824310 1335320330.08964354845796510145246E-604 -> 421071135212152225162086005824310 Inexact Rounded
addx3446 add 1249441.46421514282301182772247227 -0289848.71208912281976374705180836E-676 -> 1249441.46421514282301182772247227 Inexact Rounded
comx3446 compare 1249441.46421514282301182772247227 -0289848.71208912281976374705180836E-676 -> 1
divx3446 divide 1249441.46421514282301182772247227 -0289848.71208912281976374705180836E-676 -> -4.31066764178328992440635387255816E+676 Inexact Rounded
dvix3446 divideint 1249441.46421514282301182772247227 -0289848.71208912281976374705180836E-676 -> NaN Division_impossible
mulx3446 multiply 1249441.46421514282301182772247227 -0289848.71208912281976374705180836E-676 -> -3.62148999233506984566620611700349E-665 Inexact Rounded
powx3446 power 1249441.46421514282301182772247227 -3 -> 5.12686942572191282348415024932322E-19 Inexact Rounded
remx3446 remainder 1249441.46421514282301182772247227 -0289848.71208912281976374705180836E-676 -> NaN Division_impossible
subx3446 subtract 1249441.46421514282301182772247227 -0289848.71208912281976374705180836E-676 -> 1249441.46421514282301182772247227 Inexact Rounded
addx3447 add 74815000.4716875558358937279052903 -690425401708167622194241915195001E+891 -> -6.90425401708167622194241915195001E+923 Inexact Rounded
comx3447 compare 74815000.4716875558358937279052903 -690425401708167622194241915195001E+891 -> 1
divx3447 divide 74815000.4716875558358937279052903 -690425401708167622194241915195001E+891 -> -1.08360729901578455109968388309079E-916 Inexact Rounded
dvix3447 divideint 74815000.4716875558358937279052903 -690425401708167622194241915195001E+891 -> -0
mulx3447 multiply 74815000.4716875558358937279052903 -690425401708167622194241915195001E+891 -> -5.16541767544616308732028810026275E+931 Inexact Rounded
powx3447 power 74815000.4716875558358937279052903 -7 -> 7.62218032252683815537906972439985E-56 Inexact Rounded
remx3447 remainder 74815000.4716875558358937279052903 -690425401708167622194241915195001E+891 -> 74815000.4716875558358937279052903
subx3447 subtract 74815000.4716875558358937279052903 -690425401708167622194241915195001E+891 -> 6.90425401708167622194241915195001E+923 Inexact Rounded
addx3448 add -1683993.51210241555668790556759021 -72394384927344.8402585228267493374 -> -72394386611338.3523609383834372430 Inexact Rounded
comx3448 compare -1683993.51210241555668790556759021 -72394384927344.8402585228267493374 -> 1
divx3448 divide -1683993.51210241555668790556759021 -72394384927344.8402585228267493374 -> 2.32613829621244113284301004158794E-8 Inexact Rounded
dvix3448 divideint -1683993.51210241555668790556759021 -72394384927344.8402585228267493374 -> 0
mulx3448 multiply -1683993.51210241555668790556759021 -72394384927344.8402585228267493374 -> 121911674530293613615.441384822381 Inexact Rounded
powx3448 power -1683993.51210241555668790556759021 -7 -> -2.60385683509956889000676113860292E-44 Inexact Rounded
remx3448 remainder -1683993.51210241555668790556759021 -72394384927344.8402585228267493374 -> -1683993.51210241555668790556759021
subx3448 subtract -1683993.51210241555668790556759021 -72394384927344.8402585228267493374 -> 72394383243351.3281561072700614318 Inexact Rounded
addx3449 add -763.148530974741766171756970448158 517370.808956957601473642272664647 -> 516607.660425982859707470515694199 Inexact Rounded
comx3449 compare -763.148530974741766171756970448158 517370.808956957601473642272664647 -> -1
divx3449 divide -763.148530974741766171756970448158 517370.808956957601473642272664647 -> -0.00147505139014951946381155525173867 Inexact Rounded
dvix3449 divideint -763.148530974741766171756970448158 517370.808956957601473642272664647 -> -0
mulx3449 multiply -763.148530974741766171756970448158 517370.808956957601473642272664647 -> -394830772.824715962925351447322187 Inexact Rounded
powx3449 power -763.148530974741766171756970448158 517371 -> -Infinity Overflow Inexact Rounded
remx3449 remainder -763.148530974741766171756970448158 517370.808956957601473642272664647 -> -763.148530974741766171756970448158
subx3449 subtract -763.148530974741766171756970448158 517370.808956957601473642272664647 -> -518133.957487932343239814029635095 Inexact Rounded
addx3450 add -77.5841338812312523460591226178754 -927540422.641025050968830154578151E+524 -> -9.27540422641025050968830154578151E+532 Inexact Rounded
comx3450 compare -77.5841338812312523460591226178754 -927540422.641025050968830154578151E+524 -> 1
divx3450 divide -77.5841338812312523460591226178754 -927540422.641025050968830154578151E+524 -> 8.36450164191471769978415758342237E-532 Inexact Rounded
dvix3450 divideint -77.5841338812312523460591226178754 -927540422.641025050968830154578151E+524 -> 0
mulx3450 multiply -77.5841338812312523460591226178754 -927540422.641025050968830154578151E+524 -> 7.19624203304351070562409746475943E+534 Inexact Rounded
powx3450 power -77.5841338812312523460591226178754 -9 -> -9.81846856873938549466341693997829E-18 Inexact Rounded
remx3450 remainder -77.5841338812312523460591226178754 -927540422.641025050968830154578151E+524 -> -77.5841338812312523460591226178754
subx3450 subtract -77.5841338812312523460591226178754 -927540422.641025050968830154578151E+524 -> 9.27540422641025050968830154578151E+532 Inexact Rounded
addx3451 add 5176295309.89943746236102209837813 -129733.103628797477167908698565465 -> 5176165576.79580866488385418967956 Inexact Rounded
comx3451 compare 5176295309.89943746236102209837813 -129733.103628797477167908698565465 -> 1
divx3451 divide 5176295309.89943746236102209837813 -129733.103628797477167908698565465 -> -39899.5720067736855444089432524094 Inexact Rounded
dvix3451 divideint 5176295309.89943746236102209837813 -129733.103628797477167908698565465 -> -39899
mulx3451 multiply 5176295309.89943746236102209837813 -129733.103628797477167908698565465 -> -671536855852442.071887385512001794 Inexact Rounded
powx3451 power 5176295309.89943746236102209837813 -129733 -> 0E-10031 Underflow Subnormal Inexact Rounded Clamped
remx3451 remainder 5176295309.89943746236102209837813 -129733.103628797477167908698565465 -> 74208.214046920838632934314641965
subx3451 subtract 5176295309.89943746236102209837813 -129733.103628797477167908698565465 -> 5176425043.00306625983819000707670 Inexact Rounded
addx3452 add 4471634841166.90197229286530307326E+172 31511104397.8671727003201890825879E-955 -> 4.47163484116690197229286530307326E+184 Inexact Rounded
comx3452 compare 4471634841166.90197229286530307326E+172 31511104397.8671727003201890825879E-955 -> 1
divx3452 divide 4471634841166.90197229286530307326E+172 31511104397.8671727003201890825879E-955 -> 1.41906636616314987705536737025932E+1129 Inexact Rounded
dvix3452 divideint 4471634841166.90197229286530307326E+172 31511104397.8671727003201890825879E-955 -> NaN Division_impossible
mulx3452 multiply 4471634841166.90197229286530307326E+172 31511104397.8671727003201890825879E-955 -> 1.40906152309150441010046222214810E-760 Inexact Rounded
powx3452 power 4471634841166.90197229286530307326E+172 3 -> 8.94126556389673498386397569249516E+553 Inexact Rounded
remx3452 remainder 4471634841166.90197229286530307326E+172 31511104397.8671727003201890825879E-955 -> NaN Division_impossible
subx3452 subtract 4471634841166.90197229286530307326E+172 31511104397.8671727003201890825879E-955 -> 4.47163484116690197229286530307326E+184 Inexact Rounded
addx3453 add -8189130.15945231049670285726774317 2.57912402871404325831670923864936E-366 -> -8189130.15945231049670285726774317 Inexact Rounded
comx3453 compare -8189130.15945231049670285726774317 2.57912402871404325831670923864936E-366 -> -1
divx3453 divide -8189130.15945231049670285726774317 2.57912402871404325831670923864936E-366 -> -3.17515949922556778343526099830093E+372 Inexact Rounded
dvix3453 divideint -8189130.15945231049670285726774317 2.57912402871404325831670923864936E-366 -> NaN Division_impossible
mulx3453 multiply -8189130.15945231049670285726774317 2.57912402871404325831670923864936E-366 -> -2.11207823685103185039979144161848E-359 Inexact Rounded
powx3453 power -8189130.15945231049670285726774317 3 -> -549178241054875982731.000937875885 Inexact Rounded
remx3453 remainder -8189130.15945231049670285726774317 2.57912402871404325831670923864936E-366 -> NaN Division_impossible
subx3453 subtract -8189130.15945231049670285726774317 2.57912402871404325831670923864936E-366 -> -8189130.15945231049670285726774317 Inexact Rounded
addx3454 add -254346232981353293392888785643245 -764.416902486152093758287929678445 -> -254346232981353293392888785644009 Inexact Rounded
comx3454 compare -254346232981353293392888785643245 -764.416902486152093758287929678445 -> -1
divx3454 divide -254346232981353293392888785643245 -764.416902486152093758287929678445 -> 332732350833857889204406356900.665 Inexact Rounded
dvix3454 divideint -254346232981353293392888785643245 -764.416902486152093758287929678445 -> 332732350833857889204406356900
mulx3454 multiply -254346232981353293392888785643245 -764.416902486152093758287929678445 -> 1.94426559574627262006307326336289E+35 Inexact Rounded
powx3454 power -254346232981353293392888785643245 -764 -> 0E-10031 Underflow Subnormal Inexact Rounded Clamped
remx3454 remainder -254346232981353293392888785643245 -764.416902486152093758287929678445 -> -508.299323962538610580669092979500
subx3454 subtract -254346232981353293392888785643245 -764.416902486152093758287929678445 -> -254346232981353293392888785642481 Inexact Rounded
addx3455 add -2875.36745499544177964804277329726 -13187.8492045054802205691248267638 -> -16063.2166595009220002171676000611 Inexact Rounded
comx3455 compare -2875.36745499544177964804277329726 -13187.8492045054802205691248267638 -> 1
divx3455 divide -2875.36745499544177964804277329726 -13187.8492045054802205691248267638 -> 0.218031569091122520391599541575615 Inexact Rounded
dvix3455 divideint -2875.36745499544177964804277329726 -13187.8492045054802205691248267638 -> 0
mulx3455 multiply -2875.36745499544177964804277329726 -13187.8492045054802205691248267638 -> 37919912.4040225840727281633024665 Inexact Rounded
powx3455 power -2875.36745499544177964804277329726 -13188 -> 0E-10031 Underflow Subnormal Inexact Rounded Clamped
remx3455 remainder -2875.36745499544177964804277329726 -13187.8492045054802205691248267638 -> -2875.36745499544177964804277329726
subx3455 subtract -2875.36745499544177964804277329726 -13187.8492045054802205691248267638 -> 10312.4817495100384409210820534665 Inexact Rounded
addx3456 add -7.26927570984219944693602140223103 0160883021541.32275286769110003971E-438 -> -7.26927570984219944693602140223103 Inexact Rounded
comx3456 compare -7.26927570984219944693602140223103 0160883021541.32275286769110003971E-438 -> -1
divx3456 divide -7.26927570984219944693602140223103 0160883021541.32275286769110003971E-438 -> -4.51836100553039917574557235275173E+427 Inexact Rounded
dvix3456 divideint -7.26927570984219944693602140223103 0160883021541.32275286769110003971E-438 -> NaN Division_impossible
mulx3456 multiply -7.26927570984219944693602140223103 0160883021541.32275286769110003971E-438 -> -1.16950304061635681891361504442479E-426 Inexact Rounded
powx3456 power -7.26927570984219944693602140223103 2 -> 52.8423693457018126451998096211036 Inexact Rounded
remx3456 remainder -7.26927570984219944693602140223103 0160883021541.32275286769110003971E-438 -> NaN Division_impossible
subx3456 subtract -7.26927570984219944693602140223103 0160883021541.32275286769110003971E-438 -> -7.26927570984219944693602140223103 Inexact Rounded
addx3457 add -28567151.6868762752241056540028515E+498 -4470.15455137546427645290210989675 -> -2.85671516868762752241056540028515E+505 Inexact Rounded
comx3457 compare -28567151.6868762752241056540028515E+498 -4470.15455137546427645290210989675 -> -1
divx3457 divide -28567151.6868762752241056540028515E+498 -4470.15455137546427645290210989675 -> 6.39064071690455919792707589054106E+501 Inexact Rounded
dvix3457 divideint -28567151.6868762752241056540028515E+498 -4470.15455137546427645290210989675 -> NaN Division_impossible
mulx3457 multiply -28567151.6868762752241056540028515E+498 -4470.15455137546427645290210989675 -> 1.27699583132923253605397736797000E+509 Inexact Rounded
powx3457 power -28567151.6868762752241056540028515E+498 -4470 -> 0E-10031 Underflow Subnormal Inexact Rounded Clamped
remx3457 remainder -28567151.6868762752241056540028515E+498 -4470.15455137546427645290210989675 -> NaN Division_impossible
subx3457 subtract -28567151.6868762752241056540028515E+498 -4470.15455137546427645290210989675 -> -2.85671516868762752241056540028515E+505 Inexact Rounded
addx3458 add 7191753.79974136447257468282073718 81.3878426176038487948375777384429 -> 7191835.18758398207642347765831492 Inexact Rounded
comx3458 compare 7191753.79974136447257468282073718 81.3878426176038487948375777384429 -> 1
divx3458 divide 7191753.79974136447257468282073718 81.3878426176038487948375777384429 -> 88363.9812586188186733935569874100 Inexact Rounded
dvix3458 divideint 7191753.79974136447257468282073718 81.3878426176038487948375777384429 -> 88363
mulx3458 multiply 7191753.79974136447257468282073718 81.3878426176038487948375777384429 -> 585321326.397904638863485891524555 Inexact Rounded
powx3458 power 7191753.79974136447257468282073718 81 -> 2.53290983138561482612557404148760E+555 Inexact Rounded
remx3458 remainder 7191753.79974136447257468282073718 81.3878426176038487948375777384429 -> 79.8625220355815164499390351500273
subx3458 subtract 7191753.79974136447257468282073718 81.3878426176038487948375777384429 -> 7191672.41189874686872588798315944 Inexact Rounded
addx3459 add 502975804.069864536104621700404770 684.790028432074527960269515227243 -> 502976488.859892968179149660674285 Inexact Rounded
comx3459 compare 502975804.069864536104621700404770 684.790028432074527960269515227243 -> 1
divx3459 divide 502975804.069864536104621700404770 684.790028432074527960269515227243 -> 734496.390406706323899801641278933 Inexact Rounded
dvix3459 divideint 502975804.069864536104621700404770 684.790028432074527960269515227243 -> 734496
mulx3459 multiply 502975804.069864536104621700404770 684.790028432074527960269515227243 -> 344432815169.648082754214631086270 Inexact Rounded
powx3459 power 502975804.069864536104621700404770 685 -> 3.62876716573623552761739177592677E+5960 Inexact Rounded
remx3459 remainder 502975804.069864536104621700404770 684.790028432074527960269515227243 -> 267.346619523615915582548420925472
subx3459 subtract 502975804.069864536104621700404770 684.790028432074527960269515227243 -> 502975119.279836104030093740135255 Inexact Rounded
addx3460 add 1505368.42063731861590460453659570 -465242.678439951462767630022819105 -> 1040125.74219736715313697451377660 Inexact Rounded
comx3460 compare 1505368.42063731861590460453659570 -465242.678439951462767630022819105 -> 1
divx3460 divide 1505368.42063731861590460453659570 -465242.678439951462767630022819105 -> -3.23566278503319947059213001405065 Inexact Rounded
dvix3460 divideint 1505368.42063731861590460453659570 -465242.678439951462767630022819105 -> -3
mulx3460 multiply 1505368.42063731861590460453659570 -465242.678439951462767630022819105 -> -700361636056.225618266296899048765 Inexact Rounded
powx3460 power 1505368.42063731861590460453659570 -465243 -> 0E-10031 Underflow Subnormal Inexact Rounded Clamped
remx3460 remainder 1505368.42063731861590460453659570 -465242.678439951462767630022819105 -> 109640.385317464227601714468138385
subx3460 subtract 1505368.42063731861590460453659570 -465242.678439951462767630022819105 -> 1970611.09907727007867223455941481 Inexact Rounded
addx3461 add 69225023.2850142784063416137144829 8584050.06648191798834819995325693 -> 77809073.3514961963946898136677398 Inexact Rounded
comx3461 compare 69225023.2850142784063416137144829 8584050.06648191798834819995325693 -> 1
divx3461 divide 69225023.2850142784063416137144829 8584050.06648191798834819995325693 -> 8.06437785764050431295652411163382 Inexact Rounded
dvix3461 divideint 69225023.2850142784063416137144829 8584050.06648191798834819995325693 -> 8
mulx3461 multiply 69225023.2850142784063416137144829 8584050.06648191798834819995325693 -> 594231065731939.137329770485497261 Inexact Rounded
powx3461 power 69225023.2850142784063416137144829 8584050 -> Infinity Overflow Inexact Rounded
remx3461 remainder 69225023.2850142784063416137144829 8584050.06648191798834819995325693 -> 552622.75315893449955601408842746
subx3461 subtract 69225023.2850142784063416137144829 8584050.06648191798834819995325693 -> 60640973.2185323604179934137612260 Inexact Rounded
addx3462 add -55669501853.7751006841940471339310E+614 061400538.186044693233816566977189 -> -5.56695018537751006841940471339310E+624 Inexact Rounded
comx3462 compare -55669501853.7751006841940471339310E+614 061400538.186044693233816566977189 -> -1
divx3462 divide -55669501853.7751006841940471339310E+614 061400538.186044693233816566977189 -> -9.06661464189378059067792554300676E+616 Inexact Rounded
dvix3462 divideint -55669501853.7751006841940471339310E+614 061400538.186044693233816566977189 -> NaN Division_impossible
mulx3462 multiply -55669501853.7751006841940471339310E+614 061400538.186044693233816566977189 -> -3.41813737437080390787865389703565E+632 Inexact Rounded
powx3462 power -55669501853.7751006841940471339310E+614 61400538 -> Infinity Overflow Inexact Rounded
remx3462 remainder -55669501853.7751006841940471339310E+614 061400538.186044693233816566977189 -> NaN Division_impossible
subx3462 subtract -55669501853.7751006841940471339310E+614 061400538.186044693233816566977189 -> -5.56695018537751006841940471339310E+624 Inexact Rounded
addx3463 add -527566.521273992424649346837337602E-408 -834662.599983953345718523807123972 -> -834662.599983953345718523807123972 Inexact Rounded
comx3463 compare -527566.521273992424649346837337602E-408 -834662.599983953345718523807123972 -> 1
divx3463 divide -527566.521273992424649346837337602E-408 -834662.599983953345718523807123972 -> 6.32071595497552015656909892339226E-409 Inexact Rounded
dvix3463 divideint -527566.521273992424649346837337602E-408 -834662.599983953345718523807123972 -> 0
mulx3463 multiply -527566.521273992424649346837337602E-408 -834662.599983953345718523807123972 -> 4.40340044311040151960763108019957E-397 Inexact Rounded
powx3463 power -527566.521273992424649346837337602E-408 -834663 -> -Infinity Overflow Inexact Rounded
remx3463 remainder -527566.521273992424649346837337602E-408 -834662.599983953345718523807123972 -> -5.27566521273992424649346837337602E-403
subx3463 subtract -527566.521273992424649346837337602E-408 -834662.599983953345718523807123972 -> 834662.599983953345718523807123972 Inexact Rounded
addx3464 add 69065510.0459653699418083455335366 694848643848212520086960886818157E-853 -> 69065510.0459653699418083455335366 Inexact Rounded
comx3464 compare 69065510.0459653699418083455335366 694848643848212520086960886818157E-853 -> 1
divx3464 divide 69065510.0459653699418083455335366 694848643848212520086960886818157E-853 -> 9.93964810285396646889830919492683E+827 Inexact Rounded
dvix3464 divideint 69065510.0459653699418083455335366 694848643848212520086960886818157E-853 -> NaN Division_impossible
mulx3464 multiply 69065510.0459653699418083455335366 694848643848212520086960886818157E-853 -> 4.79900759921241352562381181332720E-813 Inexact Rounded
powx3464 power 69065510.0459653699418083455335366 7 -> 7.49598249763416483824919118973567E+54 Inexact Rounded
remx3464 remainder 69065510.0459653699418083455335366 694848643848212520086960886818157E-853 -> NaN Division_impossible
subx3464 subtract 69065510.0459653699418083455335366 694848643848212520086960886818157E-853 -> 69065510.0459653699418083455335366 Inexact Rounded
addx3465 add -2921982.82411285505229122890041841 -72994.6523840298017471962569778803E-763 -> -2921982.82411285505229122890041841 Inexact Rounded
comx3465 compare -2921982.82411285505229122890041841 -72994.6523840298017471962569778803E-763 -> -1
divx3465 divide -2921982.82411285505229122890041841 -72994.6523840298017471962569778803E-763 -> 4.00300943792444663467732029501716E+764 Inexact Rounded
dvix3465 divideint -2921982.82411285505229122890041841 -72994.6523840298017471962569778803E-763 -> NaN Division_impossible
mulx3465 multiply -2921982.82411285505229122890041841 -72994.6523840298017471962569778803E-763 -> 2.13289120518223547921212412642411E-752 Inexact Rounded
powx3465 power -2921982.82411285505229122890041841 -7 -> -5.49865394870631248479668782154131E-46 Inexact Rounded
remx3465 remainder -2921982.82411285505229122890041841 -72994.6523840298017471962569778803E-763 -> NaN Division_impossible
subx3465 subtract -2921982.82411285505229122890041841 -72994.6523840298017471962569778803E-763 -> -2921982.82411285505229122890041841 Inexact Rounded
addx3466 add 4.51117459466491451401835756593793 3873385.19981811640063144338087230 -> 3873389.71099271106554595739922987 Inexact Rounded
comx3466 compare 4.51117459466491451401835756593793 3873385.19981811640063144338087230 -> -1
divx3466 divide 4.51117459466491451401835756593793 3873385.19981811640063144338087230 -> 0.00000116465942888322776753062580106351 Inexact Rounded
dvix3466 divideint 4.51117459466491451401835756593793 3873385.19981811640063144338087230 -> 0
mulx3466 multiply 4.51117459466491451401835756593793 3873385.19981811640063144338087230 -> 17473516.9087705701652062546164705 Inexact Rounded
powx3466 power 4.51117459466491451401835756593793 3873385 -> Infinity Overflow Inexact Rounded
remx3466 remainder 4.51117459466491451401835756593793 3873385.19981811640063144338087230 -> 4.51117459466491451401835756593793
subx3466 subtract 4.51117459466491451401835756593793 3873385.19981811640063144338087230 -> -3873380.68864352173571692936251473 Inexact Rounded
addx3467 add 49553763474698.8118661758811091120 36.1713861293896216593840817950781E+410 -> 3.61713861293896216593840817950781E+411 Inexact Rounded
comx3467 compare 49553763474698.8118661758811091120 36.1713861293896216593840817950781E+410 -> -1
divx3467 divide 49553763474698.8118661758811091120 36.1713861293896216593840817950781E+410 -> 1.36997137177543416190811827685231E-398 Inexact Rounded
dvix3467 divideint 49553763474698.8118661758811091120 36.1713861293896216593840817950781E+410 -> 0
mulx3467 multiply 49553763474698.8118661758811091120 36.1713861293896216593840817950781E+410 -> 1.79242831280777466554271332425735E+425 Inexact Rounded
powx3467 power 49553763474698.8118661758811091120 4 -> 6.02985091099730236635954801474802E+54 Inexact Rounded
remx3467 remainder 49553763474698.8118661758811091120 36.1713861293896216593840817950781E+410 -> 49553763474698.8118661758811091120
subx3467 subtract 49553763474698.8118661758811091120 36.1713861293896216593840817950781E+410 -> -3.61713861293896216593840817950781E+411 Inexact Rounded
addx3468 add 755985583344.379951506071499170749E+956 746921095569971477373643487285780 -> 7.55985583344379951506071499170749E+967 Inexact Rounded
comx3468 compare 755985583344.379951506071499170749E+956 746921095569971477373643487285780 -> 1
divx3468 divide 755985583344.379951506071499170749E+956 746921095569971477373643487285780 -> 1.01213580367212873025671916758669E+935 Inexact Rounded
dvix3468 divideint 755985583344.379951506071499170749E+956 746921095569971477373643487285780 -> NaN Division_impossible
mulx3468 multiply 755985583344.379951506071499170749E+956 746921095569971477373643487285780 -> 5.64661580146688255286933753616580E+1000 Inexact Rounded
powx3468 power 755985583344.379951506071499170749E+956 7 -> 1.41121958516547725677142981375469E+6775 Inexact Rounded
remx3468 remainder 755985583344.379951506071499170749E+956 746921095569971477373643487285780 -> NaN Division_impossible
subx3468 subtract 755985583344.379951506071499170749E+956 746921095569971477373643487285780 -> 7.55985583344379951506071499170749E+967 Inexact Rounded
addx3469 add -20101668541.7472260497594230105456 -395562148.345003931161532101109964 -> -20497230690.0922299809209551116556 Inexact Rounded
comx3469 compare -20101668541.7472260497594230105456 -395562148.345003931161532101109964 -> -1
divx3469 divide -20101668541.7472260497594230105456 -395562148.345003931161532101109964 -> 50.8179779735012053661447873666816 Inexact Rounded
dvix3469 divideint -20101668541.7472260497594230105456 -395562148.345003931161532101109964 -> 50
mulx3469 multiply -20101668541.7472260497594230105456 -395562148.345003931161532101109964 -> 7951459193692715079.09328760016914 Inexact Rounded
powx3469 power -20101668541.7472260497594230105456 -395562148 -> 0E-10031 Underflow Subnormal Inexact Rounded Clamped
remx3469 remainder -20101668541.7472260497594230105456 -395562148.345003931161532101109964 -> -323561124.497029491682817955047400
subx3469 subtract -20101668541.7472260497594230105456 -395562148.345003931161532101109964 -> -19706106393.4022221185978909094356 Inexact Rounded
addx3470 add 5583903.18072100716072011264668631 460868914694.088387067451312500723 -> 460874498597.269108074612032613370 Inexact Rounded
comx3470 compare 5583903.18072100716072011264668631 460868914694.088387067451312500723 -> -1
divx3470 divide 5583903.18072100716072011264668631 460868914694.088387067451312500723 -> 0.0000121160334374633240168068405467307 Inexact Rounded
dvix3470 divideint 5583903.18072100716072011264668631 460868914694.088387067451312500723 -> 0
mulx3470 multiply 5583903.18072100716072011264668631 460868914694.088387067451312500723 -> 2573447398655758659.39475672905225 Inexact Rounded
powx3470 power 5583903.18072100716072011264668631 5 -> 5.42861943589418603298670454702901E+33 Inexact Rounded
remx3470 remainder 5583903.18072100716072011264668631 460868914694.088387067451312500723 -> 5583903.18072100716072011264668631
subx3470 subtract 5583903.18072100716072011264668631 460868914694.088387067451312500723 -> -460863330790.907666060290592388076 Inexact Rounded
addx3471 add -955638397975240685017992436116257E+020 -508580.148958769104511751975720470E+662 -> -5.08580148958769104511751975720470E+667 Inexact Rounded
comx3471 compare -955638397975240685017992436116257E+020 -508580.148958769104511751975720470E+662 -> 1
divx3471 divide -955638397975240685017992436116257E+020 -508580.148958769104511751975720470E+662 -> 1.87903204624039476408191264564568E-615 Inexact Rounded
dvix3471 divideint -955638397975240685017992436116257E+020 -508580.148958769104511751975720470E+662 -> 0
mulx3471 multiply -955638397975240685017992436116257E+020 -508580.148958769104511751975720470E+662 -> 4.86018718792967378985838739820108E+720 Inexact Rounded
powx3471 power -955638397975240685017992436116257E+020 -5 -> -1.25467730420304189161768408462414E-265 Inexact Rounded
remx3471 remainder -955638397975240685017992436116257E+020 -508580.148958769104511751975720470E+662 -> -9.55638397975240685017992436116257E+52
subx3471 subtract -955638397975240685017992436116257E+020 -508580.148958769104511751975720470E+662 -> 5.08580148958769104511751975720470E+667 Inexact Rounded
addx3472 add -136243796098020983814115584402407E+796 6589108083.99750311651581032447390 -> -1.36243796098020983814115584402407E+828 Inexact Rounded
comx3472 compare -136243796098020983814115584402407E+796 6589108083.99750311651581032447390 -> -1
divx3472 divide -136243796098020983814115584402407E+796 6589108083.99750311651581032447390 -> -2.06771226638255600634939371365920E+818 Inexact Rounded
dvix3472 divideint -136243796098020983814115584402407E+796 6589108083.99750311651581032447390 -> NaN Division_impossible
mulx3472 multiply -136243796098020983814115584402407E+796 6589108083.99750311651581032447390 -> -8.97725098263977535966921696143011E+837 Inexact Rounded
powx3472 power -136243796098020983814115584402407E+796 7 -> -8.71399185551742199752832286984005E+5796 Inexact Rounded
remx3472 remainder -136243796098020983814115584402407E+796 6589108083.99750311651581032447390 -> NaN Division_impossible
subx3472 subtract -136243796098020983814115584402407E+796 6589108083.99750311651581032447390 -> -1.36243796098020983814115584402407E+828 Inexact Rounded
addx3473 add -808498.482718304598213092937543934E+521 48005.1465097914355096301483788905 -> -8.08498482718304598213092937543934E+526 Inexact Rounded
comx3473 compare -808498.482718304598213092937543934E+521 48005.1465097914355096301483788905 -> -1
divx3473 divide -808498.482718304598213092937543934E+521 48005.1465097914355096301483788905 -> -1.68419126177106468565397017107736E+522 Inexact Rounded
dvix3473 divideint -808498.482718304598213092937543934E+521 48005.1465097914355096301483788905 -> NaN Division_impossible
mulx3473 multiply -808498.482718304598213092937543934E+521 48005.1465097914355096301483788905 -> -3.88120881158362912220132691803539E+531 Inexact Rounded
powx3473 power -808498.482718304598213092937543934E+521 48005 -> -Infinity Overflow Inexact Rounded
remx3473 remainder -808498.482718304598213092937543934E+521 48005.1465097914355096301483788905 -> NaN Division_impossible
subx3473 subtract -808498.482718304598213092937543934E+521 48005.1465097914355096301483788905 -> -8.08498482718304598213092937543934E+526 Inexact Rounded
addx3474 add -812.266340554281305985524813074211E+396 -3195.63111559114001594257448970812E+986 -> -3.19563111559114001594257448970812E+989 Inexact Rounded
comx3474 compare -812.266340554281305985524813074211E+396 -3195.63111559114001594257448970812E+986 -> 1
divx3474 divide -812.266340554281305985524813074211E+396 -3195.63111559114001594257448970812E+986 -> 2.54180257724779721448484781056040E-591 Inexact Rounded
dvix3474 divideint -812.266340554281305985524813074211E+396 -3195.63111559114001594257448970812E+986 -> 0
mulx3474 multiply -812.266340554281305985524813074211E+396 -3195.63111559114001594257448970812E+986 -> 2.59570359202261082537505332325404E+1388 Inexact Rounded
powx3474 power -812.266340554281305985524813074211E+396 -3 -> -1.86596988030914616216741808216469E-1197 Inexact Rounded
remx3474 remainder -812.266340554281305985524813074211E+396 -3195.63111559114001594257448970812E+986 -> -8.12266340554281305985524813074211E+398
subx3474 subtract -812.266340554281305985524813074211E+396 -3195.63111559114001594257448970812E+986 -> 3.19563111559114001594257448970812E+989 Inexact Rounded
addx3475 add -929889.720905183397678866648217793E+134 -280300190774.057595671079264841349 -> -9.29889720905183397678866648217793E+139 Inexact Rounded
comx3475 compare -929889.720905183397678866648217793E+134 -280300190774.057595671079264841349 -> -1
divx3475 divide -929889.720905183397678866648217793E+134 -280300190774.057595671079264841349 -> 3.31747801646964399331556971055197E+128 Inexact Rounded
dvix3475 divideint -929889.720905183397678866648217793E+134 -280300190774.057595671079264841349 -> NaN Division_impossible
mulx3475 multiply -929889.720905183397678866648217793E+134 -280300190774.057595671079264841349 -> 2.60648266168558079957349074609920E+151 Inexact Rounded
powx3475 power -929889.720905183397678866648217793E+134 -3 -> -1.24367143370300189518778505830181E-420 Inexact Rounded
remx3475 remainder -929889.720905183397678866648217793E+134 -280300190774.057595671079264841349 -> NaN Division_impossible
subx3475 subtract -929889.720905183397678866648217793E+134 -280300190774.057595671079264841349 -> -9.29889720905183397678866648217793E+139 Inexact Rounded
addx3476 add 83946.0157801953636255078996185540 492670373.235391665758701500314473 -> 492754319.251171861122327008214092 Inexact Rounded
comx3476 compare 83946.0157801953636255078996185540 492670373.235391665758701500314473 -> -1
divx3476 divide 83946.0157801953636255078996185540 492670373.235391665758701500314473 -> 0.000170389819117633485695890041185782 Inexact Rounded
dvix3476 divideint 83946.0157801953636255078996185540 492670373.235391665758701500314473 -> 0
mulx3476 multiply 83946.0157801953636255078996185540 492670373.235391665758701500314473 -> 41357714926052.9282985560380064649 Inexact Rounded
powx3476 power 83946.0157801953636255078996185540 492670373 -> Infinity Overflow Inexact Rounded
remx3476 remainder 83946.0157801953636255078996185540 492670373.235391665758701500314473 -> 83946.0157801953636255078996185540
subx3476 subtract 83946.0157801953636255078996185540 492670373.235391665758701500314473 -> -492586427.219611470395075992414854 Inexact Rounded
addx3477 add 7812758113817.99135851825223122508 3037492.36716301443309571918002123E-157 -> 7812758113817.99135851825223122508 Inexact Rounded
comx3477 compare 7812758113817.99135851825223122508 3037492.36716301443309571918002123E-157 -> 1
divx3477 divide 7812758113817.99135851825223122508 3037492.36716301443309571918002123E-157 -> 2.57210790001590171809512871857381E+163 Inexact Rounded
dvix3477 divideint 7812758113817.99135851825223122508 3037492.36716301443309571918002123E-157 -> NaN Division_impossible
mulx3477 multiply 7812758113817.99135851825223122508 3037492.36716301443309571918002123E-157 -> 2.37311931372130583136091717093935E-138 Inexact Rounded
powx3477 power 7812758113817.99135851825223122508 3 -> 4.76884421816246896090414314934253E+38 Inexact Rounded
remx3477 remainder 7812758113817.99135851825223122508 3037492.36716301443309571918002123E-157 -> NaN Division_impossible
subx3477 subtract 7812758113817.99135851825223122508 3037492.36716301443309571918002123E-157 -> 7812758113817.99135851825223122508 Inexact Rounded
addx3478 add 489191747.148674326757767356626016 01136942.1182277580093027768490545 -> 490328689.266902084767070133475071 Inexact Rounded
comx3478 compare 489191747.148674326757767356626016 01136942.1182277580093027768490545 -> 1
divx3478 divide 489191747.148674326757767356626016 01136942.1182277580093027768490545 -> 430.269702657525223124148258641358 Inexact Rounded
dvix3478 divideint 489191747.148674326757767356626016 01136942.1182277580093027768490545 -> 430
mulx3478 multiply 489191747.148674326757767356626016 01136942.1182277580093027768490545 -> 556182701222751.588454129518830550 Inexact Rounded
powx3478 power 489191747.148674326757767356626016 1136942 -> Infinity Overflow Inexact Rounded
remx3478 remainder 489191747.148674326757767356626016 01136942.1182277580093027768490545 -> 306636.3107383827575733115325810
subx3478 subtract 489191747.148674326757767356626016 01136942.1182277580093027768490545 -> 488054805.030446568748464579776962 Inexact Rounded
addx3479 add -599369540.373174482335865567937853E+289 -38288383205.6749439588707955585209 -> -5.99369540373174482335865567937853E+297 Inexact Rounded
comx3479 compare -599369540.373174482335865567937853E+289 -38288383205.6749439588707955585209 -> -1
divx3479 divide -599369540.373174482335865567937853E+289 -38288383205.6749439588707955585209 -> 1.56540833065089684132688143737586E+287 Inexact Rounded
dvix3479 divideint -599369540.373174482335865567937853E+289 -38288383205.6749439588707955585209 -> NaN Division_impossible
mulx3479 multiply -599369540.373174482335865567937853E+289 -38288383205.6749439588707955585209 -> 2.29488906436173641324091638963715E+308 Inexact Rounded
powx3479 power -599369540.373174482335865567937853E+289 -4 -> 7.74856580646291366270329165540958E-1192 Inexact Rounded
remx3479 remainder -599369540.373174482335865567937853E+289 -38288383205.6749439588707955585209 -> NaN Division_impossible
subx3479 subtract -599369540.373174482335865567937853E+289 -38288383205.6749439588707955585209 -> -5.99369540373174482335865567937853E+297 Inexact Rounded
addx3480 add -3376883870.85961692148022521960139 -65247489449.7334589731171980408284 -> -68624373320.5930758945974232604298 Inexact Rounded
comx3480 compare -3376883870.85961692148022521960139 -65247489449.7334589731171980408284 -> 1
divx3480 divide -3376883870.85961692148022521960139 -65247489449.7334589731171980408284 -> 0.0517550008335747813596332404664731 Inexact Rounded
dvix3480 divideint -3376883870.85961692148022521960139 -65247489449.7334589731171980408284 -> 0
mulx3480 multiply -3376883870.85961692148022521960139 -65247489449.7334589731171980408284 -> 220333194736887939420.719579906257 Inexact Rounded
powx3480 power -3376883870.85961692148022521960139 -7 -> -1.99704163718361153125735756179280E-67 Inexact Rounded
remx3480 remainder -3376883870.85961692148022521960139 -65247489449.7334589731171980408284 -> -3376883870.85961692148022521960139
subx3480 subtract -3376883870.85961692148022521960139 -65247489449.7334589731171980408284 -> 61870605578.8738420516369728212270 Inexact Rounded
addx3481 add 58.6776780370008364590621772421025 01.5925518866529044494309229975160 -> 60.2702299236537409084931002396185
comx3481 compare 58.6776780370008364590621772421025 01.5925518866529044494309229975160 -> 1
divx3481 divide 58.6776780370008364590621772421025 01.5925518866529044494309229975160 -> 36.8450651616286048437498576613622 Inexact Rounded
dvix3481 divideint 58.6776780370008364590621772421025 01.5925518866529044494309229975160 -> 36
mulx3481 multiply 58.6776780370008364590621772421025 01.5925518866529044494309229975160 -> 93.4472468622373769590900258060029 Inexact Rounded
powx3481 power 58.6776780370008364590621772421025 2 -> 3443.06989981393033632008313505230 Inexact Rounded
remx3481 remainder 58.6776780370008364590621772421025 01.5925518866529044494309229975160 -> 1.3458101174962762795489493315265
subx3481 subtract 58.6776780370008364590621772421025 01.5925518866529044494309229975160 -> 57.0851261503479320096312542445865
addx3482 add 4099616339.96249499552808575717579 290.795187361072489816791525139895 -> 4099616630.75768235660057557396732 Inexact Rounded
comx3482 compare 4099616339.96249499552808575717579 290.795187361072489816791525139895 -> 1
divx3482 divide 4099616339.96249499552808575717579 290.795187361072489816791525139895 -> 14097951.1289920788134209002390834 Inexact Rounded
dvix3482 divideint 4099616339.96249499552808575717579 290.795187361072489816791525139895 -> 14097951
mulx3482 multiply 4099616339.96249499552808575717579 290.795187361072489816791525139895 -> 1192148701687.90798437501397900174 Inexact Rounded
powx3482 power 4099616339.96249499552808575717579 291 -> 2.03364757877800497409765979877258E+2797 Inexact Rounded
remx3482 remainder 4099616339.96249499552808575717579 290.795187361072489816791525139895 -> 37.510275726642959858538282144855
subx3482 subtract 4099616339.96249499552808575717579 290.795187361072489816791525139895 -> 4099616049.16730763445559594038426 Inexact Rounded
addx3483 add 85870777.2282833141709970713739108 -2140392861153.69401346398478113715 -> -2140306990376.46573014981378406578 Inexact Rounded
comx3483 compare 85870777.2282833141709970713739108 -2140392861153.69401346398478113715 -> 1
divx3483 divide 85870777.2282833141709970713739108 -2140392861153.69401346398478113715 -> -0.0000401191663393971853092748263233128 Inexact Rounded
dvix3483 divideint 85870777.2282833141709970713739108 -2140392861153.69401346398478113715 -> -0
mulx3483 multiply 85870777.2282833141709970713739108 -2140392861153.69401346398478113715 -> -183797198561136797328.508878254848 Inexact Rounded
powx3483 power 85870777.2282833141709970713739108 -2 -> 1.35615463448707573424578785973269E-16 Inexact Rounded
remx3483 remainder 85870777.2282833141709970713739108 -2140392861153.69401346398478113715 -> 85870777.2282833141709970713739108
subx3483 subtract 85870777.2282833141709970713739108 -2140392861153.69401346398478113715 -> 2140478731930.92229677815577820852 Inexact Rounded
addx3484 add 20900.9693761555165742010339929779 -38.7546147649523793463260940289585 -> 20862.2147613905641948547078989489 Inexact Rounded
comx3484 compare 20900.9693761555165742010339929779 -38.7546147649523793463260940289585 -> 1
divx3484 divide 20900.9693761555165742010339929779 -38.7546147649523793463260940289585 -> -539.315627388386430188627412639767 Inexact Rounded
dvix3484 divideint 20900.9693761555165742010339929779 -38.7546147649523793463260940289585 -> -539
mulx3484 multiply 20900.9693761555165742010339929779 -38.7546147649523793463260940289585 -> -810009.016386974103738622793670566 Inexact Rounded
powx3484 power 20900.9693761555165742010339929779 -39 -> 3.26219014701526335296044439989665E-169 Inexact Rounded
remx3484 remainder 20900.9693761555165742010339929779 -38.7546147649523793463260940289585 -> 12.2320178461841065312693113692685
subx3484 subtract 20900.9693761555165742010339929779 -38.7546147649523793463260940289585 -> 20939.7239909204689535473600870069 Inexact Rounded
addx3485 add 448.827596155587910947511170319456 379130153.382794042652974596286062 -> 379130602.210390198240885543797232 Inexact Rounded
comx3485 compare 448.827596155587910947511170319456 379130153.382794042652974596286062 -> -1
divx3485 divide 448.827596155587910947511170319456 379130153.382794042652974596286062 -> 0.00000118383513458615061394140895596979 Inexact Rounded
dvix3485 divideint 448.827596155587910947511170319456 379130153.382794042652974596286062 -> 0
mulx3485 multiply 448.827596155587910947511170319456 379130153.382794042652974596286062 -> 170164075372.898786469094460692097 Inexact Rounded
powx3485 power 448.827596155587910947511170319456 379130153 -> Infinity Overflow Inexact Rounded
remx3485 remainder 448.827596155587910947511170319456 379130153.382794042652974596286062 -> 448.827596155587910947511170319456
subx3485 subtract 448.827596155587910947511170319456 379130153.382794042652974596286062 -> -379129704.555197887065063648774892 Inexact Rounded
addx3486 add 98.4134807921002817357000140482039 3404725543.77032945444654351546779 -> 3404725642.18381024654682525116780 Inexact Rounded
comx3486 compare 98.4134807921002817357000140482039 3404725543.77032945444654351546779 -> -1
divx3486 divide 98.4134807921002817357000140482039 3404725543.77032945444654351546779 -> 2.89049673833970863420201979291523E-8 Inexact Rounded
dvix3486 divideint 98.4134807921002817357000140482039 3404725543.77032945444654351546779 -> 0
mulx3486 multiply 98.4134807921002817357000140482039 3404725543.77032945444654351546779 -> 335070891904.214504811798212040413 Inexact Rounded
powx3486 power 98.4134807921002817357000140482039 3 -> 953155.543384739667965055839894682 Inexact Rounded
remx3486 remainder 98.4134807921002817357000140482039 3404725543.77032945444654351546779 -> 98.4134807921002817357000140482039
subx3486 subtract 98.4134807921002817357000140482039 3404725543.77032945444654351546779 -> -3404725445.35684866234626177976778 Inexact Rounded
addx3487 add 545746433.649359734136476718176330E-787 -5149957099709.12830072802043560650E-437 -> -5.14995709970912830072802043560650E-425 Inexact Rounded
comx3487 compare 545746433.649359734136476718176330E-787 -5149957099709.12830072802043560650E-437 -> 1
divx3487 divide 545746433.649359734136476718176330E-787 -5149957099709.12830072802043560650E-437 -> -1.05971064046375011086850722752614E-354 Inexact Rounded
dvix3487 divideint 545746433.649359734136476718176330E-787 -5149957099709.12830072802043560650E-437 -> -0
mulx3487 multiply 545746433.649359734136476718176330E-787 -5149957099709.12830072802043560650E-437 -> -2.81057072061345688074304873033317E-1203 Inexact Rounded
powx3487 power 545746433.649359734136476718176330E-787 -5 -> 2.06559640092667166976186801348662E+3891 Inexact Rounded
remx3487 remainder 545746433.649359734136476718176330E-787 -5149957099709.12830072802043560650E-437 -> 5.45746433649359734136476718176330E-779
subx3487 subtract 545746433.649359734136476718176330E-787 -5149957099709.12830072802043560650E-437 -> 5.14995709970912830072802043560650E-425 Inexact Rounded
addx3488 add 741304513547.273820525801608231737 0396.22823128272584928019323186355E-830 -> 741304513547.273820525801608231737 Inexact Rounded
comx3488 compare 741304513547.273820525801608231737 0396.22823128272584928019323186355E-830 -> 1
divx3488 divide 741304513547.273820525801608231737 0396.22823128272584928019323186355E-830 -> 1.87090281565101612623398174727653E+839 Inexact Rounded
dvix3488 divideint 741304513547.273820525801608231737 0396.22823128272584928019323186355E-830 -> NaN Division_impossible
mulx3488 multiply 741304513547.273820525801608231737 0396.22823128272584928019323186355E-830 -> 2.93725776244737788947443361076095E-816 Inexact Rounded
powx3488 power 741304513547.273820525801608231737 4 -> 3.01985838652892073903194846668712E+47 Inexact Rounded
remx3488 remainder 741304513547.273820525801608231737 0396.22823128272584928019323186355E-830 -> NaN Division_impossible
subx3488 subtract 741304513547.273820525801608231737 0396.22823128272584928019323186355E-830 -> 741304513547.273820525801608231737 Inexact Rounded
addx3489 add -706.145005094292315613907254240553 4739.82486195739758308735946332234 -> 4033.67985686310526747345220908179 Inexact Rounded
comx3489 compare -706.145005094292315613907254240553 4739.82486195739758308735946332234 -> -1
divx3489 divide -706.145005094292315613907254240553 4739.82486195739758308735946332234 -> -0.148981244172527671907534117771626 Inexact Rounded
dvix3489 divideint -706.145005094292315613907254240553 4739.82486195739758308735946332234 -> -0
mulx3489 multiply -706.145005094292315613907254240553 4739.82486195739758308735946332234 -> -3347003.65129295988793454267973464 Inexact Rounded
powx3489 power -706.145005094292315613907254240553 4740 -> Infinity Overflow Inexact Rounded
remx3489 remainder -706.145005094292315613907254240553 4739.82486195739758308735946332234 -> -706.145005094292315613907254240553
subx3489 subtract -706.145005094292315613907254240553 4739.82486195739758308735946332234 -> -5445.96986705168989870126671756289 Inexact Rounded
addx3490 add -769925786.823099083228795187975893 -31201.9980469760239870067820594790 -> -769956988.821146059252782194757952 Inexact Rounded
comx3490 compare -769925786.823099083228795187975893 -31201.9980469760239870067820594790 -> -1
divx3490 divide -769925786.823099083228795187975893 -31201.9980469760239870067820594790 -> 24675.5283319978698932292028650803 Inexact Rounded
dvix3490 divideint -769925786.823099083228795187975893 -31201.9980469760239870067820594790 -> 24675
mulx3490 multiply -769925786.823099083228795187975893 -31201.9980469760239870067820594790 -> 24023222896770.8161787236737395477 Inexact Rounded
powx3490 power -769925786.823099083228795187975893 -31202 -> 0E-10031 Underflow Subnormal Inexact Rounded Clamped
remx3490 remainder -769925786.823099083228795187975893 -31201.9980469760239870067820594790 -> -16485.0139656913494028406582486750
subx3490 subtract -769925786.823099083228795187975893 -31201.9980469760239870067820594790 -> -769894584.825052107204808181193834 Inexact Rounded
addx3491 add 84438610546049.7256507419289692857E+906 052604240766736461898844111790311 -> 8.44386105460497256507419289692857E+919 Inexact Rounded
comx3491 compare 84438610546049.7256507419289692857E+906 052604240766736461898844111790311 -> 1
divx3491 divide 84438610546049.7256507419289692857E+906 052604240766736461898844111790311 -> 1.60516736512701978695559003341922E+888 Inexact Rounded
dvix3491 divideint 84438610546049.7256507419289692857E+906 052604240766736461898844111790311 -> NaN Division_impossible
mulx3491 multiply 84438610546049.7256507419289692857E+906 052604240766736461898844111790311 -> 4.44182899917309231779837668210610E+951 Inexact Rounded
powx3491 power 84438610546049.7256507419289692857E+906 5 -> 4.29245144719689283247342866988213E+4599 Inexact Rounded
remx3491 remainder 84438610546049.7256507419289692857E+906 052604240766736461898844111790311 -> NaN Division_impossible
subx3491 subtract 84438610546049.7256507419289692857E+906 052604240766736461898844111790311 -> 8.44386105460497256507419289692857E+919 Inexact Rounded
addx3492 add 549760.911304725795164589619286514 165.160089615604924207754883953484 -> 549926.071394341400088797374170467 Inexact Rounded
comx3492 compare 549760.911304725795164589619286514 165.160089615604924207754883953484 -> 1
divx3492 divide 549760.911304725795164589619286514 165.160089615604924207754883953484 -> 3328.65471667062107598395714348089 Inexact Rounded
dvix3492 divideint 549760.911304725795164589619286514 165.160089615604924207754883953484 -> 3328
mulx3492 multiply 549760.911304725795164589619286514 165.160089615604924207754883953484 -> 90798561.3782451425861113694732484 Inexact Rounded
powx3492 power 549760.911304725795164589619286514 165 -> 1.34488925442386544028875603347654E+947 Inexact Rounded
remx3492 remainder 549760.911304725795164589619286514 165.160089615604924207754883953484 -> 108.133063992607401181365489319248
subx3492 subtract 549760.911304725795164589619286514 165.160089615604924207754883953484 -> 549595.751215110190240381864402561 Inexact Rounded
addx3493 add 3650514.18649737956855828939662794 08086721.4036886948248274834735629 -> 11737235.5901860743933857728701908 Inexact Rounded
comx3493 compare 3650514.18649737956855828939662794 08086721.4036886948248274834735629 -> -1
divx3493 divide 3650514.18649737956855828939662794 08086721.4036886948248274834735629 -> 0.451420792712387250865423208234291 Inexact Rounded
dvix3493 divideint 3650514.18649737956855828939662794 08086721.4036886948248274834735629 -> 0
mulx3493 multiply 3650514.18649737956855828939662794 08086721.4036886948248274834735629 -> 29520691206417.5831886752808745421 Inexact Rounded
powx3493 power 3650514.18649737956855828939662794 8086721 -> Infinity Overflow Inexact Rounded
remx3493 remainder 3650514.18649737956855828939662794 08086721.4036886948248274834735629 -> 3650514.18649737956855828939662794
subx3493 subtract 3650514.18649737956855828939662794 08086721.4036886948248274834735629 -> -4436207.21719131525626919407693496
addx3494 add 55067723881950.1346958179604099594 -8.90481481687182931431054785192083 -> 55067723881941.2298810010885806451 Inexact Rounded
comx3494 compare 55067723881950.1346958179604099594 -8.90481481687182931431054785192083 -> 1
divx3494 divide 55067723881950.1346958179604099594 -8.90481481687182931431054785192083 -> -6184039198391.19853088419484117054 Inexact Rounded
dvix3494 divideint 55067723881950.1346958179604099594 -8.90481481687182931431054785192083 -> -6184039198391
mulx3494 multiply 55067723881950.1346958179604099594 -8.90481481687182931431054785192083 -> -490367883555396.250365158593373279 Inexact Rounded
powx3494 power 55067723881950.1346958179604099594 -9 -> 2.14746386538529270173788457887121E-124 Inexact Rounded
remx3494 remainder 55067723881950.1346958179604099594 -8.90481481687182931431054785192083 -> 1.76788075918488693086347720461547
subx3494 subtract 55067723881950.1346958179604099594 -8.90481481687182931431054785192083 -> 55067723881959.0395106348322392737 Inexact Rounded
addx3495 add 868251123.413992653362860637541060E+019 5579665045.37858308541154858567656E+131 -> 5.57966504537858308541154858567656E+140 Inexact Rounded
comx3495 compare 868251123.413992653362860637541060E+019 5579665045.37858308541154858567656E+131 -> -1
divx3495 divide 868251123.413992653362860637541060E+019 5579665045.37858308541154858567656E+131 -> 1.55609900657590706155251902725027E-113 Inexact Rounded
dvix3495 divideint 868251123.413992653362860637541060E+019 5579665045.37858308541154858567656E+131 -> 0
mulx3495 multiply 868251123.413992653362860637541060E+019 5579665045.37858308541154858567656E+131 -> 4.84455044392374106106966779322483E+168 Inexact Rounded
powx3495 power 868251123.413992653362860637541060E+019 6 -> 4.28422354304291884802690733853227E+167 Inexact Rounded
remx3495 remainder 868251123.413992653362860637541060E+019 5579665045.37858308541154858567656E+131 -> 8682511234139926533628606375.41060
subx3495 subtract 868251123.413992653362860637541060E+019 5579665045.37858308541154858567656E+131 -> -5.57966504537858308541154858567656E+140 Inexact Rounded
addx3496 add -646.464431574014407536004990059069 -798.679560020414523841321724649594E-037 -> -646.464431574014407536004990059069 Inexact Rounded
comx3496 compare -646.464431574014407536004990059069 -798.679560020414523841321724649594E-037 -> -1
divx3496 divide -646.464431574014407536004990059069 -798.679560020414523841321724649594E-037 -> 8.09416521887063886613527228353543E+36 Inexact Rounded
dvix3496 divideint -646.464431574014407536004990059069 -798.679560020414523841321724649594E-037 -> NaN Division_impossible
mulx3496 multiply -646.464431574014407536004990059069 -798.679560020414523841321724649594E-037 -> 5.16317927778381197995451363439626E-32 Inexact Rounded
powx3496 power -646.464431574014407536004990059069 -8 -> 3.27825641569860861774700548035691E-23 Inexact Rounded
remx3496 remainder -646.464431574014407536004990059069 -798.679560020414523841321724649594E-037 -> NaN Division_impossible
subx3496 subtract -646.464431574014407536004990059069 -798.679560020414523841321724649594E-037 -> -646.464431574014407536004990059069 Inexact Rounded
addx3497 add 354.546679975219753598558273421556 -7039.46386812239015144581761752927E-448 -> 354.546679975219753598558273421556 Inexact Rounded
comx3497 compare 354.546679975219753598558273421556 -7039.46386812239015144581761752927E-448 -> 1
divx3497 divide 354.546679975219753598558273421556 -7039.46386812239015144581761752927E-448 -> -5.03655799102477192579414523352028E+446 Inexact Rounded
dvix3497 divideint 354.546679975219753598558273421556 -7039.46386812239015144581761752927E-448 -> NaN Division_impossible
mulx3497 multiply 354.546679975219753598558273421556 -7039.46386812239015144581761752927E-448 -> -2.49581854324831161267369292071408E-442 Inexact Rounded
powx3497 power 354.546679975219753598558273421556 -7 -> 1.41999246365875617298270414304233E-18 Inexact Rounded
remx3497 remainder 354.546679975219753598558273421556 -7039.46386812239015144581761752927E-448 -> NaN Division_impossible
subx3497 subtract 354.546679975219753598558273421556 -7039.46386812239015144581761752927E-448 -> 354.546679975219753598558273421556 Inexact Rounded
addx3498 add 91936087917435.5974889495278215874 -67080823344.8903392584327136082486E-757 -> 91936087917435.5974889495278215874 Inexact Rounded
comx3498 compare 91936087917435.5974889495278215874 -67080823344.8903392584327136082486E-757 -> 1
divx3498 divide 91936087917435.5974889495278215874 -67080823344.8903392584327136082486E-757 -> -1.37052712434303366569304688993783E+760 Inexact Rounded
dvix3498 divideint 91936087917435.5974889495278215874 -67080823344.8903392584327136082486E-757 -> NaN Division_impossible
mulx3498 multiply 91936087917435.5974889495278215874 -67080823344.8903392584327136082486E-757 -> -6.16714847260980448099292763939423E-733 Inexact Rounded
powx3498 power 91936087917435.5974889495278215874 -7 -> 1.80134899939035708719659065082630E-98 Inexact Rounded
remx3498 remainder 91936087917435.5974889495278215874 -67080823344.8903392584327136082486E-757 -> NaN Division_impossible
subx3498 subtract 91936087917435.5974889495278215874 -67080823344.8903392584327136082486E-757 -> 91936087917435.5974889495278215874 Inexact Rounded
addx3499 add -07345.6422518528556136521417259811E-600 41188325.7041362608934957584583381E-763 -> -7.34564225185285561365214172598110E-597 Inexact Rounded
comx3499 compare -07345.6422518528556136521417259811E-600 41188325.7041362608934957584583381E-763 -> -1
divx3499 divide -07345.6422518528556136521417259811E-600 41188325.7041362608934957584583381E-763 -> -1.78342822299163842247184303878022E+159 Inexact Rounded
dvix3499 divideint -07345.6422518528556136521417259811E-600 41188325.7041362608934957584583381E-763 -> NaN Division_impossible
mulx3499 multiply -07345.6422518528556136521417259811E-600 41188325.7041362608934957584583381E-763 -> -3.02554705575380338274126867655676E-1352 Inexact Rounded
powx3499 power -07345.6422518528556136521417259811E-600 4 -> 2.91151541552217582082937236255996E-2385 Inexact Rounded
remx3499 remainder -07345.6422518528556136521417259811E-600 41188325.7041362608934957584583381E-763 -> NaN Division_impossible
subx3499 subtract -07345.6422518528556136521417259811E-600 41188325.7041362608934957584583381E-763 -> -7.34564225185285561365214172598110E-597 Inexact Rounded
addx3500 add -253280724.939458021588167965038184 616988.426425908872398170896375634E+396 -> 6.16988426425908872398170896375634E+401 Inexact Rounded
comx3500 compare -253280724.939458021588167965038184 616988.426425908872398170896375634E+396 -> -1
divx3500 divide -253280724.939458021588167965038184 616988.426425908872398170896375634E+396 -> -4.10511306357337753351655511866170E-394 Inexact Rounded
dvix3500 divideint -253280724.939458021588167965038184 616988.426425908872398170896375634E+396 -> -0
mulx3500 multiply -253280724.939458021588167965038184 616988.426425908872398170896375634E+396 -> -1.56271275924409657991913620522315E+410 Inexact Rounded
powx3500 power -253280724.939458021588167965038184 6 -> 2.64005420221406808782284459794424E+50 Inexact Rounded
remx3500 remainder -253280724.939458021588167965038184 616988.426425908872398170896375634E+396 -> -253280724.939458021588167965038184
subx3500 subtract -253280724.939458021588167965038184 616988.426425908872398170896375634E+396 -> -6.16988426425908872398170896375634E+401 Inexact Rounded
