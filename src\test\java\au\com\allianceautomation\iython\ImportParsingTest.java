package au.com.allianceautomation.iython;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

import au.com.allianceautomation.iython.ast.Program;
import au.com.allianceautomation.iython.ast.statements.ImportStatement;
import au.com.allianceautomation.iython.ast.statements.Statement;

/**
 * Test class for import statement parsing.
 */
public class ImportParsingTest {
    
    @Test
    public void testImportStatementParsing() {
        // Test that import statements are parsed correctly
        PythonExecutor executor = new PythonExecutor();
        
        try {
            // Use reflection to access the parseCode method
            java.lang.reflect.Method parseMethod = PythonExecutor.class.getDeclaredMethod("parseCode", String.class);
            parseMethod.setAccessible(true);
            
            Program program = (Program) parseMethod.invoke(executor, "import djdf");
            
            assertNotNull(program, "Program should not be null");
            assertTrue(program.getStatements().size() > 0, "Program should have statements");
            
            Statement firstStatement = program.getStatements().get(0);
            System.out.println("First statement type: " + firstStatement.getClass().getName());
            
            if (firstStatement instanceof ImportStatement) {
                ImportStatement importStmt = (ImportStatement) firstStatement;
                System.out.println("Import statement module names: " + importStmt.getModuleNames());
                assertEquals("djdf", importStmt.getModuleNames().get(0));
            } else {
                System.out.println("Statement is not an ImportStatement, it's: " + firstStatement.getClass().getSimpleName());
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Failed to test import parsing", e);
        }
    }
}
