package au.com.allianceautomation.iython;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import org.junit.jupiter.api.Test;

/**
 * Test class for import execution functionality.
 * Tests that imported modules are actually executed and their symbols are available.
 */
public class ImportExecutionTest {
    
    @Test
    public void testImportModuleAndCallFunction() {
        // Test that we can import a module and call its functions
        PythonExecutor executor = new PythonExecutor();
        
        try {
            // First, import the test module
            String importOutput = executor.executeCode("import test_module");
            System.out.println("Import output: " + importOutput);
            
            // Try to call a function from the imported module
            String result = executor.executeCode("result = test_module.hello()");
            System.out.println("Function call output: " + result);

            // Get the result variable
            Object resultValue = executor.executeAndGetVariable("result = test_module.hello()", "result");
            System.out.println("Result value: " + resultValue);

            assertNotNull(resultValue, "Function call should return a value");
            assertEquals("Hello from test_module!", resultValue, "Function should return correct message");
            
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("Exception: " + e.getMessage());
            if (e.getCause() != null) {
                System.out.println("Cause: " + e.getCause().getMessage());
            }

            // If we get here, the test failed
            throw new RuntimeException("Import and function call should work now", e);
        }
    }
    
    @Test
    public void testImportModuleAndAccessVariable() {
        // Test that we can import a module and access its variables
        PythonExecutor executor = new PythonExecutor();
        
        try {
            // Import the test module
            executor.executeCode("import test_module");
            
            // Try to access a module variable
            String result = executor.executeCode("version = test_module.MODULE_VERSION");
            System.out.println("Variable access output: " + result);

            // This should work if module execution is properly implemented
            Object version = executor.executeAndGetVariable("version = test_module.MODULE_VERSION", "version");
            assertEquals("1.0.0", version);
            
        } catch (Exception e) {
            System.out.println("Exception accessing module variable: " + e.getMessage());

            // If we get here, the test failed
            throw new RuntimeException("Import and variable access should work now", e);
        }
    }
    
    @Test
    public void testFromImport() {
        // Test "from module import function" syntax
        PythonExecutor executor = new PythonExecutor();
        
        try {
            // Import specific function from the module
            executor.executeCode("from test_module import hello");
            
            // Try to call the imported function directly
            String result = executor.executeCode("result = hello()");
            System.out.println("From import result: " + result);

            Object resultValue = executor.executeAndGetVariable("result = hello()", "result");
            assertEquals("Hello from test_module!", resultValue);
            
        } catch (Exception e) {
            System.out.println("Exception with from import: " + e.getMessage());

            // If we get here, the test failed
            throw new RuntimeException("From import should work now", e);
        }
    }
}
