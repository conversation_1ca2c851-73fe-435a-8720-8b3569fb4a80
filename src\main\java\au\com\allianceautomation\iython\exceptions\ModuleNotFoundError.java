package au.com.allianceautomation.iython.exceptions;

import au.com.allianceautomation.iython.runtime.exceptions.PythonRuntimeException;
import au.com.allianceautomation.iython.runtime.traceback.PythonTraceback;

/**
 * Exception thrown when a module cannot be found during import.
 * This corresponds to Python's ModuleNotFoundError.
 *
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class ModuleNotFoundError extends PythonRuntimeException {
    
    private final String moduleName;
    
    /**
     * Create a new ModuleNotFoundError.
     *
     * @param moduleName The name of the module that could not be found
     */
    public ModuleNotFoundError(String moduleName) {
        super("ModuleNotFoundError", "No module named '" + moduleName + "'", new PythonTraceback());
        this.moduleName = moduleName;
    }

    /**
     * Create a new ModuleNotFoundError with a custom message.
     *
     * @param moduleName The name of the module that could not be found
     * @param message The error message
     */
    public ModuleNotFoundError(String moduleName, String message) {
        super("ModuleNotFoundError", message, new PythonTraceback());
        this.moduleName = moduleName;
    }
    
    /**
     * Get the name of the module that could not be found.
     * 
     * @return The module name
     */
    public String getModuleName() {
        return moduleName;
    }
    
    @Override
    public String getPythonExceptionType() {
        return "ModuleNotFoundError";
    }
}
