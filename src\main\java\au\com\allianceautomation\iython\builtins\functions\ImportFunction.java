package au.com.allianceautomation.iython.builtins.functions;

import java.util.List;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import au.com.allianceautomation.iython.modules.ModuleLoader;
import au.com.allianceautomation.iython.modules.PythonModule;

/**
 * Python __import__ builtin function.
 *
 * This function is invoked by the import statement. It can be replaced
 * (by importing the builtins module and assigning to builtins.__import__)
 * in order to change semantics of the import statement.
 *
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class ImportFunction extends AbstractBuiltinFunction {

    private final ModuleLoader moduleLoader;

    public ImportFunction() {
        super("__import__", 1, 5, "__import__(name, globals=None, locals=None, fromlist=(), level=0) -> module");
        this.moduleLoader = new ModuleLoader();
    }
    
    @Override
    protected Object execute(List<Object> args) {
        if (args.isEmpty()) {
            throw new RuntimeException("__import__ requires at least 1 argument");
        }

        String moduleName = pythonStr(args.get(0));

        try {
            // Use the ModuleLoader to load the module
            PythonModule module = moduleLoader.loadModule(moduleName);
            return module;
        } catch (Exception e) {
            // This will throw the proper ModuleNotFoundError
            throw new RuntimeException(e.getMessage());
        }
    }
}
