package au.com.allianceautomation.iython;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;

/**
 * Integration test for import functionality.
 */
public class ImportIntegrationTest {
    
    @Test
    public void testImportNonExistentModuleThrowsError() {
        // Test that importing a non-existent module throws the correct error
        PythonExecutor executor = new PythonExecutor();

        Exception exception = assertThrows(PythonExecutionException.class, () -> {
            executor.executeCode("import djdf");
        });

        // The PythonExecutor should wrap ModuleNotFoundError and put the formatted message in the main message
        String message = exception.getMessage();

        // Verify the error message contains "ModuleNotFoundError" and the module name
        assertTrue(message.contains("ModuleNotFoundError"),
                   "Error message should contain 'ModuleNotFoundError', but was: " + message);
        assertTrue(message.contains("djdf"),
                   "Error message should contain module name 'djdf', but was: " + message);
    }
    
    @Test
    public void testImportStatementParsing() {
        // Test that import statements are parsed correctly without throwing syntax errors
        PythonExecutor executor = new PythonExecutor();

        // This should parse correctly but throw a runtime error
        Exception exception = assertThrows(PythonExecutionException.class, () -> {
            executor.executeCode("import nonexistent_module");
        });

        // Verify it's a ModuleNotFoundError, not a syntax error
        String message = exception.getMessage();
        assertTrue(message.contains("ModuleNotFoundError"),
                   "Should be ModuleNotFoundError, not syntax error. Message: " + message);
        assertTrue(message.contains("nonexistent_module"),
                   "Error message should contain module name 'nonexistent_module', but was: " + message);
    }
}
