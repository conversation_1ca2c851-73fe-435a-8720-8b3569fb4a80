package au.com.allianceautomation.iython.ast.statements;

import java.util.List;

import au.com.allianceautomation.iython.ast.ASTVisitor;

public class ImportStatement extends Statement {
    private final List<String> moduleNames;
    private final List<String> aliases;
    private final boolean isFromImport;
    private final String fromModule;

    public ImportStatement(int line, int column, List<String> moduleNames, List<String> aliases) {
        super(line, column);
        this.moduleNames = moduleNames;
        this.aliases = aliases;
        this.isFromImport = false;
        this.fromModule = null;
    }

    public ImportStatement(int line, int column, String fromModule, List<String> moduleNames, List<String> aliases) {
        super(line, column);
        this.moduleNames = moduleNames;
        this.aliases = aliases;
        this.isFromImport = true;
        this.fromModule = fromModule;
    }

    public List<String> getModuleNames() {
        return moduleNames;
    }

    public List<String> getAliases() {
        return aliases;
    }

    public boolean isFromImport() {
        return isFromImport;
    }

    public String getFromModule() {
        return fromModule;
    }

    @Override
    public <T> T accept(ASTVisitor<T> visitor) {
        return visitor.visitImport(this);
    }
}
